'use client'

import * as React from 'react'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Database, 
  RefreshCw, 
  HardDrive, 
  Activity, 
  Users, 
  Building2, 
  FileText, 
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Download,
  Upload,
  Trash2,
  Settings,
  BarChart3,
  PieChart,
  Server
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface DatabaseStats {
  totalSize: number
  totalDocuments: number
  totalCollections: number
  indexSize: number
  avgObjSize: number
  storageSize: number
  dataSize: number
}

interface CollectionInfo {
  name: string
  documentCount: number
  avgDocSize: number
  totalSize: number
  indexCount: number
  indexSize: number
}

interface ConnectionInfo {
  host: string
  port: number
  database: string
  isConnected: boolean
  connectionTime: number
  version: string
  uptime: number
}

interface QueryPerformance {
  operation: string
  collection: string
  avgExecutionTime: number
  totalExecutions: number
  slowQueries: number
  lastExecuted: string
}

const mockDatabaseStats: DatabaseStats = {
  totalSize: 2.5 * 1024 * 1024 * 1024, // 2.5 GB
  totalDocuments: 125000,
  totalCollections: 12,
  indexSize: 150 * 1024 * 1024, // 150 MB
  avgObjSize: 2048, // 2 KB
  storageSize: 2.8 * 1024 * 1024 * 1024, // 2.8 GB
  dataSize: 2.3 * 1024 * 1024 * 1024 // 2.3 GB
}

const mockCollections: CollectionInfo[] = [
  {
    name: 'users',
    documentCount: 15000,
    avgDocSize: 1024,
    totalSize: 15 * 1024 * 1024,
    indexCount: 5,
    indexSize: 2 * 1024 * 1024
  },
  {
    name: 'projects',
    documentCount: 2500,
    avgDocSize: 4096,
    totalSize: 10 * 1024 * 1024,
    indexCount: 8,
    indexSize: 1.5 * 1024 * 1024
  },
  {
    name: 'testCases',
    documentCount: 45000,
    avgDocSize: 2048,
    totalSize: 90 * 1024 * 1024,
    indexCount: 6,
    indexSize: 5 * 1024 * 1024
  },
  {
    name: 'testExecutions',
    documentCount: 180000,
    avgDocSize: 1536,
    totalSize: 270 * 1024 * 1024,
    indexCount: 4,
    indexSize: 8 * 1024 * 1024
  },
  {
    name: 'tenants',
    documentCount: 150,
    avgDocSize: 2048,
    totalSize: 300 * 1024,
    indexCount: 3,
    indexSize: 50 * 1024
  },
  {
    name: 'logs',
    documentCount: 500000,
    avgDocSize: 512,
    totalSize: 250 * 1024 * 1024,
    indexCount: 3,
    indexSize: 10 * 1024 * 1024
  }
]

const mockConnectionInfo: ConnectionInfo = {
  host: 'localhost',
  port: 27017,
  database: 'qak_production',
  isConnected: true,
  connectionTime: 45,
  version: '7.0.4',
  uptime: 2592000 // 30 days in seconds
}

const mockQueryPerformance: QueryPerformance[] = [
  {
    operation: 'find',
    collection: 'testExecutions',
    avgExecutionTime: 125,
    totalExecutions: 15000,
    slowQueries: 23,
    lastExecuted: '2024-01-15T10:30:00Z'
  },
  {
    operation: 'aggregate',
    collection: 'users',
    avgExecutionTime: 89,
    totalExecutions: 8500,
    slowQueries: 12,
    lastExecuted: '2024-01-15T10:25:00Z'
  },
  {
    operation: 'insert',
    collection: 'testCases',
    avgExecutionTime: 45,
    totalExecutions: 25000,
    slowQueries: 5,
    lastExecuted: '2024-01-15T10:28:00Z'
  },
  {
    operation: 'update',
    collection: 'projects',
    avgExecutionTime: 67,
    totalExecutions: 12000,
    slowQueries: 8,
    lastExecuted: '2024-01-15T10:20:00Z'
  }
]

export default function DatabasePage() {
  const { toast } = useToast()
  const [stats, setStats] = useState<DatabaseStats>(mockDatabaseStats)
  const [collections, setCollections] = useState<CollectionInfo[]>(mockCollections)
  const [connectionInfo, setConnectionInfo] = useState<ConnectionInfo>(mockConnectionInfo)
  const [queryPerformance, setQueryPerformance] = useState<QueryPerformance[]>(mockQueryPerformance)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedCollection, setSelectedCollection] = useState<string>('')

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}d ${hours}h ${minutes}m`
  }

  const handleRefreshStats = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement API call to fetch database stats
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Estadísticas actualizadas",
        description: "Los datos de la base de datos han sido actualizados.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudieron cargar las estadísticas. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleOptimizeCollection = async (collectionName: string) => {
    setIsLoading(true)
    try {
      // TODO: Implement collection optimization
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      toast({
        title: "Colección optimizada",
        description: `La colección ${collectionName} ha sido optimizada correctamente.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo optimizar la colección. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackupDatabase = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement database backup
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      toast({
        title: "Backup completado",
        description: "La copia de seguridad de la base de datos ha sido creada.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo crear el backup. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStorageUsagePercentage = () => {
    return Math.round((stats.dataSize / stats.storageSize) * 100)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gestión de Base de Datos</h1>
          <p className="text-muted-foreground mt-2">
            Monitorea y administra la base de datos MongoDB de QAK
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleRefreshStats} disabled={isLoading} variant="outline">
            {isLoading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Actualizar
          </Button>
          <Button onClick={handleBackupDatabase} disabled={isLoading}>
            <Download className="h-4 w-4 mr-2" />
            Crear Backup
          </Button>
        </div>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Estado de Conexión
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Estado</Label>
              <div className="flex items-center gap-2">
                {connectionInfo.isConnected ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-accent" />
                    <span className="text-accent font-medium">Conectado</span>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-4 w-4 text-destructive" />
                    <span className="text-destructive font-medium">Desconectado</span>
                  </>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Servidor</Label>
              <p className="text-sm font-mono">{connectionInfo.host}:{connectionInfo.port}</p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Base de Datos</Label>
              <p className="text-sm font-mono">{connectionInfo.database}</p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Versión MongoDB</Label>
              <Badge variant="outline">{connectionInfo.version}</Badge>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Tiempo de Conexión</Label>
              <p className="text-sm">{connectionInfo.connectionTime}ms</p>
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium">Uptime</Label>
              <p className="text-sm">{formatUptime(connectionInfo.uptime)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Resumen
          </TabsTrigger>
          <TabsTrigger value="collections" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Colecciones
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Rendimiento
          </TabsTrigger>
          <TabsTrigger value="maintenance" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Mantenimiento
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tamaño Total</CardTitle>
                <HardDrive className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatBytes(stats.totalSize)}</div>
                <p className="text-xs text-muted-foreground">
                  Datos: {formatBytes(stats.dataSize)}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Documentos</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalDocuments.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Promedio: {formatBytes(stats.avgObjSize)}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Colecciones</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalCollections}</div>
                <p className="text-xs text-muted-foreground">
                  Índices: {formatBytes(stats.indexSize)}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Uso de Almacenamiento</CardTitle>
                <PieChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{getStorageUsagePercentage()}%</div>
                <Progress value={getStorageUsagePercentage()} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  {formatBytes(stats.dataSize)} / {formatBytes(stats.storageSize)}
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="collections" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Colecciones de la Base de Datos
              </CardTitle>
              <CardDescription>
                Información detallada de cada colección
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nombre</TableHead>
                      <TableHead className="text-right">Documentos</TableHead>
                      <TableHead className="text-right">Tamaño Promedio</TableHead>
                      <TableHead className="text-right">Tamaño Total</TableHead>
                      <TableHead className="text-right">Índices</TableHead>
                      <TableHead className="text-right">Tamaño Índices</TableHead>
                      <TableHead>Acciones</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {collections.map((collection) => (
                      <TableRow key={collection.name}>
                        <TableCell className="font-medium">
                          {collection.name}
                        </TableCell>
                        <TableCell className="text-right">
                          {collection.documentCount.toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatBytes(collection.avgDocSize)}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatBytes(collection.totalSize)}
                        </TableCell>
                        <TableCell className="text-right">
                          {collection.indexCount}
                        </TableCell>
                        <TableCell className="text-right">
                          {formatBytes(collection.indexSize)}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleOptimizeCollection(collection.name)}
                            disabled={isLoading}
                          >
                            <Zap className="h-4 w-4 mr-1" />
                            Optimizar
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Rendimiento de Consultas
              </CardTitle>
              <CardDescription>
                Estadísticas de rendimiento de operaciones de base de datos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Operación</TableHead>
                      <TableHead>Colección</TableHead>
                      <TableHead className="text-right">Tiempo Promedio</TableHead>
                      <TableHead className="text-right">Total Ejecuciones</TableHead>
                      <TableHead className="text-right">Consultas Lentas</TableHead>
                      <TableHead>Última Ejecución</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {queryPerformance.map((query, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Badge variant="outline">{query.operation}</Badge>
                        </TableCell>
                        <TableCell className="font-medium">
                          {query.collection}
                        </TableCell>
                        <TableCell className="text-right">
                          <span className={query.avgExecutionTime > 100 ? 'text-destructive' : 'text-accent'}>
                            {query.avgExecutionTime}ms
                          </span>
                        </TableCell>
                        <TableCell className="text-right">
                          {query.totalExecutions.toLocaleString()}
                        </TableCell>
                        <TableCell className="text-right">
                          {query.slowQueries > 0 ? (
                            <Badge variant="destructive">{query.slowQueries}</Badge>
                          ) : (
                            <Badge variant="secondary">0</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {new Date(query.lastExecuted).toLocaleString('es-ES')}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="maintenance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Backup y Restauración
                </CardTitle>
                <CardDescription>
                  Gestiona las copias de seguridad de la base de datos
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button 
                  onClick={handleBackupDatabase} 
                  disabled={isLoading}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Crear Backup Completo
                </Button>
                <Button variant="outline" className="w-full">
                  <Upload className="h-4 w-4 mr-2" />
                  Restaurar desde Backup
                </Button>
                <div className="text-sm text-muted-foreground">
                  <p>Último backup: Hace 2 horas</p>
                  <p>Tamaño del backup: 1.8 GB</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Mantenimiento
                </CardTitle>
                <CardDescription>
                  Herramientas de optimización y limpieza
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button variant="outline" className="w-full">
                  <Zap className="h-4 w-4 mr-2" />
                  Optimizar Todas las Colecciones
                </Button>
                <Button variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reconstruir Índices
                </Button>
                <Button variant="destructive" className="w-full">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Limpiar Logs Antiguos
                </Button>
                <div className="text-sm text-muted-foreground">
                  <p>Última optimización: Hace 1 día</p>
                  <p>Logs antiguos: 45,000 entradas</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}