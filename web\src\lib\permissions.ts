/**
 * Sistema de permisos y roles para QAK
 * Define las 3 categorías principales de usuarios:
 * 1. SUPER ADMIN - Control total del sistema
 * 2. ADMIN DE TENANT - Gestión de su propio tenant
 * 3. USUARIO DE TENANT - Uso básico de funcionalidades
 */

export enum UserRole {
  USER = 'User',
  ADMIN = 'Admin',
  SUPER_ADMIN = 'SuperAdmin'
}

export enum TenantUserRole {
  MEMBER = 'Member',
  ADMIN = 'Admin',
  OWNER = 'Owner'
}

export interface UserPermissions {
  // Super Admin - Control total del sistema
  isSuperAdmin: boolean;
  canManageTenants: boolean;
  canManageGlobalUsers: boolean;
  canAccessGlobalAdmin: boolean;

  // Admin de Tenant - Gestión de su propio tenant
  isTenantAdmin: boolean;
  canManageOwnTenant: (tenantId?: string) => boolean;
  canManageUsersInOwnTenant: (tenantId?: string) => boolean;

  // Usuario de Tenant - Uso básico
  isTenantUser: boolean;
  canExecuteTests: boolean;
  canCreateTests: boolean;
  canUseQAKI: boolean;

  // Métodos de verificación
  hasPermission: (permission: string, tenantId?: string) => boolean;
}

/**
 * Servicio centralizado de permisos
 */
export class PermissionService {
  /**
   * Verifica si un usuario puede gestionar un tenant específico
   */
  static canManageTenant(userRole: string, userTenantId?: string, targetTenantId?: string): boolean {
    // SuperAdmin puede gestionar cualquier tenant
    if (userRole === UserRole.SUPER_ADMIN) {
      return true;
    }

    // Admin puede gestionar cualquier tenant (gestión global)
    if (userRole === UserRole.ADMIN) {
      return true;
    }

    // Otros usuarios no pueden gestionar tenants
    return false;
  }

  /**
   * Verifica si un usuario puede gestionar usuarios dentro de un tenant específico
   */
  static canManageUsersInTenant(userRole: string, userTenantId?: string, targetTenantId?: string): boolean {
    // SuperAdmin puede gestionar usuarios en cualquier tenant
    if (userRole === UserRole.SUPER_ADMIN) {
      return true;
    }

    // Admin puede gestionar usuarios en cualquier tenant (gestión global)
    if (userRole === UserRole.ADMIN) {
      return true;
    }

    // Usuario con TenantAdmin puede gestionar usuarios en su propio tenant
    if (userRole === UserRole.USER && userTenantId === targetTenantId) {
      return true; // Asumimos que si llega aquí, tiene rol de admin en el tenant
    }

    return false;
  }

  /**
   * Verifica si un usuario es un tenant user regular
   */
  static isTenantUser(userRole: string, userTenantId?: string, targetTenantId?: string): boolean {
    // Usuario regular dentro de un tenant específico
    return userRole === UserRole.USER &&
           userTenantId === targetTenantId;
  }

  /**
   * Verifica si un usuario puede acceder a administración global
   */
  static canAccessGlobalAdmin(userRole: string): boolean {
    // Solo SuperAdmin puede acceder a administración global
    return userRole === UserRole.SUPER_ADMIN;
  }

  /**
   * Verifica si un usuario puede crear tenants
   */
  static canCreateTenants(userRole: string): boolean {
    // Solo SuperAdmin puede crear tenants
    return userRole === UserRole.SUPER_ADMIN;
  }

  /**
   * Verifica si un usuario puede gestionar usuarios globales
   */
  static canManageGlobalUsers(userRole: string): boolean {
    // Solo SuperAdmin puede gestionar usuarios globales
    return userRole === UserRole.SUPER_ADMIN;
  }

  /**
   * Verifica permisos específicos
   */
  static hasPermission(userRole: string, permission: string, userTenantId?: string, targetTenantId?: string): boolean {
    switch (permission.toLowerCase()) {
      case 'manage_tenant':
        return this.canManageTenant(userRole, userTenantId, targetTenantId);

      case 'manage_users':
        return this.canManageUsersInTenant(userRole, userTenantId, targetTenantId);

      case 'global_admin':
        return this.canAccessGlobalAdmin(userRole);

      case 'create_tenant':
        return this.canCreateTenants(userRole);

      case 'manage_global_users':
        return this.canManageGlobalUsers(userRole);

      case 'access_tenant':
        return this.isTenantUser(userRole, userTenantId, targetTenantId);

      case 'execute_tests':
      case 'create_tests':
      case 'use_qaki':
        // Todos los usuarios autenticados pueden usar estas funcionalidades
        return userRole === UserRole.USER || userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN;

      default:
        return false;
    }
  }

  /**
   * Obtiene todos los permisos de un usuario
   */
  static getUserPermissions(userRole: string, userTenantId?: string): UserPermissions {
    return {
      // Super Admin - Control total del sistema
      isSuperAdmin: userRole === UserRole.SUPER_ADMIN,
      canManageTenants: userRole === UserRole.SUPER_ADMIN,
      canManageGlobalUsers: userRole === UserRole.SUPER_ADMIN,
      canAccessGlobalAdmin: userRole === UserRole.SUPER_ADMIN,

      // Admin de Tenant - Gestión de su propio tenant
      isTenantAdmin: userRole === UserRole.ADMIN && !!userTenantId,
      canManageOwnTenant: (tenantId?: string) =>
        this.canManageTenant(userRole, userTenantId, tenantId),
      canManageUsersInOwnTenant: (tenantId?: string) =>
        this.canManageUsersInTenant(userRole, userTenantId, tenantId),

      // Usuario de Tenant - Uso básico
      isTenantUser: userRole === UserRole.USER && !!userTenantId,
      canExecuteTests: userRole === UserRole.USER || userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN,
      canCreateTests: userRole === UserRole.USER || userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN,
      canUseQAKI: userRole === UserRole.USER || userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN,

      // Método de verificación general
      hasPermission: (permission: string, tenantId?: string) =>
        this.hasPermission(userRole, permission, userTenantId, tenantId)
    };
  }
}

/**
 * Utilidades para trabajar con roles
 */
export class RoleUtils {
  /**
   * Obtiene el nombre display de un rol
   */
  static getRoleDisplayName(role: string): string {
    switch (role) {
      case UserRole.SUPER_ADMIN:
        return 'Super Admin';
      case UserRole.ADMIN:
        return 'Admin';
      case UserRole.USER:
        return 'Usuario';
      default:
        return role;
    }
  }

  /**
   * Obtiene el nombre display de un rol de tenant
   */
  static getTenantRoleDisplayName(role: string): string {
    switch (role) {
      case TenantUserRole.OWNER:
        return 'Owner';
      case TenantUserRole.ADMIN:
        return 'Admin';
      case TenantUserRole.MEMBER:
        return 'Miembro';
      default:
        return role;
    }
  }

  /**
   * Verifica si un rol es de sistema (no tenant-specific)
   */
  static isSystemRole(role: string): boolean {
    return role === UserRole.SUPER_ADMIN || role === UserRole.ADMIN;
  }

  /**
   * Verifica si un rol es de tenant
   */
  static isTenantRole(role: string): boolean {
    return role === UserRole.USER;
  }
}