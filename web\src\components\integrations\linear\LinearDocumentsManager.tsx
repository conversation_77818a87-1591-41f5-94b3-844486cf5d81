"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  SearchIcon, 
  FileTextIcon, 
  CalendarIcon, 
  UserIcon,
  ExternalLinkIcon,
  FilterIcon,
  SortAscIcon
} from 'lucide-react';
import { toast } from 'sonner';

interface LinearDocument {
  id: string;
  linearIssueId: string;
  issueNumber: string;
  title: string;
  description?: string;
  state: string;
  priority: number;
  labels: string[];
  teamName?: string;
  projectName?: string;
  assigneeName?: string;
  creatorName?: string;
  linearCreatedAt: string;
  linearUpdatedAt: string;
  createdAt: string;
  updatedAt: string;
}

interface LinearDocumentsManagerProps {
  tenantId: string;
  workspaceId?: string;
}

export function LinearDocumentsManager({ tenantId, workspaceId }: LinearDocumentsManagerProps) {
  const [documents, setDocuments] = useState<LinearDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<LinearDocument | null>(null);
  const [filters, setFilters] = useState({
    state: '',
    priority: '',
    team: '',
    project: '',
    assignee: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0
  });

  useEffect(() => {
    loadDocuments();
  }, [tenantId, workspaceId, searchQuery, filters, pagination.page]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        skip: ((pagination.page - 1) * pagination.pageSize).toString(),
        take: pagination.pageSize.toString()
      });

      if (searchQuery) {
        queryParams.append('searchText', searchQuery);
      }

      if (workspaceId) {
        queryParams.append('workspaceIds', workspaceId);
      }

      if (filters.state) {
        queryParams.append('states', filters.state);
      }

      if (filters.priority) {
        queryParams.append('priorities', filters.priority);
      }

      if (filters.team) {
        queryParams.append('teamIds', filters.team);
      }

      if (filters.project) {
        queryParams.append('projectIds', filters.project);
      }

      if (filters.assignee) {
        queryParams.append('assigneeIds', filters.assignee);
      }

      const response = await fetch(`/api/v1/linear/documents?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-Tenant-ID': tenantId
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents);
        setPagination(prev => ({
          ...prev,
          totalCount: data.totalCount,
          totalPages: data.totalPages
        }));
      } else {
        toast.error('Error loading documents');
      }
    } catch (error) {
      console.error('Error loading documents:', error);
      toast.error('Error loading documents');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleFilterChange = (filterType: string, value: string) => {
    setFilters(prev => ({ ...prev, [filterType]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const getPriorityLabel = (priority: number) => {
    switch (priority) {
      case 0: return 'None';
      case 1: return 'Low';
      case 2: return 'Medium';
      case 3: return 'High';
      case 4: return 'Urgent';
      default: return 'Unknown';
    }
  };

  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 0: return 'bg-gray-100 text-gray-800';
      case 1: return 'bg-blue-100 text-blue-800';
      case 2: return 'bg-yellow-100 text-yellow-800';
      case 3: return 'bg-orange-100 text-orange-800';
      case 4: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getLinearUrl = (doc: LinearDocument) => {
    // Extract workspace URL key from the workspace info
    return `https://linear.app/issue/${doc.linearIssueId}`;
  };

  if (loading && documents.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading documents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Linear Documents</h2>
          <p className="text-muted-foreground">
            Browse and search synchronized issues and user stories from Linear
          </p>
        </div>
        <div className="text-sm text-muted-foreground">
          {pagination.totalCount} documents
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search issues and user stories..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <FilterIcon className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </div>

            {/* Quick Filters */}
            <div className="flex flex-wrap gap-2">
              <Button 
                variant={filters.state === '' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => handleFilterChange('state', '')}
              >
                All States
              </Button>
              <Button 
                variant={filters.state === 'In Progress' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => handleFilterChange('state', 'In Progress')}
              >
                In Progress
              </Button>
              <Button 
                variant={filters.state === 'Todo' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => handleFilterChange('state', 'Todo')}
              >
                Todo
              </Button>
              <Button 
                variant={filters.state === 'Done' ? 'default' : 'outline'} 
                size="sm"
                onClick={() => handleFilterChange('state', 'Done')}
              >
                Done
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Grid */}
      <div className="grid gap-4">
        {documents.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <FileTextIcon className="w-12 h-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No documents found</h3>
              <p className="text-muted-foreground text-center">
                {searchQuery || Object.values(filters).some(f => f) 
                  ? "Try adjusting your search or filters"
                  : "No Linear documents have been synchronized yet"}
              </p>
            </CardContent>
          </Card>
        ) : (
          documents.map((doc) => (
            <Card key={doc.id} className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Badge variant="outline">
                        {doc.issueNumber}
                      </Badge>
                      <Badge className={getPriorityColor(doc.priority)}>
                        {getPriorityLabel(doc.priority)}
                      </Badge>
                      <Badge variant="secondary">
                        {doc.state}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg leading-6">
                      {doc.title}
                    </CardTitle>
                    {doc.description && (
                      <CardDescription className="mt-2 line-clamp-2">
                        {doc.description}
                      </CardDescription>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                  >
                    <a href={getLinearUrl(doc)} target="_blank" rel="noopener noreferrer">
                      <ExternalLinkIcon className="w-4 h-4" />
                    </a>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Labels */}
                  {doc.labels.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {doc.labels.map((label) => (
                        <Badge key={label} variant="outline" className="text-xs">
                          {label}
                        </Badge>
                      ))}
                    </div>
                  )}

                  {/* Metadata */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm text-muted-foreground">
                    {doc.teamName && (
                      <div className="flex items-center space-x-1">
                        <span className="font-medium">Team:</span>
                        <span>{doc.teamName}</span>
                      </div>
                    )}
                    {doc.projectName && (
                      <div className="flex items-center space-x-1">
                        <span className="font-medium">Project:</span>
                        <span>{doc.projectName}</span>
                      </div>
                    )}
                    {doc.assigneeName && (
                      <div className="flex items-center space-x-1">
                        <UserIcon className="w-3 h-3" />
                        <span>{doc.assigneeName}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-1">
                      <CalendarIcon className="w-3 h-3" />
                      <span>{formatDate(doc.linearUpdatedAt)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
            {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} of{' '}
            {pagination.totalCount} documents
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.page === 1}
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={pagination.page === pagination.totalPages}
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}