-- Seed: Development Users
-- Description: Creates default users for development environment
-- Usage: Only run in development environment

-- Insert default superadmin user into auth.users
-- Note: In production, users should be created through Supabase Auth API
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role,
  aud,
  confirmation_token,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  crypt('admin123456', gen_salt('bf')), -- Password: admin123456
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"username": "superadmin", "full_name": "Super Administrator"}',
  false,
  'authenticated',
  'authenticated',
  '',
  '',
  ''
) ON CONFLICT (email) DO NOTHING;

-- Insert corresponding profile for the superadmin user
INSERT INTO public.profiles (
  id,
  email,
  username,
  first_name,
  last_name,
  full_name,
  role,
  email_verified,
  tenant_id,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '<EMAIL>',
  'superadmin',
  'Super',
  'Administrator',
  'Super Administrator',
  'SuperAdmin',
  true,
  null,
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Insert test user for development
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role,
  aud,
  confirmation_token,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000002'::uuid,
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  crypt('test123456', gen_salt('bf')), -- Password: test123456
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"username": "testuser", "full_name": "Test User"}',
  false,
  'authenticated',
  'authenticated',
  '',
  '',
  ''
) ON CONFLICT (email) DO NOTHING;

-- Insert corresponding profile for the test user
INSERT INTO public.profiles (
  id,
  email,
  username,
  first_name,
  last_name,
  full_name,
  role,
  email_verified,
  tenant_id,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000002'::uuid,
  '<EMAIL>',
  'testuser',
  'Test',
  'User',
  'Test User',
  'User',
  true,
  null,
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;