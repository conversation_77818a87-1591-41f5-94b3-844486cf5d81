"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createTestCase, createTestCaseWithFiles, getComputedVariables } from "@/lib/api";
import type { TestCaseCreateInput, TestCaseUpdateInput, ComputedVariable } from "@/lib/types";
import { ModernTestCreator } from "@/components/forms/ModernTestCreator";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function CreateTestCasePage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;

  // Fetch computed variables for this project/suite
  const { data: availableVariables = [], isLoading: loadingVariables } = useQuery({
    queryKey: ['computedVariables', projectId, suiteId],
    queryFn: () => getComputedVariables(projectId, suiteId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const mutation = useMutation({
    mutationFn: (newTestCase: TestCaseCreateInput) => {
      // Use createTestCaseWithFiles if file attachments are present
      if (newTestCase.file_attachments && newTestCase.file_attachments.length > 0) {
        return createTestCaseWithFiles(projectId, suiteId, newTestCase);
      } else {
        return createTestCase(projectId, suiteId, newTestCase);
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
      queryClient.invalidateQueries({ queryKey: ['suite', projectId, suiteId] }); // Also invalidate suite details 
      toast({
        title: "Test Case Created",
        description: `Test Case "${data.name}" has been successfully created.`,
      });
      router.push(`/projects/${projectId}/suites/${suiteId}/tests/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: "Error Creating Test Case",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: TestCaseCreateInput) => {
    await mutation.mutateAsync(data);
  };

  if (loadingVariables) {
    return (
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href={`/projects/${projectId}/suites/${suiteId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Suite
          </Link>
        </Button>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading variables...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suite
        </Link>
      </Button>
      <h1 className="page-header">Create New Test Case</h1>
      <ModernTestCreator 
        onSubmit={handleSubmit}
        isSubmitting={mutation.isPending}
        availableVariables={availableVariables}
      />
    </div>
  );
}
