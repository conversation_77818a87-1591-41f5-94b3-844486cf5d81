"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { TestCase, TestCaseCreateInput, TestCaseUpdateInput, TestCasePriority, TestCaseType } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { XIcon } from "lucide-react";
import { GherkinEditor } from "@/components/execution/GherkinEditor";
import { SensitiveDataEditor } from "@/components/forms/SensitiveDataEditor";
import { AllowedDomainsEditor } from "@/components/forms/AllowedDomainsEditor";
import { Separator } from "@/components/ui/separator";
import { FileUpload } from "@/components/ui/file-upload";

const testCaseFormSchema = z.object({
  name: z.string().min(2, { message: "Test case name must be at least 2 characters." }),
  description: z.string().optional(),
  instructions: z.string().optional(),
  user_story: z.string().optional(),
  gherkin: z.string().optional(),
  url: z.string().url({ message: "Please enter a valid URL." }).optional().or(z.literal('')),
  tags: z.array(z.string()).optional(),
  priority: z.enum(["Low", "Medium", "High", "Critical"]).default("Medium"),
  type: z.enum(["Functional", "Performance", "Security", "Integration", "UnitTest"]).default("Functional"),
  sensitive_data: z.record(z.record(z.string())).optional(),
  allowed_domains: z.array(z.string()).optional(),
  file_attachments: z.array(z.instanceof(File)).optional(),
  enable_file_handling: z.boolean().optional(),
});

type TestCaseFormData = z.infer<typeof testCaseFormSchema>;

interface TestCaseFormProps {
  testCase?: TestCase;
  onSubmit: (data: TestCaseCreateInput | TestCaseUpdateInput) => Promise<void>;
  isSubmitting: boolean;
  submitButtonText?: string;
}

const priorityOptions: { value: TestCasePriority; label: string; description: string }[] = [
  { value: "Low", label: "Low", description: "Non-critical functionality" },
  { value: "Medium", label: "Medium", description: "Standard functionality" },
  { value: "High", label: "High", description: "Important functionality" },
  { value: "Critical", label: "Critical", description: "Business-critical functionality" },
];

const typeOptions: { value: TestCaseType; label: string; description: string }[] = [
  { value: "Functional", label: "Functional", description: "Tests application features and workflows" },
  { value: "Performance", label: "Performance", description: "Tests system performance and load" },
  { value: "Security", label: "Security", description: "Tests security vulnerabilities" },
  { value: "Integration", label: "Integration", description: "Tests integration between systems" },
  { value: "UnitTest", label: "Unit Test", description: "Tests individual components" },
];

export function TestCaseForm({
  testCase,
  onSubmit,
  isSubmitting,
  submitButtonText = "Create Test Case",
}: TestCaseFormProps) {
  const [currentTag, setCurrentTag] = useState("");
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  const form = useForm<TestCaseFormData>({
    resolver: zodResolver(testCaseFormSchema),
    defaultValues: {
      name: testCase?.name || "",
      description: testCase?.description || "",
      instructions: testCase?.instructions || "",
      user_story: testCase?.user_story || "",
      gherkin: testCase?.gherkin || "",
      url: testCase?.url || "",
      tags: testCase?.tags || [],
      priority: testCase?.priority || "Medium",
      type: testCase?.type || "Functional",
      sensitive_data: testCase?.sensitive_data || {},
      allowed_domains: testCase?.allowed_domains || [],
      file_attachments: [],
      enable_file_handling: false,
    },
  });

  const handleAddTag = () => {
    if (currentTag.trim() !== "") {
      const currentTags = form.getValues("tags") || [];
      if(!currentTags.includes(currentTag.trim())) {
        form.setValue("tags", [...currentTags, currentTag.trim()]);
      }
      setCurrentTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter(tag => tag !== tagToRemove));
  };

  const handleFilesChange = (files: File[]) => {
    setAttachedFiles(files);
    form.setValue("file_attachments", files);
    form.setValue("enable_file_handling", files.length > 0);
  };

  const handleSubmit = async (data: TestCaseFormData) => {
    const submitData = {
      ...data,
      file_attachments: attachedFiles,
      enable_file_handling: attachedFiles.length > 0,
    };
    await onSubmit(submitData);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{testCase ? "Edit Test Case" : "Create New Test Case"}</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Basic Information</h3>
                
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Test Case Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Successful Login" {...field} />
                      </FormControl>
                      <FormDescription>
                        A descriptive name for this test case.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Verifies that a user can log in successfully."
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Priority</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {priorityOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex flex-col">
                                  <span>{option.label}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {option.description}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Test case priority level
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Test Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select test type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {typeOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex flex-col">
                                  <span>{option.label}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {option.description}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Category of testing this case covers
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target URL</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com/login" {...field} />
                      </FormControl>
                       <FormDescription>
                        The starting URL for this test case.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tags"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tags</FormLabel>
                       <div className="flex items-center gap-2">
                        <Input
                          placeholder="Add a tag (e.g., login, critical)"
                          value={currentTag}
                          onChange={(e) => setCurrentTag(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              handleAddTag();
                            }
                          }}
                        />
                        <Button type="button" variant="outline" onClick={handleAddTag}>Add Tag</Button>
                      </div>
                      <FormDescription>
                        Keywords to categorize your test case.
                      </FormDescription>
                      {field.value && field.value.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {field.value.map((tag, index) => (
                            <Badge key={`testcase-form-tag-${index}-${tag}`} variant="secondary" className="flex items-center gap-1">
                              {tag}
                               <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="h-4 w-4 text-muted-foreground hover:text-destructive"
                                onClick={() => handleRemoveTag(tag)}
                              >
                                <XIcon size={12} />
                                <span className="sr-only">Remove tag {tag}</span>
                              </Button>
                            </Badge>
                          ))}
                        </div>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Test Content */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Test Content</h3>

                <FormField
                  control={form.control}
                  name="user_story"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>User Story</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="As a user, I want to log in..."
                          className="resize-none"
                          rows={4}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        The user story or requirement this test case validates.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="instructions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Instructions</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="1. Go to the login page..."
                          className="resize-none"
                          rows={6}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        The manual steps or automation instructions for this test case.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="file_attachments"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>File Attachments</FormLabel>
                      <FormControl>
                        <FileUpload
                          onFilesChange={handleFilesChange}
                          value={attachedFiles}
                          maxFiles={5}
                          maxSize={10 * 1024 * 1024} // 10MB
                          acceptedFileTypes={['image/*', 'application/pdf', '.doc', '.docx', '.txt', '.json', '.xml']}
                          disabled={isSubmitting}
                        />
                      </FormControl>
                      <FormDescription>
                        Upload files that will be included in the test case instructions. These files will be available during test execution.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gherkin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gherkin Scenario</FormLabel>
                      <FormControl>
                        <GherkinEditor
                          value={field.value || ""}
                          onChange={field.onChange}
                          placeholder="Feature: User Login&#10;&#10;  Scenario: Successful login&#10;    Given I am on the login page&#10;    When I enter valid credentials&#10;    Then I should be logged in successfully"
                        />
                      </FormControl>
                      <FormDescription>
                        Define the test scenario using Gherkin syntax (Given, When, Then).
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Separator />

              {/* Security Configuration */}
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Security & Automation Configuration</h3>
                
                <FormField
                  control={form.control}
                  name="allowed_domains"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Allowed Domains</FormLabel>
                      <FormControl>
                        <AllowedDomainsEditor
                          value={field.value || []}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sensitive_data"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sensitive Data Configuration</FormLabel>
                      <FormControl>
                        <SensitiveDataEditor
                          value={field.value || {}}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end pt-6">
                <Button type="submit" disabled={isSubmitting} size="lg">
                  {isSubmitting ? "Submitting..." : submitButtonText}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}