"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trash2, Plus, Shield, AlertTriangle, CheckCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface AllowedDomainsEditorProps {
  value?: string[];
  onChange: (value: string[]) => void;
}

export function AllowedDomainsEditor({ value = [], onChange }: AllowedDomainsEditorProps) {
  const [newDomain, setNewDomain] = useState("");
  const [validationError, setValidationError] = useState("");

  const validateDomain = (domain: string): { isValid: boolean; error?: string } => {
    if (!domain.trim()) {
      return { isValid: false, error: "Domain cannot be empty" };
    }

    // Basic URL validation
    try {
      const url = new URL(domain.startsWith('http') ? domain : `https://${domain}`);
      
      // Check for wildcards in subdomains
      if (domain.includes('*')) {
        const wildcardPattern = /^https?:\/\/(\*\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/.*)?$/;
        if (!wildcardPattern.test(domain)) {
          return { isValid: false, error: "Invalid wildcard pattern. Use format: https://*.example.com" };
        }
      }
      
      return { isValid: true };
    } catch (error) {
      return { isValid: false, error: "Invalid URL format" };
    }
  };

  const addDomain = () => {
    const trimmedDomain = newDomain.trim();
    
    if (!trimmedDomain) {
      setValidationError("Please enter a domain");
      return;
    }

    const validation = validateDomain(trimmedDomain);
    if (!validation.isValid) {
      setValidationError(validation.error || "Invalid domain");
      return;
    }

    if (value.includes(trimmedDomain)) {
      setValidationError("Domain already exists");
      return;
    }

    onChange([...value, trimmedDomain]);
    setNewDomain("");
    setValidationError("");
  };

  const removeDomain = (domainToRemove: string) => {
    onChange(value.filter(domain => domain !== domainToRemove));
  };

  const getDomainType = (domain: string) => {
    if (domain.includes('*')) {
      return { type: 'wildcard', icon: Shield, color: 'text-blue-600' };
    }
    if (domain.startsWith('https://')) {
      return { type: 'secure', icon: CheckCircle, color: 'text-green-600' };
    }
    return { type: 'standard', icon: Shield, color: 'text-muted-foreground' };
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Allowed Domains
        </CardTitle>
        <CardDescription>
          Configure which domains this test is allowed to navigate to during execution.
          This helps prevent tests from accessing unauthorized sites.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Security Feature:</strong> Tests will be restricted to only these domains.
            Use wildcards (*.example.com) to allow all subdomains.
          </AlertDescription>
        </Alert>

        <div className="space-y-3">
          <div className="flex items-end gap-2">
            <div className="flex-1 space-y-2">
              <Label htmlFor="new-domain">Add Domain</Label>
              <Input
                id="new-domain"
                placeholder="https://example.com or *.example.com"
                value={newDomain}
                onChange={(e) => {
                  setNewDomain(e.target.value);
                  setValidationError("");
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    addDomain();
                  }
                }}
                className={validationError ? "border-destructive" : ""}
              />
              {validationError && (
                <p className="text-sm text-destructive">{validationError}</p>
              )}
            </div>
            <Button onClick={addDomain} disabled={!newDomain.trim()}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>

          {value.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground border-2 border-dashed rounded-lg">
              <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No domains configured</p>
              <p className="text-sm">Add domains to restrict test execution scope</p>
            </div>
          ) : (
            <div className="space-y-2">
              <Label>Configured Domains ({value.length})</Label>
              <div className="space-y-2 max-h-48 overflow-y-auto border rounded-lg p-3">
                {value.map((domain, index) => {
                  const { type, icon: Icon, color } = getDomainType(domain);
                  
                  return (
                    <div
                      key={`${domain}-${index}`}
                      className="flex items-center justify-between p-2 bg-muted/50 rounded-md group hover:bg-muted"
                    >
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <Icon className={`h-4 w-4 flex-shrink-0 ${color}`} />
                        <code className="text-sm font-mono truncate">{domain}</code>
                        {type === 'wildcard' && (
                          <Badge variant="outline" className="text-xs">
                            Wildcard
                          </Badge>
                        )}
                        {type === 'secure' && (
                          <Badge variant="outline" className="text-xs text-green-600">
                            HTTPS
                          </Badge>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDomain(domain)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <div className="space-y-2 text-sm text-muted-foreground">
          <p><strong>Examples:</strong></p>
          <ul className="space-y-1 ml-4">
            <li>• <code>https://example.com</code> - Exact domain match</li>
            <li>• <code>https://*.example.com</code> - All subdomains</li>
            <li>• <code>https://app.example.com/admin</code> - Specific path</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}