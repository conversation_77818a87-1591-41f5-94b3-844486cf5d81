/**
 * Configuration Management Functions
 * 
 * Functions for managing system configurations, execution types, and environment defaults.
 * This module handles both predefined and custom configurations.
 * 
 * @module ConfigurationAPI
 */

import { fetchApi } from '../core/fetch';

// ============================================================================
// CONFIGURATION MANAGEMENT
// ============================================================================

/**
 * Get all configurations (predefined + custom)
 * @returns Promise<{ predefined: any[], custom: any[] }> All configurations
 * @example
 * ```typescript
 * const { predefined, custom } = await getAllConfigurations();
 * console.log(`Found ${predefined.length} predefined and ${custom.length} custom configs`);
 * ```
 */
export const getAllConfigurations = (): Promise<{ predefined: any[], custom: any[] }> => 
  fetchApi<{ predefined: any[], custom: any[] }>('/config/');

/**
 * Get all predefined configurations
 * @returns Promise<any[]> List of predefined configurations
 */
export const getPredefinedConfigurations = (): Promise<any[]> => 
  fetchApi<any[]>('/config/predefined');

/**
 * Get specific predefined configuration by type
 * @param configType - Configuration type identifier
 * @returns Promise<any> Predefined configuration
 */
export const getPredefinedConfiguration = (configType: string): Promise<any> => 
  fetchApi<any>(`/config/predefined/${configType}`);

/**
 * Update a predefined configuration
 * @param configType - Configuration type identifier
 * @param data - Updated configuration data
 * @returns Promise<any> Updated configuration
 */
export const updatePredefinedConfiguration = (configType: string, data: any): Promise<any> => 
  fetchApi<any>(`/config/predefined/${configType}`, {
    method: 'PUT',
    body: JSON.stringify(data)
  });

/**
 * Get all custom configurations
 * @returns Promise<any[]> List of custom configurations
 */
export const getCustomConfigurations = (): Promise<any[]> => 
  fetchApi<any[]>('/config/custom');

/**
 * Get specific custom configuration by ID
 * @param configId - Configuration ID
 * @returns Promise<any> Custom configuration
 */
export const getCustomConfiguration = (configId: string): Promise<any> => 
  fetchApi<any>(`/config/custom/${configId}`);

/**
 * Create a new custom configuration
 * @param data - Configuration data
 * @returns Promise<any> Created configuration
 */
export const createCustomConfiguration = (data: any): Promise<any> => 
  fetchApi<any>('/config/custom', {
    method: 'POST',
    body: JSON.stringify(data)
  });

/**
 * Update an existing custom configuration
 * @param configId - Configuration ID to update
 * @param data - Updated configuration data
 * @returns Promise<any> Updated configuration
 */
export const updateCustomConfiguration = (configId: string, data: any): Promise<any> => 
  fetchApi<any>(`/config/custom/${configId}`, {
    method: 'PUT',
    body: JSON.stringify(data)
  });

/**
 * Delete a custom configuration
 * @param configId - Configuration ID to delete
 * @returns Promise<any> Deletion result
 */
export const deleteCustomConfiguration = (configId: string): Promise<any> => 
  fetchApi<any>(`/config/custom/${configId}`, {
    method: 'DELETE'
  });

/**
 * Validate a configuration before saving
 * @param data - Configuration data to validate
 * @returns Promise<any> Validation result
 */
export const validateConfiguration = (data: any): Promise<any> => 
  fetchApi<any>('/config/validate', {
    method: 'POST',
    body: JSON.stringify(data)
  });

/**
 * Test a configuration to ensure it works
 * @param data - Configuration data to test
 * @returns Promise<any> Test result
 */
export const testConfiguration = (data: any): Promise<any> => 
  fetchApi<any>('/config/test', {
    method: 'POST',
    body: JSON.stringify(data)
  });

// ============================================================================
// EXECUTION TYPE MANAGEMENT
// ============================================================================

/**
 * Get configurations by execution type
 * @param executionType - Type of execution to filter by
 * @returns Promise<any[]> Configurations for the execution type
 */
export const getConfigurationsByExecutionType = (executionType: string): Promise<any[]> => 
  fetchApi<any[]>(`/config/mongodb/configurations/execution-type/${executionType}`);

/**
 * Update configuration execution types
 * @param configId - Configuration ID
 * @param executionTypes - Array of execution types
 * @returns Promise<any> Update result
 */
export const updateConfigurationExecutionTypes = (configId: string, executionTypes: string[]): Promise<any> => 
  fetchApi<any>(`/config/mongodb/configurations/${configId}/execution-types`, {
    method: 'PUT',
    body: JSON.stringify({ execution_types: executionTypes })
  });

/**
 * Validate execution types for conflicts
 * @param data - Validation data
 * @returns Promise<object> Conflict validation result
 */
export const validateExecutionTypes = (data: {
  config_id?: string;
  execution_types: string[];
  exclude_config_id?: string;
}): Promise<{
  has_conflicts: boolean;
  conflicts: Array<{
    config_id: string;
    config_name: string;
    config_type: string;
    conflicting_execution_types: string[];
    is_predefined: boolean;
  }>;
  suggested_action: string;
}> => fetchApi('/config/validate-execution-types', {
  method: 'POST',
  body: JSON.stringify(data)
});

/**
 * Resolve execution type conflicts
 * @param data - Conflict resolution data
 * @returns Promise<object> Resolution result
 */
export const resolveExecutionTypeConflict = (data: {
  action: 'remove_from_existing' | 'cancel';
  target_config?: any;
  conflicts: any[];
  execution_types: string[];
}): Promise<{
  success: boolean;
  message: string;
  updated_configs?: Array<{
    config_id: string;
    name: string;
    removed_types: string[];
  }>;
}> => fetchApi('/config/resolve-execution-type-conflict', {
  method: 'POST',
  body: JSON.stringify(data)
});

// ============================================================================
// ENVIRONMENT DEFAULTS
// ============================================================================

/**
 * Get environment defaults
 * @returns Promise<any> Environment default settings
 */
export const getEnvironmentDefaults = (): Promise<any> => 
  fetchApi<any>('/config/defaults');