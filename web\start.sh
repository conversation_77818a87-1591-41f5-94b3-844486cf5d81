#!/bin/bash

# QAK Web Start Script
set -e

echo "🚀 Starting QAK Web application..."

# Set default environment variables if not provided
export NODE_ENV=${NODE_ENV:-production}
export PORT=${PORT:-3000}
export HOSTNAME=${HOSTNAME:-0.0.0.0}

# Display configuration
echo "📊 Configuration:"
echo "  NODE_ENV: $NODE_ENV"
echo "  PORT: $PORT"
echo "  HOSTNAME: $HOSTNAME"
echo "  API_BASE_URL: $NEXT_PUBLIC_API_BASE_URL"

# Start the application
echo "🎯 Starting Next.js server..."
exec node server.js