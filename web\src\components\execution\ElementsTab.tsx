"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ElementsTabProps {
  historyData: TestExecutionHistoryData;
}

export function ElementsTab({ historyData }: ElementsTabProps) {
  const { elements } = historyData;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="glow-text">Interacted Elements</CardTitle>
      </CardHeader>
      <CardContent>
        {elements && elements.length > 0 ? (
          <ScrollArea className="h-[400px] w-full">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[80px]">Step</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>XPath</TableHead>
                  <TableHead>Attributes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {elements.map((element, index) => (
                  <TableRow key={index}>
                    <TableCell>{element.step}</TableCell>
                    <TableCell className="font-medium">{element.tag_name}</TableCell>
                    <TableCell className="text-xs font-mono text-muted-foreground break-all">{element.xpath}</TableCell>
                    <TableCell className="text-xs font-mono text-muted-foreground break-all">
                      {Object.entries(element.attributes).map(([key, value]) => `${key}="${value}"`).join(' ')}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        ) : (
          <p className="text-muted-foreground">No elements were interacted with during this execution.</p>
        )}
      </CardContent>
    </Card>
  );
}
