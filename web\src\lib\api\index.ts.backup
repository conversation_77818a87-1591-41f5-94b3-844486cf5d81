/**
 * QAK API Library - Main Index
 * 
 * Centralized API interface providing backward compatibility while enabling
 * modular organization. This file re-exports all API functions from their
 * respective modules.
 * 
 * @example
 * ```typescript
 * // You can still import from the main api module
 * import { getProjects, executeTestV2, getLinearWorkspaces } from '@/lib/api';
 * 
 * // Or import from specific modules for better tree-shaking
 * import { getProjects } from '@/lib/api/projects';
 * import { executeTestV2 } from '@/lib/api/execution';
 * ```
 */

// Re-export core functions (for internal use, not typically needed by components)
export { fetchApi, fetchExternalApi, fetchApiWithFormData } from './core/fetch';

// Re-export all project-related functions
export {
  // Projects
  getProjects,
  createProject,
  getProjectById,
  updateProject,
  deleteProject,
  exportProject,
  importProject,

  // Test Suites
  getSuitesByProjectId,
  createSuite,
  getSuiteById,
  updateSuite,
  deleteSuite,

  // Test Cases
  getTestCasesBySuiteId,
  createTestCase,
  createTestCaseWithFiles,
  getTestCaseById,
  updateTestCase,
  updateTestCaseStatus,
  deleteTestCase,
} from './projects';

// Re-export all execution functions
export {
  // V2 Test Execution (recommended)
  executeTestV2,
  executeSmokeTestV2,
  executeTestCaseV2,
  executeTestSuiteV2,
  executeTestUnified,

  // Execution Control
  pauseExecution,
  resumeExecution,
  stopExecution,
  getExecutionById,
  deleteExecution,

  // Execution Data & History
  getTestMongoExecutions,
  getTestExecutionSummaries,
  getTestExecutionHistory,
  getTestExecutionHistoryDetails,

  // V1 Legacy Execution (deprecated)
  executeTestV1,
  executeSmokeTestV1,
  executeTest,
  executeSmokeTest,
  runTest,
  runSmokeTest,
  runTestUsingV2,
  runSmokeTestUsingV2,
  fetchResults,
  downloadHistory,
  getExecutions,
} from './execution';

// Re-export all AI functions
export {
  // User Story Enhancement
  enhanceUserStory,
  generateManualTestCases,
  callEnhanceUserStory,
  callGenerateManualTestCases,

  // Test Generation & Analysis
  generateGherkin,
  generateCode,
  summarizeTestResults,
  callGenerateGherkin,
  callGenerateCode,
  callSummarizeTestResults,

  // Prompt Management & Test History
  saveTestHistory,
  fetchPrompts,
  fetchPromptDetail,
  updatePrompt,
} from './ai';

// Re-export additional AI functions for backward compatibility
// Note: Some prompt functions are duplicated from the main AI module
// TODO: consolidate prompt management in ai/prompts module

// Re-export script generation functions
export {
  getExecutionScript,
  generateScript,
  saveExecutionScript,
  generateCodeFromScript,
} from './scripts';

// Re-export CodeGen functions
export {
  startCodegenSession,
  getCodegenSession,
  stopCodegenSession,
  getCodegenGeneratedCode,
  convertCodegenToTestcase,
  cleanupCodegenSession,
  listCodegenSessions,
  getCodegenStats,
  bulkCleanupCodegenSessions,
  getCodegenHealth,
  getCodegenHistory,
  getCodegenHistorySession,
  executeCodegenTest,
  getCodegenExecution,
  listCodegenExecutions,
  stopCodegenExecution,
  cleanupCodegenExecution,
} from './codegen';

// Re-export tenant management functions
export {
  getTenantUsers,
  addUserToTenant,
  removeUserFromTenant,
  updateUserRoleInTenant,
  getTenantStatistics,
  updateTenantSettings,
  getTenantInvitations,
  createTenantInvitation,
  validateTenantAccess,
  canTenantAddUser,
  canTenantAddProject,
} from './admin/tenants';

// Re-export all integration functions
export {
  // Linear Integration
  callLinearApi,
  getLinearWorkspaces,
  getLinearAuthUrl,
  handleLinearAuthCallback,
  deleteLinearWorkspace,
  syncLinearWorkspace,
  updateLinearSyncConfig,
  getLinearDocuments,

  // Knowledge Base Integration
  uploadKnowledgeBaseDocument,
  getKnowledgeBaseDocuments,
  generateManualTestsFromKB,

  // External API Integration
  callOpenAI,
} from './integration';

// Re-export configuration and analytics functions from utils module
export {
  // Configuration management
  getAllConfigurations,
  getPredefinedConfigurations,
  getPredefinedConfiguration,
  getCustomConfigurations,
  getCustomConfiguration,
  createCustomConfiguration,
  updateCustomConfiguration,
  deleteCustomConfiguration,
  validateConfiguration,
  testConfiguration,
  getConfigurationsByExecutionType,
  updateConfigurationExecutionTypes,
  getEnvironmentDefaults,
  updatePredefinedConfiguration,
  validateExecutionTypes,
  resolveExecutionTypeConflict,

  // Analytics and reporting
  getAnalytics,
  getAnalyticsLegacy,
  compareAnalytics,
} from './utils';

// Re-export admin functions
export {
  // Global user and tenant management
  getGlobalUsers,
  getGlobalTenants,
  createGlobalUser,
  updateGlobalUser,
  deleteGlobalUser,
  toggleGlobalUserActivation,
  createGlobalTenant,
  updateGlobalTenant,
  deleteGlobalTenant,
  toggleGlobalTenantStatus,
  getGlobalUserStatistics,
  getGlobalTenantSummary,

  // LLM Management
  getLLMProviders,
  createLLMProvider,
  updateLLMProvider,
  deleteLLMProvider,
  getLLMModels,
  createLLMModel,
  updateLLMModel,
  deleteLLMModel,
  getLLMCredentials,
  createLLMCredential,
  updateLLMCredential,
  deleteLLMCredential,
  testLLMCredential,
  getEmergencyAccessToken,
} from './admin';

// Temporary: Keep importing remaining functions from the legacy file
// This maintains backward compatibility while we gradually migrate
import {
  
  // Environment Management (TODO: migrate to projects/environments)
  getProjectEnvironments,
  getProjectEnvironment,
  createProjectEnvironment,
  updateProjectEnvironment,
  deleteProjectEnvironment,
  setDefaultEnvironment,
  getDefaultEnvironment,
  getEnvironmentsByTag,
  testEnvironmentUrl,
  
  // Variables API (TODO: migrate to projects/variables)
  getProjectVariables,
  createProjectVariable,
  updateProjectVariable,
  deleteProjectVariable,
  getSuiteVariables,
  createSuiteVariable,
  updateSuiteVariable,
  deleteSuiteVariable,
  getComputedVariables,
  
  // Prompt Management (additional functions not in AI module yet)
  createPrompt,
  deletePrompt,
  validatePrompt,
  validateAllPrompts,
  
  // Types for V2 API
  type V2SmokeTestRequest,
  type V2FullTestRequest,
  type V2TestCaseRequest,
  type V2SuiteRequest,
  type V2ExecutionRequest,
  type AnalyticsResponse,
} from '../api';
  deleteGlobalUser,
  createGlobalTenant,
  updateGlobalTenant,
  deleteGlobalTenant,
  toggleGlobalTenantStatus,
  
  // Prompt Management (additional functions not in AI module yet)
  createPrompt,
  deletePrompt,
  validatePrompt,
  validateAllPrompts
  
  // Note: Types for V2 API are available in their respective modules
} from '../api';

// Re-export the remaining imported functions to maintain backward compatibility
export {
  // Environment Management (TODO: migrate to projects/environments)
  getProjectEnvironments,
  getProjectEnvironment,
  createProjectEnvironment,
  updateProjectEnvironment,
  deleteProjectEnvironment,
  setDefaultEnvironment,
  getDefaultEnvironment,
  getEnvironmentsByTag,
  testEnvironmentUrl,
  
  // Variables API (TODO: migrate to projects/variables)
  getProjectVariables,
  createProjectVariable,
  updateProjectVariable,
  deleteProjectVariable,
  getSuiteVariables,
  createSuiteVariable,
  updateSuiteVariable,
  deleteSuiteVariable,
  getComputedVariables,
  
  // Additional Prompt Management (TODO: move to ai/prompts)
  createPrompt,
  deletePrompt,
  validatePrompt,
  validateAllPrompts,
  
  // Types for V2 API
  type V2SmokeTestRequest,
  type V2FullTestRequest,
  type V2TestCaseRequest,
  type V2SuiteRequest,
  type V2ExecutionRequest,
  type AnalyticsResponse,
};