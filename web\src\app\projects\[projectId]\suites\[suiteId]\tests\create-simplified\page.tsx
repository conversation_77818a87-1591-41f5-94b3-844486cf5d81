"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createTestCase, createTestCaseWithFiles } from "@/lib/api";
import type { TestCaseCreateInput, TestCaseUpdateInput } from "@/lib/types";
import { QuickTestCreator } from "@/components/forms/QuickTestCreator";
import { ModernTestCreator } from "@/components/forms/ModernTestCreator";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft, Zap, Settings } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function CreateTestCaseSimplifiedPage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const [activeTab, setActiveTab] = useState("modern");

  const mutation = useMutation({
    mutationFn: (newTestCase: TestCaseCreateInput) => {
      // Use createTestCaseWithFiles if file attachments are present
      if (newTestCase.file_attachments && newTestCase.file_attachments.length > 0) {
        return createTestCaseWithFiles(projectId, suiteId, newTestCase);
      } else {
        return createTestCase(projectId, suiteId, newTestCase);
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
      queryClient.invalidateQueries({ queryKey: ['suite', projectId, suiteId] });
      toast({
        title: "Test Case Created Successfully!",
        description: `"${data.name}" has been created and is ready for execution.`,
      });
      router.push(`/projects/${projectId}/suites/${suiteId}/tests/${data.id}`);
    },
    onError: (error) => {
      toast({
        title: "Error Creating Test Case",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: TestCaseCreateInput) => {
    await mutation.mutateAsync(data);
  };

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/projects/${projectId}/suites/${suiteId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Suite
          </Link>
        </Button>
      </div>

      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Create Test Case</h1>
        <p className="text-muted-foreground">
          Choose your preferred method to create a new test case
        </p>
      </div>

      {/* Creation Methods */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="quick" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Quick Creator
          </TabsTrigger>
          <TabsTrigger value="modern" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Modern Builder
          </TabsTrigger>
          <TabsTrigger value="detailed" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Detailed Form
          </TabsTrigger>
        </TabsList>

        {/* Quick Creator Tab */}
        <TabsContent value="quick" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-primary" />
                Quick Test Creation
              </CardTitle>
              <CardDescription>
                Perfect for rapid test creation. Just provide a name and steps - we'll handle the rest with smart defaults.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-green-800">Fast</div>
                    <div className="text-xs text-green-600">Create in seconds</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-blue-800">Smart Defaults</div>
                    <div className="text-xs text-blue-600">Auto-configured settings</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-purple-800">Templates</div>
                    <div className="text-xs text-purple-600">Pre-built examples</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <QuickTestCreator
            onSubmit={handleSubmit}
            isSubmitting={mutation.isPending}
          />
        </TabsContent>

        {/* Modern Builder Tab */}
        <TabsContent value="modern" className="space-y-4">
          <ModernTestCreator
            onSubmit={handleSubmit}
            isSubmitting={mutation.isPending}
          />
        </TabsContent>

        {/* Detailed Form Tab */}
        <TabsContent value="detailed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-primary" />
                Detailed Test Creation
              </CardTitle>
              <CardDescription>
                More control over test case creation with simplified options. Priority and Type are auto-set to sensible defaults.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-orange-800">Flexible</div>
                    <div className="text-xs text-orange-600">Multiple content types</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-indigo-50 rounded-lg border border-indigo-200">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-indigo-800">Simplified</div>
                    <div className="text-xs text-indigo-600">No complex fields</div>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 bg-teal-50 rounded-lg border border-teal-200">
                  <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                  <div>
                    <div className="text-sm font-medium text-teal-800">Structured</div>
                    <div className="text-xs text-teal-600">Organized content</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <QuickTestCreator
            onSubmit={handleSubmit}
            isSubmitting={mutation.isPending}
          />
        </TabsContent>
      </Tabs>

      {/* Comparison Info */}
      <Card className="border-dashed">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="font-medium mb-2">What's Different?</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Both methods eliminate unnecessary complexity while maintaining functionality:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-medium text-green-700">✅ What's Simplified</h4>
                <ul className="text-left space-y-1 text-muted-foreground">
                  <li>• No Priority selection (auto: Medium)</li>
                  <li>• No Test Type selection (auto: Functional)</li>
                  <li>• Smart content type detection</li>
                  <li>• Auto-generated descriptions</li>
                  <li>• Streamlined form layout</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-blue-700">📋 What's Preserved</h4>
                <ul className="text-left space-y-1 text-muted-foreground">
                  <li>• Full test execution capability</li>
                  <li>• Tags and categorization</li>
                  <li>• URL and environment settings</li>
                  <li>• All content types (instructions, user stories, gherkin)</li>
                  <li>• Complete API compatibility</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}