import { API_BASE_URL } from './config';

export interface LoginCredentials {
  email: string;
  password: string;
  tenantId?: string;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    token: string;
    refreshToken: string;
    expiresAt: string;
    user: {
      id: string;
      email: string;
      username: string;
      firstName?: string;
      lastName?: string;
      fullName: string;
      role: string;
      emailVerified: boolean;
      tenantId?: string;
    };
    requiresEmailVerification: boolean;
  };
  message?: string;
  errorMessage?: string;
  errors?: string[];
}

export interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  role: string;
  tenantId?: string;
}

const TOKEN_KEY = 'qak_auth_token';
const USER_KEY = 'qak_auth_user';

export class AuthService {
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    console.log('🔐 AuthService: Making login request to:', `${API_BASE_URL}/api/Auth/login`);
    console.log('🔐 AuthService: Credentials:', { email: credentials.email, password: '***' });
    
    const response = await fetch(`${API_BASE_URL}/api/Auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    console.log('🔐 AuthService: Response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.log('🔐 AuthService: Error response:', errorData);
      return {
        success: false,
        errorMessage: errorData.message || `HTTP error ${response.status}`,
        errors: errorData.errors,
      };
    }

    const data = await response.json();
    console.log('🔐 AuthService: Success response:', data);
    
    if (data.success && data.data?.token && !data.data.requiresEmailVerification) {
      console.log('🔐 AuthService: Storing token and user data');
      this.setToken(data.data.token);
      // Map backend user structure to frontend User interface
      const mappedUser: User = {
        id: data.data.user.id,
        email: data.data.user.email,
        username: data.data.user.username,
        name: data.data.user.fullName || data.data.user.username,
        role: this.mapRoleToString(data.data.user.role),
        tenantId: data.data.user.tenantId,
      };
      console.log('🔐 AuthService: Mapped user:', mappedUser);
      this.setUser(mappedUser);
    } else if (data.success && data.data?.requiresEmailVerification) {
      console.log('🔐 AuthService: Email verification required, not storing token');
    }

    return data;
  }

  static logout(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(TOKEN_KEY);
      localStorage.removeItem(USER_KEY);
    }
  }

  static getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(TOKEN_KEY);
  }

  static setToken(token: string): void {
    if (typeof window !== 'undefined') {
      console.log('🔐 AuthService: Setting token in localStorage');
      localStorage.setItem(TOKEN_KEY, token);
    }
  }

  static getUser(): User | null {
    if (typeof window === 'undefined') return null;
    const userStr = localStorage.getItem(USER_KEY);
    if (!userStr) {
      console.log('🔐 AuthService: No user found in localStorage');
      return null;
    }
    
    try {
      const user = JSON.parse(userStr);
      console.log('🔐 AuthService: Retrieved user from localStorage:', user);
      
      // Ensure role is properly mapped to string
      if (user && typeof user.role === 'number') {
        user.role = this.mapRoleToString(user.role);
        // Update localStorage with corrected role
        this.setUser(user);
      }
      
      return user;
    } catch {
      console.log('🔐 AuthService: Failed to parse user from localStorage');
      return null;
    }
  }

  static setUser(user: User): void {
    if (typeof window !== 'undefined') {
      console.log('🔐 AuthService: Setting user in localStorage:', user);
      localStorage.setItem(USER_KEY, JSON.stringify(user));
    }
  }

  static isAuthenticated(): boolean {
    const token = this.getToken();
    const user = this.getUser();
    
    if (!token || !user) return false;

    try {
      // Basic JWT token validation (check if not expired)
      const parts = token.split('.');
      if (parts.length !== 3) return false;
      
      const payload = JSON.parse(atob(parts[1]));
      const now = Date.now() / 1000;
      
      return payload.exp > now;
    } catch {
      // If token is invalid, clear storage and return false
      this.logout();
      return false;
    }
  }

  static getAuthHeaders(): Record<string, string> {
    const token = this.getToken();
    if (!token) return {};
    
    return {
      'Authorization': `Bearer ${token}`,
    };
  }

  // Development helper to force verify email
  static async devVerifyEmail(email: string): Promise<boolean> {
    try {
      console.log('🔐 AuthService: Attempting dev email verification for:', email);
      
      const response = await fetch(`${API_BASE_URL}/api/Auth/dev-verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      console.log('🔐 AuthService: Dev verification response status:', response.status);
      
      if (response.ok) {
        console.log('🔐 AuthService: Email verified successfully for development');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('🔐 AuthService: Dev email verification error:', error);
      return false;
    }
  }

  private static mapRoleToString(role: number | string): string {
    // Handle both numeric and string role values
    if (typeof role === 'string') {
      return role;
    }
    
    // Map numeric UserRole enum values to strings
    switch (role) {
      case 1:
        return 'User';
      case 2:
        return 'Admin';
      case 3:
        return 'SuperAdmin';
      default:
        console.warn('🔐 AuthService: Unknown role value:', role);
        return 'User'; // Default to User role
    }
  }
}