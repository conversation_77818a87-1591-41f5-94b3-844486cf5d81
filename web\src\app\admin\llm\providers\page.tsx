'use client'

import * as React from 'react'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Brain,
   CheckCircle,
   Zap,
   RefreshCw,
   Key,
   Eye,
   EyeOff,
   Copy,
   DollarSign,
   Clock
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import {
  getLLMProviders,
  createLLMProvider,
  updateLLMProvider,
  deleteLLMProvider,
  getLLMModels,
  createLLMModel,
  updateLLMModel,
  deleteLLMModel,
  getLLMCredentials,
  createLLMCredential,
  updateLLMCredential,
  deleteLLMCredential,
  testLLMCredential
} from '@/lib/api'

interface LLMProvider {
  id: string
  name: string
  displayName: string
  baseUrl?: string
  apiVersion?: string
  isActive: boolean
  description?: string
  supportedModels?: string[]
  rateLimits?: {
    requestsPerMinute: number
    requestsPerHour: number
    tokensPerMinute: number
  }
  createdAt: string
  updatedAt: string
}

interface CreateProviderRequest {
  name: string
  displayName: string
  baseUrl?: string
  apiVersion?: string
  description?: string
  rateLimits?: {
    requestsPerMinute: number
    requestsPerHour: number
    tokensPerMinute: number
  }
}

export default function ProvidersPage() {
  const [providers, setProviders] = useState<LLMProvider[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
   const [editingProvider, setEditingProvider] = useState<LLMProvider | null>(null)
   const [editingModel, setEditingModel] = useState<any | null>(null)
   const [showEditDialog, setShowEditDialog] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const { toast } = useToast()

  useEffect(() => {
    fetchProviders()
  }, [])

  const fetchProviders = async () => {
    try {
      setLoading(true)
      const response = await getLLMProviders()

      // Handle different response formats
      let providersData: LLMProvider[] = []
      if (Array.isArray(response)) {
        providersData = response as LLMProvider[]
      } else if (response && typeof response === 'object' && !Array.isArray(response)) {
        const responseObj = response as Record<string, any>
        if (responseObj.data && Array.isArray(responseObj.data)) {
          providersData = responseObj.data as LLMProvider[]
        } else if (responseObj.items && Array.isArray(responseObj.items)) {
          providersData = responseObj.items as LLMProvider[]
        } else if ('id' in responseObj) {
          providersData = [responseObj as LLMProvider]
        }
      }

      // Ensure providersData is always an array
      if (!Array.isArray(providersData)) {
        providersData = []
      }

      setProviders(providersData)
    } catch (error: any) {
      console.error('Error fetching providers:', error)

      // Check if it's a connection error
      const isConnectionError = error?.message?.includes('timed out') ||
                               error?.message?.includes('UnableToConnect') ||
                               error?.message?.includes('Redis') ||
                               error?.message?.includes('connection');

      if (isConnectionError) {
        toast({
          title: 'Connection Error',
          description: 'Unable to connect to the server. Showing cached data if available.',
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Error',
          description: 'Failed to fetch LLM providers',
          variant: 'destructive',
        })
      }

      // Set empty array on error
      setProviders([])
    } finally {
      setLoading(false)
    }
  }

  const handleCreateProvider = async (providerData: CreateProviderRequest) => {
    // Validación adicional
    if (providerData.baseUrl && !isValidUrl(providerData.baseUrl)) {
      toast({
        title: 'Error',
        description: 'La URL base no es válida',
        variant: 'destructive',
      })
      return
    }

    if (providerData.rateLimits) {
      if (providerData.rateLimits.requestsPerMinute <= 0 ||
          providerData.rateLimits.requestsPerHour <= 0 ||
          providerData.rateLimits.tokensPerMinute <= 0) {
        toast({
          title: 'Error',
          description: 'Los límites de tasa deben ser valores positivos',
          variant: 'destructive',
        })
        return
      }
    }

    try {
      await createLLMProvider(providerData)
      toast({
        title: 'Success',
        description: 'LLM Provider created successfully',
      })
      setShowCreateDialog(false)
      fetchProviders()
    } catch (error: any) {
      console.error('Error creating provider:', error)

      // Check if it's a connection error
      const isConnectionError = error?.message?.includes('timed out') ||
                               error?.message?.includes('UnableToConnect') ||
                               error?.message?.includes('Redis') ||
                               error?.message?.includes('connection');

      if (isConnectionError) {
        toast({
          title: 'Connection Error',
          description: 'Unable to connect to the server. Please check your connection and try again.',
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Error',
          description: 'Failed to create LLM provider',
          variant: 'destructive',
        })
      }
    }
  }

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  const handleUpdateProvider = async (providerId: string, providerData: Partial<LLMProvider>) => {
    try {
      await updateLLMProvider(providerId, providerData)
      toast({
        title: 'Success',
        description: 'LLM Provider updated successfully',
      })
      setEditingProvider(null)
      fetchProviders()
    } catch (error: any) {
      console.error('Error updating provider:', error)

      // Check if it's a connection error
      const isConnectionError = error?.message?.includes('timed out') ||
                               error?.message?.includes('UnableToConnect') ||
                               error?.message?.includes('Redis') ||
                               error?.message?.includes('connection');

      if (isConnectionError) {
        toast({
          title: 'Connection Error',
          description: 'Unable to connect to the server. The changes may have been applied locally.',
          variant: 'destructive',
        })
        // Still update the UI optimistically
        setProviders(prev => prev.map(provider =>
          provider.id === providerId
            ? { ...provider, ...providerData }
            : provider
        ))
        setEditingProvider(null)
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update LLM provider',
          variant: 'destructive',
        })
      }
    }
  }

  const handleDeleteProvider = async (providerId: string) => {
    try {
      await deleteLLMProvider(providerId)
      toast({
        title: 'Success',
        description: 'LLM Provider deleted successfully',
      })
      fetchProviders()
    } catch (error: any) {
      console.error('Error deleting provider:', error)

      // Check if it's a connection error
      const isConnectionError = error?.message?.includes('timed out') ||
                               error?.message?.includes('UnableToConnect') ||
                               error?.message?.includes('Redis') ||
                               error?.message?.includes('connection');

      if (isConnectionError) {
        toast({
          title: 'Connection Error',
          description: 'Unable to connect to the server. The provider may have been deleted locally.',
          variant: 'destructive',
        })
        // Still update the UI optimistically
        setProviders(prev => prev.filter(provider => provider.id !== providerId))
      } else {
        toast({
          title: 'Error',
          description: 'Failed to delete LLM provider',
          variant: 'destructive',
        })
      }
    }
  }

  const handleToggleProviderStatus = async (providerId: string, isActive: boolean) => {
    try {
      await updateLLMProvider(providerId, { isActive: !isActive })
      toast({
        title: 'Success',
        description: `Provider ${isActive ? 'deactivated' : 'activated'} successfully`,
      })
      fetchProviders()
    } catch (error: any) {
      console.error('Error toggling provider status:', error)

      // Check if it's a Redis/connection error
      const isConnectionError = error?.message?.includes('timed out') ||
                               error?.message?.includes('UnableToConnect') ||
                               error?.message?.includes('Redis') ||
                               error?.message?.includes('connection');

      if (isConnectionError) {
        toast({
          title: 'Connection Error',
          description: 'Unable to connect to the server. The change may have been applied locally.',
          variant: 'destructive',
        })
        // Still update the UI optimistically
        setProviders(prev => prev.map(provider =>
          provider.id === providerId
            ? { ...provider, isActive: !isActive }
            : provider
        ))
      } else {
        toast({
          title: 'Error',
          description: 'Failed to update provider status',
          variant: 'destructive',
        })
      }
    }
  }

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         provider.displayName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'active' && provider.isActive) ||
                         (filterStatus === 'inactive' && !provider.isActive)
    return matchesSearch && matchesStatus
  })

   const totalActiveProviders = Array.isArray(providers) ? providers.filter(p => p.isActive).length : 0
   const totalProviders = Array.isArray(providers) ? providers.length : 0
   const uniqueApiVersions = Array.isArray(providers) ? [...new Set(providers.map(p => p.apiVersion || 'N/A'))].length : 0

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Brain className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Gestión de LLM
              </h1>
              <p className="text-muted-foreground">
                Gestiona proveedores, modelos y credenciales de IA
              </p>
            </div>
          </div>
        </div>
      </div>

      <Tabs defaultValue="providers" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="providers">Proveedores</TabsTrigger>
          <TabsTrigger value="models">Modelos</TabsTrigger>
          <TabsTrigger value="credentials">Credenciales</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Proveedores LLM</h2>
              <p className="text-muted-foreground">
                Gestiona los proveedores de modelos de lenguaje
              </p>
            </div>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground">
                  <Plus className="mr-2 h-4 w-4" />
                  Crear Proveedor
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Crear Nuevo Proveedor</DialogTitle>
                  <DialogDescription>
                    Agregar un nuevo proveedor de LLM al sistema
                  </DialogDescription>
                </DialogHeader>
                <CreateProviderForm onSubmit={handleCreateProvider} />
          </DialogContent>
        </Dialog>

        {/* Edit Model Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Editar Modelo</DialogTitle>
              <DialogDescription>
                Modifica la configuración del modelo LLM
              </DialogDescription>
            </DialogHeader>
            {editingModel && (
              <EditModelForm
                model={editingModel}
                providers={providers}
                onSubmit={async (modelData) => {
                  try {
                    await updateLLMModel(editingModel.id, modelData)
                    toast({
                      title: "Modelo actualizado",
                      description: "El modelo ha sido actualizado exitosamente",
                    })
                    setShowEditDialog(false)
                    setEditingModel(null)
                    // Note: Models list refresh should be handled by ModelsTab component
                  } catch (error) {
                    console.error('Error updating model:', error)
                    toast({
                      title: "Error",
                      description: "No se pudo actualizar el modelo",
                      variant: "destructive",
                    })
                  }
                }}
                onCancel={() => {
                  setShowEditDialog(false)
                  setEditingModel(null)
                }}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary">Total Proveedores</p>
                <p className="text-3xl font-bold text-foreground">{totalProviders}</p>
              </div>
              <Brain className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-accent/5 to-accent/10 border-accent/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-accent">Activos</p>
                <p className="text-3xl font-bold text-foreground">{totalActiveProviders}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-accent" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-secondary/50 to-secondary/30 border-secondary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-foreground">Modelos</p>
                <p className="text-3xl font-bold text-foreground">{providers.reduce((acc, p) => acc + (p.supportedModels?.length || 0), 0)}</p>
              </div>
              <Zap className="h-8 w-8 text-secondary-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-muted/50 to-muted/30 border-muted/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Versiones API</p>
                <p className="text-3xl font-bold text-foreground">{uniqueApiVersions}</p>
              </div>
              <RefreshCw className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-card border-border">
        <CardHeader className="bg-gradient-to-r from-muted/20 to-muted/10">
          <CardTitle className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
            Gestión de Proveedores
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Visualiza y administra todos los proveedores de LLM
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar proveedores..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 bg-background border-border"
              />
            </div>

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[140px] border-primary/20">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="active">Activos</SelectItem>
                <SelectItem value="inactive">Inactivos</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center gap-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary border-t-transparent"></div>
                <p className="text-sm text-muted-foreground">Cargando proveedores...</p>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/30 hover:bg-muted/30">
                  <TableHead className="text-foreground">Proveedor</TableHead>
                  <TableHead className="text-foreground">API Version</TableHead>
                  <TableHead className="text-foreground">Modelos</TableHead>
                  <TableHead className="text-foreground">Estado</TableHead>
                  <TableHead className="text-foreground text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
               <TableBody>
                 {Array.isArray(filteredProviders) && filteredProviders.map((provider) => (
                  <TableRow key={provider.id} className="bg-card hover:bg-primary/5 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-primary-foreground font-semibold text-sm">
                          {provider.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-semibold text-foreground">{provider.displayName}</div>
                          <div className="text-sm text-muted-foreground">{provider.name}</div>
                          {provider.description && (
                            <div className="text-xs text-muted-foreground mt-1">{provider.description}</div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
                        {provider.apiVersion || 'v1'}
                      </Badge>
                    </TableCell>
                     <TableCell>
                       <div className="flex flex-wrap gap-1">
                         {Array.isArray(provider.supportedModels) && provider.supportedModels.length > 0 ? (
                           provider.supportedModels.slice(0, 2).map((model) => (
                             <Badge key={model} variant="outline" className="text-xs">
                               {model}
                             </Badge>
                           ))
                         ) : (
                           <span className="text-sm text-muted-foreground">Sin modelos</span>
                         )}
                         {Array.isArray(provider.supportedModels) && provider.supportedModels.length > 2 && (
                           <Badge variant="outline" className="text-xs">
                             +{provider.supportedModels.length - 2}
                           </Badge>
                         )}
                       </div>
                     </TableCell>
                    <TableCell>
                      <Badge variant={provider.isActive ? 'default' : 'secondary'} className={provider.isActive ? 'bg-accent/10 text-accent border-accent/20' : 'bg-destructive/10 text-destructive border-destructive/20'}>
                        {provider.isActive ? 'Activo' : 'Inactivo'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingProvider(provider)}
                          className="border-primary/20 text-primary hover:bg-primary/5"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleProviderStatus(provider.id, provider.isActive)}
                          className="border-accent/20 text-accent hover:bg-accent/5"
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline" className="border-destructive/20 text-destructive hover:bg-destructive/5">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>¿Eliminar proveedor?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Esta acción no se puede deshacer. El proveedor "{provider.displayName}" será eliminado permanentemente.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancelar</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteProvider(provider.id)}
                                className="bg-destructive hover:bg-destructive/90"
                              >
                                Eliminar
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {editingProvider && (
        <Dialog open={!!editingProvider} onOpenChange={() => setEditingProvider(null)}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Editar Proveedor</DialogTitle>
              <DialogDescription>
                Modificar la configuración del proveedor LLM
              </DialogDescription>
            </DialogHeader>
            <EditProviderForm
              provider={editingProvider}
              onSubmit={(data) => handleUpdateProvider(editingProvider.id, data)}
              onCancel={() => setEditingProvider(null)}
            />
          </DialogContent>
        </Dialog>
      )}
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <ModelsTab />
        </TabsContent>

        <TabsContent value="credentials" className="space-y-4">
          <CredentialsTab />
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Models Tab Component
function ModelsTab() {
  const [models, setModels] = useState<any[]>([])
  const [providers, setProviders] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProvider, setSelectedProvider] = useState<string>('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingModel, setEditingModel] = useState<any | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const { toast } = useToast()

  const sampleModels = [
    {
      id: '1',
      providerId: 'openai',
      modelId: 'gpt-4',
      displayName: 'GPT-4',
      capabilities: ['text-generation', 'conversation'],
      inputCostPer1K: 0.03,
      outputCostPer1K: 0.06,
      maxTokens: 8192,
      defaultParams: { temperature: 0.7, top_p: 1 },
      isActive: true,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      providerId: 'anthropic',
      modelId: 'claude-3-sonnet',
      displayName: 'Claude 3 Sonnet',
      capabilities: ['text-generation', 'conversation', 'analysis'],
      inputCostPer1K: 0.015,
      outputCostPer1K: 0.075,
      maxTokens: 200000,
      defaultParams: { temperature: 0.3, max_tokens: 4000 },
      isActive: true,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    }
  ]

  const sampleProviders = [
    { id: 'openai', name: 'openai', displayName: 'OpenAI', isActive: true },
    { id: 'anthropic', name: 'anthropic', displayName: 'Anthropic', isActive: true }
  ]

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await getLLMModels()
        const providersResponse = await getLLMProviders()

        if (Array.isArray(response)) {
          setModels(response)
        } else if (response && typeof response === 'object') {
          const responseObj = response as any
          if (responseObj.data && Array.isArray(responseObj.data)) {
            setModels(responseObj.data)
          } else {
            setModels(sampleModels)
          }
        } else {
          setModels(sampleModels)
        }

        if (Array.isArray(providersResponse)) {
          setProviders(providersResponse)
        } else if (providersResponse && typeof providersResponse === 'object') {
          const providersObj = providersResponse as any
          if (providersObj.data && Array.isArray(providersObj.data)) {
            setProviders(providersObj.data)
          } else {
            setProviders(sampleProviders)
          }
        } else {
          setProviders(sampleProviders)
        }
      } catch (error) {
        console.error('Error loading data:', error)
        setModels(sampleModels)
        setProviders(sampleProviders)
        toast({
          title: "Error",
          description: "Error al cargar datos, usando datos de ejemplo.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }
    loadData()
  }, [])

  const filteredModels = models.filter(model => {
    const matchesSearch = model.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         model.modelId?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesProvider = selectedProvider === 'all' || model.providerId === selectedProvider
    return matchesSearch && matchesProvider
  })

  const handleToggleActive = async (modelId: string, isActive: boolean) => {
    try {
      await updateLLMModel(modelId, { isActive })
      setModels(prev => prev.map(model =>
        model.id === modelId ? { ...model, isActive } : model
      ))
      toast({
        title: "Éxito",
        description: `Modelo ${isActive ? 'activado' : 'desactivado'} correctamente`,
      })
    } catch (error) {
      console.error('Error toggling model status:', error)
      toast({
        title: "Error",
        description: "Error al actualizar el modelo",
        variant: "destructive",
      })
    }
  }

  const getCapabilityColor = (capability: string) => {
    const colors: Record<string, string> = {
      'text-generation': 'bg-primary/10 text-primary border-primary/20',
      'conversation': 'bg-accent/10 text-accent border-accent/20',
      'analysis': 'bg-secondary/20 text-secondary-foreground border-secondary/30',
      'multimodal': 'bg-warning/10 text-warning border-warning/20',
      'code-generation': 'bg-muted/30 text-muted-foreground border-muted/40'
    }
    return colors[capability] || 'bg-muted/30 text-muted-foreground border-muted/40'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary border-t-transparent"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Modelos LLM</h2>
          <p className="text-muted-foreground">
            Gestiona los modelos de lenguaje disponibles en el sistema
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nuevo Modelo
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Crear Nuevo Modelo</DialogTitle>
              <DialogDescription>
                Configura un nuevo modelo LLM para el sistema
              </DialogDescription>
            </DialogHeader>
            <CreateModelForm
              providers={providers}
              onSubmit={async (modelData) => {
                try {
                  await createLLMModel(modelData)
                  toast({
                    title: "Modelo creado",
                    description: "El modelo ha sido creado exitosamente",
                  })
                  setShowCreateDialog(false)
                  // Refresh models list
                  const response = await getLLMModels()
                  if (Array.isArray(response)) {
                    setModels(response)
                  } else if (response && typeof response === 'object') {
                    const responseObj = response as any
                    if (responseObj.data && Array.isArray(responseObj.data)) {
                      setModels(responseObj.data)
                    }
                  }
                } catch (error) {
                  console.error('Error creating model:', error)
                  toast({
                    title: "Error",
                    description: "No se pudo crear el modelo",
                    variant: "destructive",
                  })
                }
              }}
              onCancel={() => setShowCreateDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Buscar modelos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedProvider} onValueChange={setSelectedProvider}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Todos los proveedores</SelectItem>
                          {Array.isArray(providers) && providers.map(provider => (
                            <SelectItem key={provider.id} value={provider.id}>
                              {provider.displayName}
                            </SelectItem>
                          ))}
                        </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Modelos Disponibles</CardTitle>
          <CardDescription>
            {filteredModels.length} modelo(s) encontrado(s)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Modelo</TableHead>
                <TableHead>Proveedor</TableHead>
                <TableHead>Capacidades</TableHead>
                <TableHead>Costos</TableHead>
                <TableHead>Tokens Máx.</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredModels.map((model) => {
                const provider = providers.find(p => p.id === model.providerId)
                return (
                  <TableRow key={model.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{model.displayName}</div>
                        <div className="text-sm text-muted-foreground">{model.modelId}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{provider?.displayName || model.providerId}</Badge>
                    </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {Array.isArray(model.capabilities) && model.capabilities.map((capability: string) => (
                            <Badge
                              key={capability}
                              variant="secondary"
                              className={`text-xs ${getCapabilityColor(capability)}`}
                            >
                              {capability}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          <span>In: ${model.inputCostPer1K}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          <span>Out: ${model.outputCostPer1K}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span className="text-sm">{model.maxTokens?.toLocaleString()}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={model.isActive}
                          onCheckedChange={(checked) => handleToggleActive(model.id, checked)}
                        />
                        <span className="text-sm">
                          {model.isActive ? 'Activo' : 'Inactivo'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingModel(model)
                            setShowEditDialog(true)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-destructive hover:text-destructive/80"
                          onClick={async () => {
                            if (confirm('¿Estás seguro de que quieres eliminar este modelo?')) {
                              try {
                                await deleteLLMModel(model.id)
                                setModels(prev => prev.filter(m => m.id !== model.id))
                                toast({
                                  title: "Modelo eliminado",
                                  description: "El modelo ha sido eliminado exitosamente",
                                })
                              } catch (error) {
                                console.error('Error deleting model:', error)
                                toast({
                                  title: "Error",
                                  description: "No se pudo eliminar el modelo",
                                  variant: "destructive",
                                })
                              }
                            }
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}

// Credentials Tab Component
function CredentialsTab() {
  const { toast } = useToast()
  const [credentials, setCredentials] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [selectedCredential, setSelectedCredential] = useState<any | null>(null)
  const [editFormData, setEditFormData] = useState({
    name: '',
    provider: '',
    description: '',
    isActive: true
  })
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [filterProvider, setFilterProvider] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  const mockCredentials = [
    {
      id: '1',
      name: 'OpenAI Production',
      provider: 'OpenAI',
      apiKey: 'sk-proj-***************************',
      isActive: true,
      createdAt: '2024-01-10T10:00:00Z',
      lastUsed: '2024-01-15T09:30:00Z',
      usageCount: 15420,
      isValid: true,
      expiresAt: '2024-12-31T23:59:59Z',
      description: 'Credencial principal para GPT-4 en producción'
    },
    {
      id: '2',
      name: 'Anthropic Claude',
      provider: 'Anthropic',
      apiKey: 'sk-ant-***************************',
      isActive: true,
      createdAt: '2024-01-08T14:20:00Z',
      lastUsed: '2024-01-14T16:45:00Z',
      usageCount: 8750,
      isValid: true,
      description: 'Credencial para Claude 3.5 Sonnet'
    }
  ]

  const providers = [
    'OpenAI',
    'Anthropic',
    'Google',
    'Azure OpenAI',
    'Cohere',
    'Hugging Face',
    'Replicate'
  ]

  useEffect(() => {
    fetchCredentials()
  }, [])

  const fetchCredentials = async () => {
    try {
      const response = await getLLMCredentials()
      if (Array.isArray(response)) {
        setCredentials(response)
      } else if (response && typeof response === 'object') {
        const responseObj = response as any
        if (responseObj.data && Array.isArray(responseObj.data)) {
          setCredentials(responseObj.data)
        } else {
          setCredentials(mockCredentials)
        }
      } else {
        setCredentials(mockCredentials)
      }
    } catch (error) {
      console.error('Error fetching credentials:', error)
      setCredentials(mockCredentials)
      toast({
        title: "Error",
        description: "Error al cargar credenciales, usando datos locales.",
        variant: "destructive",
      })
    }
  }

  const handleEditCredential = async () => {
    if (!selectedCredential) return

    try {
      await updateLLMCredential(selectedCredential.id, {
        name: editFormData.name,
        provider: editFormData.provider,
        description: editFormData.description,
        isActive: editFormData.isActive
      })

      setShowEditDialog(false)
      setSelectedCredential(null)

      toast({
        title: "Credencial actualizada",
        description: "La credencial ha sido actualizada exitosamente.",
      })

      // Refresh credentials list
      fetchCredentials()
    } catch (error) {
      console.error('Error updating credential:', error)
      toast({
        title: "Error",
        description: "No se pudo actualizar la credencial.",
        variant: "destructive",
      })
    }
  }

  const openEditDialog = (credential: any) => {
    setSelectedCredential(credential)
    setEditFormData({
      name: credential.name,
      provider: credential.provider,
      description: credential.description || '',
      isActive: credential.isActive
    })
    setShowEditDialog(true)
  }

  const filteredCredentials = credentials.filter(credential => {
    const matchesSearch = credential.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         credential.provider?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesProvider = filterProvider === 'all' || credential.provider === filterProvider
    const matchesStatus = filterStatus === 'all' ||
                         (filterStatus === 'active' && credential.isActive) ||
                         (filterStatus === 'inactive' && !credential.isActive) ||
                         (filterStatus === 'valid' && credential.isValid) ||
                         (filterStatus === 'invalid' && !credential.isValid)

    return matchesSearch && matchesProvider && matchesStatus
  })

  const handleToggleKeyVisibility = (credentialId: string) => {
    const newVisibleKeys = new Set(visibleKeys)
    if (newVisibleKeys.has(credentialId)) {
      newVisibleKeys.delete(credentialId)
    } else {
      newVisibleKeys.add(credentialId)
    }
    setVisibleKeys(newVisibleKeys)
  }

  const handleCopyKey = async (apiKey: string) => {
    try {
      await navigator.clipboard.writeText(apiKey)
      toast({
        title: "Copiado",
        description: "La clave API ha sido copiada al portapapeles.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo copiar la clave API.",
        variant: "destructive",
      })
    }
  }

  const handleTestCredential = async (credentialId: string) => {
    setIsLoading(true)
    try {
      const result = await testLLMCredential(credentialId)
      const isValid = result && (result as any).isValid !== false
      setCredentials(credentials.map(cred =>
        cred.id === credentialId ? { ...cred, isValid } : cred
      ))

      toast({
        title: isValid ? "Credencial válida" : "Credencial inválida",
        description: isValid
          ? "La credencial funciona correctamente."
          : "La credencial no es válida o ha expirado.",
        variant: isValid ? "default" : "destructive",
      })
    } catch (error) {
      console.error('Error testing credential:', error)
      toast({
        title: "Error",
        description: "No se pudo probar la credencial. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const maskApiKey = (apiKey: string): string => {
    if (apiKey.length <= 8) return '*'.repeat(apiKey.length)
    return apiKey.substring(0, 8) + '*'.repeat(Math.max(0, apiKey.length - 8))
  }

  const getStatusBadge = (credential: any) => {
    if (!credential.isActive) {
      return <Badge variant="secondary">Inactiva</Badge>
    }
    if (!credential.isValid) {
      return <Badge variant="destructive">Inválida</Badge>
    }
    return <Badge variant="default" className="bg-accent text-accent-foreground">Activa</Badge>
  }

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'openai':
        return '🤖'
      case 'anthropic':
        return '🧠'
      case 'google':
        return '🔍'
      case 'azure openai':
        return '☁️'
      default:
        return '🔧'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Credenciales LLM</h2>
          <p className="text-muted-foreground">
            Gestiona las credenciales de API para proveedores de LLM
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nueva Credencial
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Crear Nueva Credencial</DialogTitle>
              <DialogDescription>
                Agrega una nueva credencial de API para un proveedor LLM
              </DialogDescription>
            </DialogHeader>
            <CreateCredentialForm
              providers={['OpenAI', 'Anthropic', 'Google', 'Azure OpenAI', 'Cohere', 'Hugging Face', 'Replicate']}
              onSubmit={async (credentialData) => {
                try {
                  await createLLMCredential(credentialData)
                  toast({
                    title: "Credencial creada",
                    description: "La credencial ha sido creada exitosamente",
                  })
                  setShowCreateDialog(false)
                  // Refresh credentials list
                  fetchCredentials()
                } catch (error) {
                  console.error('Error creating credential:', error)
                  toast({
                    title: "Error",
                    description: "No se pudo crear la credencial",
                    variant: "destructive",
                  })
                }
              }}
              onCancel={() => setShowCreateDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Buscar</Label>
              <Input
                id="search"
                placeholder="Buscar por nombre o proveedor..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="provider-filter">Proveedor</Label>
              <Select value={filterProvider} onValueChange={setFilterProvider}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los proveedores</SelectItem>
                  {providers.map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      {getProviderIcon(provider)} {provider}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status-filter">Estado</Label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="active">Activas</SelectItem>
                  <SelectItem value="inactive">Inactivas</SelectItem>
                  <SelectItem value="valid">Válidas</SelectItem>
                  <SelectItem value="invalid">Inválidas</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button variant="outline" className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualizar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Credenciales ({filteredCredentials.length})
          </CardTitle>
          <CardDescription>
            Lista de todas las credenciales de API configuradas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nombre</TableHead>
                  <TableHead>Proveedor</TableHead>
                  <TableHead>Clave API</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead className="text-right">Uso</TableHead>
                  <TableHead>Último Uso</TableHead>
                  <TableHead>Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCredentials.map((credential) => (
                  <TableRow key={credential.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{credential.name}</div>
                        {credential.description && (
                          <div className="text-sm text-muted-foreground">
                            {credential.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span>{getProviderIcon(credential.provider)}</span>
                        <span>{credential.provider}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <code className="text-sm font-mono">
                          {visibleKeys.has(credential.id)
                            ? credential.apiKey
                            : maskApiKey(credential.apiKey)
                          }
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleToggleKeyVisibility(credential.id)}
                        >
                          {visibleKeys.has(credential.id) ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleCopyKey(credential.apiKey)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(credential)}
                    </TableCell>
                    <TableCell className="text-right">
                      {credential.usageCount?.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      {credential.lastUsed ? (
                        <div className="text-sm">
                          {new Date(credential.lastUsed).toLocaleDateString('es-ES')}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Nunca</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTestCredential(credential.id)}
                          disabled={isLoading}
                        >
                          <Zap className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => openEditDialog(credential)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>¿Eliminar credencial?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Esta acción no se puede deshacer. La credencial "{credential.name}" será eliminada permanentemente.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancelar</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={async () => {
                                  try {
                                    await deleteLLMCredential(credential.id)
                                    setCredentials(credentials.filter(cred => cred.id !== credential.id))
                                    toast({
                                      title: "Credencial eliminada",
                                      description: "La credencial ha sido eliminada exitosamente.",
                                    })
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo eliminar la credencial.",
                                      variant: "destructive",
                                    })
                                  }
                                }}
                                className="bg-destructive hover:bg-destructive/90"
                              >
                                Eliminar
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit Credential Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Credencial</DialogTitle>
            <DialogDescription>
              Modifica los detalles de la credencial
            </DialogDescription>
          </DialogHeader>
          {selectedCredential && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Nombre</Label>
                <Input
                  id="edit-name"
                  value={editFormData.name}
                  onChange={(e) => setEditFormData({
                    ...editFormData,
                    name: e.target.value
                  })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-provider">Proveedor</Label>
                <Select
                  value={editFormData.provider}
                  onValueChange={(value) => setEditFormData({
                    ...editFormData,
                    provider: value
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {['OpenAI', 'Anthropic', 'Google', 'Azure OpenAI', 'Cohere', 'Hugging Face', 'Replicate'].map((provider) => (
                      <SelectItem key={provider} value={provider}>
                        {getProviderIcon(provider)} {provider}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Descripción</Label>
                <Input
                  id="edit-description"
                  value={editFormData.description}
                  onChange={(e) => setEditFormData({
                    ...editFormData,
                    description: e.target.value
                  })}
                />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit-active"
                  checked={editFormData.isActive}
                  onChange={(e) => setEditFormData({
                    ...editFormData,
                    isActive: e.target.checked
                  })}
                  className="rounded"
                />
                <Label htmlFor="edit-active">Credencial activa</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowEditDialog(false)
              setSelectedCredential(null)
            }}>
              Cancelar
            </Button>
            <Button onClick={handleEditCredential}>
              Guardar Cambios
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Create Model Form Component
function CreateModelForm({ providers, onSubmit, onCancel }: {
  providers: any[],
  onSubmit: (data: any) => void,
  onCancel: () => void
}) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    providerId: '',
    modelId: '',
    displayName: '',
    capabilities: [] as string[],
    inputCostPer1K: 0,
    outputCostPer1K: 0,
    maxTokens: 0,
    defaultParams: {},
    isActive: true
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Validación
    if (!formData.providerId) {
      toast({ title: "Error", description: "Por favor selecciona un proveedor", variant: "destructive" })
      return
    }
    if (!formData.modelId.trim()) {
      toast({ title: "Error", description: "Por favor ingresa el ID del modelo", variant: "destructive" })
      return
    }
    if (!formData.displayName.trim()) {
      toast({ title: "Error", description: "Por favor ingresa el nombre para mostrar", variant: "destructive" })
      return
    }
    if (formData.inputCostPer1K < 0) {
      toast({ title: "Error", description: "El costo de input debe ser positivo", variant: "destructive" })
      return
    }
    if (formData.outputCostPer1K < 0) {
      toast({ title: "Error", description: "El costo de output debe ser positivo", variant: "destructive" })
      return
    }
    if (formData.maxTokens <= 0) {
      toast({ title: "Error", description: "El máximo de tokens debe ser mayor a 0", variant: "destructive" })
      return
    }

    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="provider">Proveedor</Label>
          <Select
            value={formData.providerId}
            onValueChange={(value) => setFormData({ ...formData, providerId: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Seleccionar proveedor" />
            </SelectTrigger>
            <SelectContent>
              {Array.isArray(providers) && providers.map(provider => (
                <SelectItem key={provider.id} value={provider.id}>
                  {provider.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="modelId">ID del Modelo</Label>
          <Input
            id="modelId"
            placeholder="gpt-4, claude-3-sonnet, etc."
            value={formData.modelId}
            onChange={(e) => setFormData({ ...formData, modelId: e.target.value })}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="displayName">Nombre para Mostrar</Label>
        <Input
          id="displayName"
          placeholder="GPT-4, Claude 3 Sonnet, etc."
          value={formData.displayName}
          onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Capacidades</Label>
        <div className="grid grid-cols-2 gap-2">
          {['text-generation', 'conversation', 'analysis', 'multimodal', 'code-generation', 'image-generation'].map((capability) => (
            <div key={capability} className="flex items-center space-x-2">
              <input
                type="checkbox"
                id={`capability-${capability}`}
                checked={formData.capabilities.includes(capability)}
                onChange={(e) => {
                  const updatedCapabilities = e.target.checked
                    ? [...formData.capabilities, capability]
                    : formData.capabilities.filter((c: string) => c !== capability);
                  setFormData({ ...formData, capabilities: updatedCapabilities });
                }}
                className="rounded"
              />
              <Label htmlFor={`capability-${capability}`} className="text-sm capitalize">
                {capability.replace('-', ' ')}
              </Label>
            </div>
          ))}
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="inputCost">Costo Input (por 1K tokens)</Label>
          <Input
            id="inputCost"
            type="number"
            step="0.001"
            placeholder="0.03"
            value={formData.inputCostPer1K}
            onChange={(e) => setFormData({ ...formData, inputCostPer1K: parseFloat(e.target.value) || 0 })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="outputCost">Costo Output (por 1K tokens)</Label>
          <Input
            id="outputCost"
            type="number"
            step="0.001"
            placeholder="0.06"
            value={formData.outputCostPer1K}
            onChange={(e) => setFormData({ ...formData, outputCostPer1K: parseFloat(e.target.value) || 0 })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="maxTokens">Máximo Tokens</Label>
          <Input
            id="maxTokens"
            type="number"
            placeholder="8192"
            value={formData.maxTokens}
            onChange={(e) => setFormData({ ...formData, maxTokens: parseInt(e.target.value) || 0 })}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="defaultParams">Parámetros por Defecto (JSON)</Label>
        <Textarea
          id="defaultParams"
          placeholder='{"temperature": 0.7, "top_p": 1}'
          className="font-mono text-sm"
          value={JSON.stringify(formData.defaultParams, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value || '{}');
              setFormData({ ...formData, defaultParams: parsed });
            } catch (error) {
              // Keep current value if JSON is invalid
            }
          }}
        />
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="isActive">Modelo activo</Label>
      </div>
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit">Crear Modelo</Button>
      </div>
    </form>
  )
}

// Edit Model Form Component
function EditModelForm({ model, providers, onSubmit, onCancel }: {
  model: any,
  providers: any[],
  onSubmit: (data: any) => void,
  onCancel: () => void
}) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    providerId: model.providerId || '',
    modelId: model.modelId || '',
    displayName: model.displayName || '',
    capabilities: model.capabilities || [],
    inputCostPer1K: model.inputCostPer1K || 0,
    outputCostPer1K: model.outputCostPer1K || 0,
    maxTokens: model.maxTokens || 0,
    defaultParams: model.defaultParams || {},
    isActive: model.isActive ?? true
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Validación
    if (!formData.providerId) {
      toast({ title: "Error", description: "Por favor selecciona un proveedor", variant: "destructive" })
      return
    }
    if (!formData.modelId.trim()) {
      toast({ title: "Error", description: "Por favor ingresa el ID del modelo", variant: "destructive" })
      return
    }
    if (!formData.displayName.trim()) {
      toast({ title: "Error", description: "Por favor ingresa el nombre para mostrar", variant: "destructive" })
      return
    }
    if (formData.inputCostPer1K < 0) {
      toast({ title: "Error", description: "El costo de input debe ser positivo", variant: "destructive" })
      return
    }
    if (formData.outputCostPer1K < 0) {
      toast({ title: "Error", description: "El costo de output debe ser positivo", variant: "destructive" })
      return
    }
    if (formData.maxTokens <= 0) {
      toast({ title: "Error", description: "El máximo de tokens debe ser mayor a 0", variant: "destructive" })
      return
    }

    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-provider">Proveedor</Label>
          <Select
            value={formData.providerId}
            onValueChange={(value) => setFormData({ ...formData, providerId: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Seleccionar proveedor" />
            </SelectTrigger>
            <SelectContent>
              {Array.isArray(providers) && providers.map(provider => (
                <SelectItem key={provider.id} value={provider.id}>
                  {provider.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-modelId">ID del Modelo</Label>
          <Input
            id="edit-modelId"
            placeholder="gpt-4, claude-3-sonnet, etc."
            value={formData.modelId}
            onChange={(e) => setFormData({ ...formData, modelId: e.target.value })}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="edit-displayName">Nombre para Mostrar</Label>
        <Input
          id="edit-displayName"
          placeholder="GPT-4, Claude 3 Sonnet, etc."
          value={formData.displayName}
          onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
        />
      </div>
      <div className="space-y-2">
        <Label>Capacidades</Label>
        <div className="grid grid-cols-2 gap-2">
          {['text-generation', 'conversation', 'analysis', 'multimodal', 'code-generation', 'image-generation'].map((capability) => (
            <div key={capability} className="flex items-center space-x-2">
              <input
                type="checkbox"
                id={`edit-capability-${capability}`}
                checked={formData.capabilities.includes(capability)}
                onChange={(e) => {
                  const updatedCapabilities = e.target.checked
                    ? [...formData.capabilities, capability]
                    : formData.capabilities.filter((c: string) => c !== capability);
                  setFormData({ ...formData, capabilities: updatedCapabilities });
                }}
                className="rounded"
              />
              <Label htmlFor={`edit-capability-${capability}`} className="text-sm capitalize">
                {capability.replace('-', ' ')}
              </Label>
            </div>
          ))}
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-inputCost">Costo Input (por 1K tokens)</Label>
          <Input
            id="edit-inputCost"
            type="number"
            step="0.001"
            placeholder="0.03"
            value={formData.inputCostPer1K}
            onChange={(e) => setFormData({ ...formData, inputCostPer1K: parseFloat(e.target.value) || 0 })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-outputCost">Costo Output (por 1K tokens)</Label>
          <Input
            id="edit-outputCost"
            type="number"
            step="0.001"
            placeholder="0.06"
            value={formData.outputCostPer1K}
            onChange={(e) => setFormData({ ...formData, outputCostPer1K: parseFloat(e.target.value) || 0 })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-maxTokens">Máximo Tokens</Label>
          <Input
            id="edit-maxTokens"
            type="number"
            placeholder="8192"
            value={formData.maxTokens}
            onChange={(e) => setFormData({ ...formData, maxTokens: parseInt(e.target.value) || 0 })}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="edit-defaultParams">Parámetros por Defecto (JSON)</Label>
        <Textarea
          id="edit-defaultParams"
          placeholder='{"temperature": 0.7, "top_p": 1}'
          className="font-mono text-sm"
          value={JSON.stringify(formData.defaultParams, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value || '{}');
              setFormData({ ...formData, defaultParams: parsed });
            } catch (error) {
              // Keep current value if JSON is invalid
            }
          }}
        />
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="edit-isActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="edit-isActive">Modelo activo</Label>
      </div>
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit">Guardar Cambios</Button>
      </div>
    </form>
  )
}

// Create Credential Form Component
function CreateCredentialForm({ providers, onSubmit, onCancel }: {
  providers: string[],
  onSubmit: (data: any) => void,
  onCancel: () => void
}) {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    name: '',
    provider: '',
    apiKey: '',
    description: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Validación
    if (!formData.name.trim()) {
      toast({ title: "Error", description: "Por favor ingresa el nombre", variant: "destructive" })
      return
    }
    if (!formData.provider) {
      toast({ title: "Error", description: "Por favor selecciona un proveedor", variant: "destructive" })
      return
    }
    if (!formData.apiKey.trim()) {
      toast({ title: "Error", description: "Por favor ingresa la clave API", variant: "destructive" })
      return
    }

    onSubmit(formData)
  }

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'openai':
        return '🤖'
      case 'anthropic':
        return '🧠'
      case 'google':
        return '🔍'
      case 'azure openai':
        return '☁️'
      default:
        return '🔧'
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid gap-2">
        <Label htmlFor="name">Nombre *</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Ej: OpenAI Production"
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="provider">Proveedor *</Label>
        <Select
          value={formData.provider}
          onValueChange={(value) => setFormData({ ...formData, provider: value })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Selecciona un proveedor" />
          </SelectTrigger>
          <SelectContent>
            {providers.map((provider) => (
              <SelectItem key={provider} value={provider}>
                {getProviderIcon(provider)} {provider}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="grid gap-2">
        <Label htmlFor="apiKey">Clave API *</Label>
        <Input
          id="apiKey"
          type="password"
          value={formData.apiKey}
          onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
          placeholder="Ingresa la clave API"
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="description">Descripción</Label>
        <Input
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Descripción opcional"
        />
      </div>
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit">Crear Credencial</Button>
      </div>
    </form>
  )
}

function CreateProviderForm({ onSubmit }: { onSubmit: (data: CreateProviderRequest) => void }) {
  const [formData, setFormData] = useState<CreateProviderRequest>({
    name: '',
    displayName: '',
    baseUrl: '',
    apiVersion: 'v1',
    description: '',
    rateLimits: {
      requestsPerMinute: 60,
      requestsPerHour: 1000,
      tokensPerMinute: 100000
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Nombre del Proveedor *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="openai, anthropic, google"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="displayName">Nombre para Mostrar *</Label>
          <Input
            id="displayName"
            value={formData.displayName}
            onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
            placeholder="OpenAI, Anthropic, Google"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="baseUrl">URL Base</Label>
          <Input
            id="baseUrl"
            value={formData.baseUrl}
            onChange={(e) => setFormData({ ...formData, baseUrl: e.target.value })}
            placeholder="https://api.openai.com"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="apiVersion">Versión API</Label>
          <Select
            value={formData.apiVersion}
            onValueChange={(value) => setFormData({ ...formData, apiVersion: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="v1">v1</SelectItem>
              <SelectItem value="v2">v2</SelectItem>
              <SelectItem value="v3">v3</SelectItem>
              <SelectItem value="beta">beta</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Descripción</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Descripción del proveedor LLM"
          rows={3}
        />
      </div>

       <div className="space-y-3">
         <Label>Límites de Tasa (Rate Limits)</Label>

         {/* Presets de Rate Limits */}
         <div className="flex gap-2 mb-3">
           <Button
             type="button"
             variant="outline"
             size="sm"
             onClick={() => setFormData({
               ...formData,
               rateLimits: { requestsPerMinute: 60, requestsPerHour: 1000, tokensPerMinute: 100000 }
             })}
           >
             Básico
           </Button>
           <Button
             type="button"
             variant="outline"
             size="sm"
             onClick={() => setFormData({
               ...formData,
               rateLimits: { requestsPerMinute: 300, requestsPerHour: 5000, tokensPerMinute: 500000 }
             })}
           >
             Pro
           </Button>
           <Button
             type="button"
             variant="outline"
             size="sm"
             onClick={() => setFormData({
               ...formData,
               rateLimits: { requestsPerMinute: 1000, requestsPerHour: 20000, tokensPerMinute: 2000000 }
             })}
           >
             Enterprise
           </Button>
         </div>

         <div className="grid grid-cols-3 gap-4">
           <div className="space-y-2">
             <Label htmlFor="rpm" className="text-xs">Requests/min</Label>
             <Input
               id="rpm"
               type="number"
               min="1"
               value={formData.rateLimits?.requestsPerMinute || ''}
               onChange={(e) => setFormData({
                 ...formData,
                 rateLimits: {
                   ...formData.rateLimits!,
                   requestsPerMinute: parseInt(e.target.value) || 0
                 }
               })}
             />
           </div>
           <div className="space-y-2">
             <Label htmlFor="rph" className="text-xs">Requests/hour</Label>
             <Input
               id="rph"
               type="number"
               min="1"
               value={formData.rateLimits?.requestsPerHour || ''}
               onChange={(e) => setFormData({
                 ...formData,
                 rateLimits: {
                   ...formData.rateLimits!,
                   requestsPerHour: parseInt(e.target.value) || 0
                 }
               })}
             />
           </div>
           <div className="space-y-2">
             <Label htmlFor="tpm" className="text-xs">Tokens/min</Label>
             <Input
               id="tpm"
               type="number"
               min="1"
               value={formData.rateLimits?.tokensPerMinute || ''}
               onChange={(e) => setFormData({
                 ...formData,
                 rateLimits: {
                   ...formData.rateLimits!,
                   tokensPerMinute: parseInt(e.target.value) || 0
                 }
               })}
             />
           </div>
         </div>
       </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={() => setFormData({
          name: '',
          displayName: '',
          baseUrl: '',
          apiVersion: 'v1',
          description: '',
          rateLimits: {
            requestsPerMinute: 60,
            requestsPerHour: 1000,
            tokensPerMinute: 100000
          }
        })}>
          Limpiar
        </Button>
        <Button type="submit">Crear Proveedor</Button>
      </div>
    </form>
  )
}

function EditProviderForm({
  provider,
  onSubmit,
  onCancel
}: {
  provider: LLMProvider
  onSubmit: (data: Partial<LLMProvider>) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    name: provider.name,
    displayName: provider.displayName,
    baseUrl: provider.baseUrl || '',
    apiVersion: provider.apiVersion || 'v1',
    description: provider.description || '',
    isActive: provider.isActive,
    rateLimits: provider.rateLimits || {
      requestsPerMinute: 60,
      requestsPerHour: 1000,
      tokensPerMinute: 100000
    }
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-name">Nombre del Proveedor</Label>
          <Input
            id="edit-name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-displayName">Nombre para Mostrar</Label>
          <Input
            id="edit-displayName"
            value={formData.displayName}
            onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-baseUrl">URL Base</Label>
          <Input
            id="edit-baseUrl"
            value={formData.baseUrl}
            onChange={(e) => setFormData({ ...formData, baseUrl: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-apiVersion">Versión API</Label>
          <Select
            value={formData.apiVersion}
            onValueChange={(value) => setFormData({ ...formData, apiVersion: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="v1">v1</SelectItem>
              <SelectItem value="v2">v2</SelectItem>
              <SelectItem value="v3">v3</SelectItem>
              <SelectItem value="beta">beta</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="edit-description">Descripción</Label>
        <Textarea
          id="edit-description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          rows={3}
        />
      </div>

      <div className="space-y-3">
        <Label>Límites de Tasa (Rate Limits)</Label>
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="edit-rpm" className="text-xs">Requests/min</Label>
            <Input
              id="edit-rpm"
              type="number"
              value={formData.rateLimits.requestsPerMinute}
              onChange={(e) => setFormData({
                ...formData,
                rateLimits: {
                  ...formData.rateLimits,
                  requestsPerMinute: parseInt(e.target.value) || 0
                }
              })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-rph" className="text-xs">Requests/hour</Label>
            <Input
              id="edit-rph"
              type="number"
              value={formData.rateLimits.requestsPerHour}
              onChange={(e) => setFormData({
                ...formData,
                rateLimits: {
                  ...formData.rateLimits,
                  requestsPerHour: parseInt(e.target.value) || 0
                }
              })}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="edit-tpm" className="text-xs">Tokens/min</Label>
            <Input
              id="edit-tpm"
              type="number"
              value={formData.rateLimits.tokensPerMinute}
              onChange={(e) => setFormData({
                ...formData,
                rateLimits: {
                  ...formData.rateLimits,
                  tokensPerMinute: parseInt(e.target.value) || 0
                }
              })}
            />
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="edit-isActive"
          checked={formData.isActive}
          onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
          className="rounded"
        />
        <Label htmlFor="edit-isActive">Proveedor activo</Label>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancelar
        </Button>
        <Button type="submit">Guardar Cambios</Button>
      </div>
    </form>
  )
}