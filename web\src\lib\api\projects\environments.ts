import { fetchApi } from '../core/fetch';
import type { Environment, EnvironmentCreateInput, EnvironmentUpdateInput } from '@/lib/types';

/**
 * Environment Management API Module
 *
 * Functions for managing test environments including creation, configuration,
 * testing, and default environment management.
 *
 * @module EnvironmentAPI
 */

/**
 * Get all environments for a specific project
 *
 * @param projectId - Unique project identifier
 * @returns Promise<Environment[]> Array of project environments
 */
export const getProjectEnvironments = async (projectId: string): Promise<Environment[]> => {
  const response = await fetchApi<Environment[]>(`/projects/${encodeURIComponent(projectId)}/environments`);
  return response;
};

/**
 * Get a specific environment by ID
 *
 * @param projectId - Unique project identifier
 * @param envId - Unique environment identifier
 * @returns Promise<Environment> Environment details
 */
export const getProjectEnvironment = async (projectId: string, envId: string): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}`);
};

/**
 * Create a new environment for a project
 *
 * @param projectId - Unique project identifier
 * @param environment - Environment creation data
 * @returns Promise<Environment> Created environment
 */
export const createProjectEnvironment = async (
  projectId: string,
  environment: EnvironmentCreateInput
): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments`, {
    method: 'POST',
    body: JSON.stringify(environment),
  });
};

/**
 * Update an existing environment
 *
 * @param projectId - Unique project identifier
 * @param envId - Unique environment identifier
 * @param environment - Updated environment data
 * @returns Promise<Environment> Updated environment
 */
export const updateProjectEnvironment = async (
  projectId: string,
  envId: string,
  environment: EnvironmentUpdateInput
): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}`, {
    method: 'PUT',
    body: JSON.stringify(environment),
  });
};

/**
 * Delete an environment
 *
 * @param projectId - Unique project identifier
 * @param envId - Unique environment identifier
 * @returns Promise<{ message: string }> Deletion confirmation
 */
export const deleteProjectEnvironment = async (projectId: string, envId: string): Promise<{ message: string }> => {
  return fetchApi<{ message: string }>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}`, {
    method: 'DELETE',
  });
};

/**
 * Set an environment as the default for a project
 *
 * @param projectId - Unique project identifier
 * @param envId - Unique environment identifier
 * @returns Promise<{ message: string }> Operation confirmation
 */
export const setDefaultEnvironment = async (projectId: string, envId: string): Promise<{ message: string }> => {
  return fetchApi<{ message: string }>(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}/set-default`, {
    method: 'POST',
  });
};

/**
 * Get the default environment for a project
 *
 * @param projectId - Unique project identifier
 * @returns Promise<Environment> Default environment details
 */
export const getDefaultEnvironment = async (projectId: string): Promise<Environment> => {
  return fetchApi<Environment>(`/projects/${encodeURIComponent(projectId)}/environments/default`);
};

/**
 * Get environments filtered by tag
 *
 * @param projectId - Unique project identifier
 * @param tag - Tag to filter environments
 * @returns Promise<Environment[]> Environments with the specified tag
 */
export const getEnvironmentsByTag = async (projectId: string, tag: string): Promise<Environment[]> => {
  const response = await fetchApi<{ items?: Environment[] }>(`/projects/${encodeURIComponent(projectId)}/environments/by-tag/${encodeURIComponent(tag)}`);
  return response.items || [];
};

/**
 * Test environment URL accessibility
 *
 * @param projectId - Unique project identifier
 * @param envId - Unique environment identifier
 * @param relativePath - Relative path to test (default: '/')
 * @returns Promise with environment details and accessibility status
 */
export const testEnvironmentUrl = async (
  projectId: string,
  envId: string,
  relativePath: string = '/'
): Promise<{
  environment_id: string;
  environment_name: string;
  full_url: string;
  accessible: boolean;
  status_code?: number;
}> => {
  return fetchApi(`/projects/${encodeURIComponent(projectId)}/environments/${encodeURIComponent(envId)}/test-url?relative_path=${encodeURIComponent(relativePath)}`, {
    method: 'POST',
  });
};