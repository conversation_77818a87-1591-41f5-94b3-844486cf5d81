import { fetchApi } from '../core/fetch';

/**
 * Configuration Management API Module
 *
 * Functions for managing test execution configurations including predefined
 * configurations, custom configurations, validation, and conflict resolution.
 *
 * @module ConfigurationAPI
 */

// ============================================================================
// CONFIGURATION FUNCTIONS
// ============================================================================

/**
 * Get all configurations (predefined + custom)
 * @returns Promise<{ predefined: any[], custom: any[] }> All configurations
 */
export const getAllConfigurations = (): Promise<{ predefined: any[], custom: any[] }> =>
  fetchApi<{ predefined: any[], custom: any[] }>('/config/');

/**
 * Get all predefined configurations
 * @returns Promise<any[]> Predefined configurations
 */
export const getPredefinedConfigurations = (): Promise<any[]> =>
  fetchApi<any[]>('/config/predefined');

/**
 * Get specific predefined configuration
 * @param configType - Configuration type identifier
 * @returns Promise<any> Predefined configuration
 */
export const getPredefinedConfiguration = (configType: string): Promise<any> =>
  fetchApi<any>(`/config/predefined/${configType}`);

/**
 * Get all custom configurations
 * @returns Promise<any[]> Custom configurations
 */
export const getCustomConfigurations = (): Promise<any[]> =>
  fetchApi<any[]>('/config/custom');

/**
 * Get specific custom configuration
 * @param configId - Configuration ID
 * @returns Promise<any> Custom configuration
 */
export const getCustomConfiguration = (configId: string): Promise<any> =>
  fetchApi<any>(`/config/custom/${configId}`);

/**
 * Create custom configuration
 * @param data - Configuration data
 * @returns Promise<any> Created configuration
 */
export const createCustomConfiguration = (data: any): Promise<any> =>
  fetchApi<any>('/config/custom', {
    method: 'POST',
    body: JSON.stringify(data)
  });

/**
 * Update custom configuration
 * @param configId - Configuration ID
 * @param data - Updated configuration data
 * @returns Promise<any> Updated configuration
 */
export const updateCustomConfiguration = (configId: string, data: any): Promise<any> =>
  fetchApi<any>(`/config/custom/${configId}`, {
    method: 'PUT',
    body: JSON.stringify(data)
  });

/**
 * Delete custom configuration
 * @param configId - Configuration ID
 * @returns Promise<any> Deletion result
 */
export const deleteCustomConfiguration = (configId: string): Promise<any> =>
  fetchApi<any>(`/config/custom/${configId}`, {
    method: 'DELETE'
  });

/**
 * Validate configuration
 * @param data - Configuration data to validate
 * @returns Promise<any> Validation result
 */
export const validateConfiguration = (data: any): Promise<any> =>
  fetchApi<any>('/config/validate', {
    method: 'POST',
    body: JSON.stringify(data)
  });

/**
 * Test configuration
 * @param data - Configuration data to test
 * @returns Promise<any> Test result
 */
export const testConfiguration = (data: any): Promise<any> =>
  fetchApi<any>('/config/test', {
    method: 'POST',
    body: JSON.stringify(data)
  });

/**
 * Get configurations by execution type
 * @param executionType - Execution type identifier
 * @returns Promise<any[]> Configurations for the execution type
 */
export const getConfigurationsByExecutionType = (executionType: string): Promise<any[]> =>
  fetchApi<any[]>(`/config/mongodb/configurations/execution-type/${executionType}`);

/**
 * Update configuration execution types
 * @param configId - Configuration ID
 * @param executionTypes - Array of execution types
 * @returns Promise<any> Update result
 */
export const updateConfigurationExecutionTypes = (configId: string, executionTypes: string[]): Promise<any> =>
  fetchApi<any>(`/config/mongodb/configurations/${configId}/execution-types`, {
    method: 'PUT',
    body: JSON.stringify({ execution_types: executionTypes })
  });

/**
 * Get environment defaults
 * @returns Promise<any> Environment defaults
 */
export const getEnvironmentDefaults = (): Promise<any> =>
  fetchApi<any>('/config/defaults');

/**
 * Update predefined configuration
 * @param configType - Configuration type
 * @param data - Updated configuration data
 * @returns Promise<any> Update result
 */
export const updatePredefinedConfiguration = (configType: string, data: any): Promise<any> =>
  fetchApi<any>(`/config/predefined/${configType}`, {
    method: 'PUT',
    body: JSON.stringify(data)
  });

/**
 * Validate execution types for conflicts
 * @param data - Validation data with config ID, execution types, and optional exclude config ID
 * @returns Promise with conflict information and suggested action
 */
export const validateExecutionTypes = (data: {
  config_id?: string;
  execution_types: string[];
  exclude_config_id?: string;
}): Promise<{
  has_conflicts: boolean;
  conflicts: Array<{
    config_id: string;
    config_name: string;
    config_type: string;
    conflicting_execution_types: string[];
    is_predefined: boolean;
  }>;
  suggested_action: string;
}> => fetchApi('/config/validate-execution-types', {
  method: 'POST',
  body: JSON.stringify(data)
});

/**
 * Resolve execution type conflicts
 * @param data - Conflict resolution data with action, target config, conflicts, and execution types
 * @returns Promise with resolution result and updated configurations
 */
export const resolveExecutionTypeConflict = (data: {
  action: 'remove_from_existing' | 'cancel';
  target_config?: any;
  conflicts: any[];
  execution_types: string[];
}): Promise<{
  success: boolean;
  message: string;
  updated_configs?: Array<{
    config_id: string;
    name: string;
    removed_types: string[];
  }>;
}> => fetchApi('/config/resolve-execution-type-conflict', {
  method: 'POST',
  body: JSON.stringify(data)
});