import { fetchApi } from '../core/fetch';
import { getProjectById } from './projects';
import { getProjectEnvironments } from './environments';
import type { ProjectVariable, ProjectVariableCreateInput, ProjectVariableUpdateInput, SuiteVariable, SuiteVariableCreateInput, SuiteVariableUpdateInput, ComputedVariable } from '@/lib/types';

/**
 * Variables Management API Module
 *
 * Functions for managing project and suite variables including creation,
 * updates, deletion, and computed variable resolution.
 *
 * @module VariablesAPI
 */

// ============================================================================
// PROJECT VARIABLES
// ============================================================================

/**
 * Get all variables for a specific project
 *
 * @param projectId - Unique project identifier
 * @returns Promise<ProjectVariable[]> Array of project variables
 */
export const getProjectVariables = async (projectId: string): Promise<ProjectVariable[]> => {
  const response = await fetchApi<{ items?: ProjectVariable[] }>(`/projects/${encodeURIComponent(projectId)}/variables`);
  return response.items || [];
};

/**
 * Create a new project variable
 *
 * @param projectId - Unique project identifier
 * @param variable - Variable creation data
 * @returns Promise<ProjectVariable> Created variable
 */
export const createProjectVariable = async (projectId: string, variable: ProjectVariableCreateInput): Promise<ProjectVariable> => {
  return fetchApi<ProjectVariable>(`/projects/${encodeURIComponent(projectId)}/variables`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(variable),
  });
};

/**
 * Update an existing project variable
 *
 * @param projectId - Unique project identifier
 * @param varId - Unique variable identifier
 * @param variable - Updated variable data
 * @returns Promise<ProjectVariable> Updated variable
 */
export const updateProjectVariable = async (projectId: string, varId: string, variable: ProjectVariableUpdateInput): Promise<ProjectVariable> => {
  return fetchApi<ProjectVariable>(`/projects/${encodeURIComponent(projectId)}/variables/${encodeURIComponent(varId)}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(variable),
  });
};

/**
 * Delete a project variable
 *
 * @param projectId - Unique project identifier
 * @param varId - Unique variable identifier
 * @returns Promise<void>
 */
export const deleteProjectVariable = async (projectId: string, varId: string): Promise<void> => {
  await fetchApi(`/projects/${encodeURIComponent(projectId)}/variables/${encodeURIComponent(varId)}`, {
    method: 'DELETE',
  });
};

// ============================================================================
// SUITE VARIABLES
// ============================================================================

/**
 * Get all variables for a specific suite
 *
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @returns Promise<SuiteVariable[]> Array of suite variables
 */
export const getSuiteVariables = async (projectId: string, suiteId: string): Promise<SuiteVariable[]> => {
  const response = await fetchApi<{ items?: SuiteVariable[] }>(`/projects/${encodeURIComponent(projectId)}/suites/${encodeURIComponent(suiteId)}/variables`);
  return response.items || [];
};

/**
 * Create a new suite variable
 *
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param variable - Variable creation data
 * @returns Promise<SuiteVariable> Created variable
 */
export const createSuiteVariable = async (projectId: string, suiteId: string, variable: SuiteVariableCreateInput): Promise<SuiteVariable> => {
  return fetchApi<SuiteVariable>(`/projects/${encodeURIComponent(projectId)}/suites/${encodeURIComponent(suiteId)}/variables`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(variable),
  });
};

/**
 * Update an existing suite variable
 *
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param varId - Unique variable identifier
 * @param variable - Updated variable data
 * @returns Promise<SuiteVariable> Updated variable
 */
export const updateSuiteVariable = async (projectId: string, suiteId: string, varId: string, variable: SuiteVariableUpdateInput): Promise<SuiteVariable> => {
  return fetchApi<SuiteVariable>(`/projects/${encodeURIComponent(projectId)}/suites/${encodeURIComponent(suiteId)}/variables/${encodeURIComponent(varId)}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(variable),
  });
};

/**
 * Delete a suite variable
 *
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param varId - Unique variable identifier
 * @returns Promise<void>
 */
export const deleteSuiteVariable = async (projectId: string, suiteId: string, varId: string): Promise<void> => {
  await fetchApi(`/projects/${encodeURIComponent(projectId)}/suites/${encodeURIComponent(suiteId)}/variables/${encodeURIComponent(varId)}`, {
    method: 'DELETE',
  });
};

// ============================================================================
// COMPUTED VARIABLES
// ============================================================================

/**
 * Get computed variables for a project with optional suite and environment context
 *
 * Computed variables combine project global variables, environment overrides,
 * and suite-level overrides into a single resolved set of variables.
 *
 * @param projectId - Unique project identifier
 * @param suiteId - Optional suite identifier for suite-level overrides
 * @param environmentId - Optional environment identifier for environment overrides
 * @returns Promise<ComputedVariable[]> Array of computed variables with resolution metadata
 */
export const getComputedVariables = async (projectId: string, suiteId?: string, environmentId?: string): Promise<ComputedVariable[]> => {
  try {
    // Get project data to access global variables
    const project = await getProjectById(projectId);
    const allVariables: ComputedVariable[] = [];

    // 1. Start with project global variables (base level)
    if (project.global_variables && typeof project.global_variables === 'object') {
      Object.entries(project.global_variables).forEach(([key, value]) => {
        allVariables.push({
          name: key,
          value: String(value),
          source: 'project',
          description: `Global project variable`,
          is_secret: false,
          is_overridden: false
        });
      });
    }

    // 2. Apply environment overrides (if environmentId is provided)
    if (environmentId) {
      try {
        const environments = await getProjectEnvironments(projectId);
        const environment = environments.find(env => env.env_id === environmentId);

        if (environment && environment.variables && typeof environment.variables === 'object') {
          Object.entries(environment.variables).forEach(([key, value]) => {
            const existingIndex = allVariables.findIndex(v => v.name === key);
            if (existingIndex >= 0) {
              // Override existing project variable
              allVariables[existingIndex] = {
                ...allVariables[existingIndex],
                value: String(value),
                source: 'environment',
                description: `Environment override from ${environment.name}`,
                is_overridden: true,
                environment_name: environment.name,
                environment_id: environment.env_id
              };
            } else {
              // Add new environment-specific variable
              allVariables.push({
                name: key,
                value: String(value),
                source: 'environment',
                description: `Environment variable from ${environment.name}`,
                is_secret: false,
                is_overridden: false,
                environment_name: environment.name,
                environment_id: environment.env_id
              });
            }
          });
        }
      } catch (error) {
        console.warn('Failed to load environment variables:', error);
      }
    }

    // 3. Apply suite-level overrides (if suiteId is provided)
    if (suiteId) {
      try {
        const url = `/projects/${encodeURIComponent(projectId)}/suites/${encodeURIComponent(suiteId)}/computed-variables`;
        const response = await fetchApi<{ items?: ComputedVariable[] }>(url);
        const suiteVariables = response.items || [];

        suiteVariables.forEach(suiteVar => {
          const existingIndex = allVariables.findIndex(v => v.name === suiteVar.name);
          if (existingIndex >= 0) {
            // Override existing variable (from project or environment)
            allVariables[existingIndex] = {
              ...allVariables[existingIndex],
              value: suiteVar.value,
              source: 'suite',
              description: suiteVar.description || `Suite override`,
              is_overridden: true
            };
          } else {
            // Add new suite-specific variable
            allVariables.push({
              ...suiteVar,
              source: 'suite',
              is_overridden: false
            });
          }
        });
      } catch (error) {
        console.warn('Failed to load suite variables:', error);
      }
    }

    return allVariables;
  } catch (error) {
    console.error('Error loading computed variables:', error);
    return [];
  }
};