-- Migration: Core Application Tables
-- Description: Creates projects, environments, test_suites, test_cases and related tables
-- Dependencies: 20250117000001_auth_profiles.sql

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
  project_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  default_environment_id UUID,
  github_config JSONB,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create environments table
CREATE TABLE IF NOT EXISTS environments (
  env_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  base_url TEXT NOT NULL,
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  config_overrides JSONB DEFAULT '{}',
  headless BOOLEAN,
  wait_time INTEGER,
  timeout INTEGER,
  viewport_width INTEGER,
  viewport_height INTEGER,
  user_agent TEXT,
  tags TEXT[] DEFAULT '{}',
  variables JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create test_suites table
CREATE TABLE IF NOT EXISTS test_suites (
  suite_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  type TEXT NOT NULL DEFAULT 'manual' CHECK (type IN ('manual', 'github', 'automated')),
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  execution_times INTEGER NOT NULL DEFAULT 0
);

-- Create test_cases table
CREATE TABLE IF NOT EXISTS test_cases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  suite_id UUID NOT NULL REFERENCES test_suites(suite_id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  instructions TEXT NOT NULL,
  user_story TEXT NOT NULL,
  gherkin TEXT,
  url TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Running', 'InProgress', 'Passed', 'Failed', 'Blocked', 'Skipped')),
  priority TEXT NOT NULL DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High', 'Critical')),
  type TEXT NOT NULL DEFAULT 'Functional' CHECK (type IN ('Functional', 'Performance', 'Security', 'Integration', 'UnitTest')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  history_files TEXT[] DEFAULT '{}',
  last_execution TIMESTAMPTZ,
  last_result TEXT,
  error_message TEXT,
  sensitive_data JSONB,
  allowed_domains TEXT[],
  code TEXT,
  framework TEXT,
  enable_file_handling BOOLEAN
);

-- Create project_variables table
CREATE TABLE IF NOT EXISTS project_variables (
  var_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  is_secret BOOLEAN NOT NULL DEFAULT FALSE,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(project_id, name)
);

-- Create suite_variables table
CREATE TABLE IF NOT EXISTS suite_variables (
  var_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  suite_id UUID NOT NULL REFERENCES test_suites(suite_id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  is_secret BOOLEAN NOT NULL DEFAULT FALSE,
  overrides_project BOOLEAN,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(suite_id, name)
);

-- Create test_file_attachments table
CREATE TABLE IF NOT EXISTS test_file_attachments (
  file_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  test_case_id UUID NOT NULL REFERENCES test_cases(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  content_type TEXT NOT NULL,
  upload_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  file_path TEXT
);

-- Add foreign key constraint for default_environment_id
ALTER TABLE projects
ADD CONSTRAINT IF NOT EXISTS projects_default_environment_id_fkey
FOREIGN KEY (default_environment_id) REFERENCES environments(env_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_environments_project_id ON environments(project_id);
CREATE INDEX IF NOT EXISTS idx_test_suites_project_id ON test_suites(project_id);
CREATE INDEX IF NOT EXISTS idx_test_cases_suite_id ON test_cases(suite_id);
CREATE INDEX IF NOT EXISTS idx_test_cases_project_id ON test_cases(project_id);
CREATE INDEX IF NOT EXISTS idx_project_variables_project_id ON project_variables(project_id);
CREATE INDEX IF NOT EXISTS idx_suite_variables_suite_id ON suite_variables(suite_id);
CREATE INDEX IF NOT EXISTS idx_test_file_attachments_test_case_id ON test_file_attachments(test_case_id);

-- Create triggers for updated_at
CREATE TRIGGER update_projects_updated_at 
  BEFORE UPDATE ON projects 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_environments_updated_at 
  BEFORE UPDATE ON environments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_test_suites_updated_at 
  BEFORE UPDATE ON test_suites 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_test_cases_updated_at 
  BEFORE UPDATE ON test_cases 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_variables_updated_at 
  BEFORE UPDATE ON project_variables 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_suite_variables_updated_at 
  BEFORE UPDATE ON suite_variables 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE environments ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_suites ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_variables ENABLE ROW LEVEL SECURITY;
ALTER TABLE suite_variables ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_file_attachments ENABLE ROW LEVEL SECURITY;