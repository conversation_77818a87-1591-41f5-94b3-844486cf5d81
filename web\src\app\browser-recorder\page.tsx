"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import {
  Play,
  Square,
  RotateCcw,
  MessageSquare,
  Eye,
  Download,
  Copy,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from "lucide-react";

interface RecordedStep {
  id: number;
  timestamp: number;
  type: string;
  action: string;
  tag?: string;
  text?: string;
  xpath?: string;
  description: string;
  value?: string;
  cypress_selector?: string;
  is_agent_action?: boolean;
}

interface ChatMessage {
  role: "user" | "assistant";
  content: string;
  timestamp: number;
  is_step_update?: boolean;
  is_final_result?: boolean;
}

export default function BrowserRecorderPage() {
  const [isRecording, setIsRecording] = useState(false);
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [steps, setSteps] = useState<RecordedStep[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [browserUrl, setBrowserUrl] = useState("");
  const wsRef = useRef<WebSocket | null>(null);
  const { toast } = useToast();

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (isSessionActive) {
      connectWebSocket();
    } else {
      disconnectWebSocket();
    }

    return () => disconnectWebSocket();
  }, [isSessionActive]);

  const connectWebSocket = () => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/api/recorder/ws`;

    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onopen = () => {
      console.log('WebSocket connected to recorder');
    };

    wsRef.current.onmessage = (event) => {
      const message = JSON.parse(event.data);
      handleWebSocketMessage(message);
    };

    wsRef.current.onerror = (error) => {
      console.error('WebSocket error:', error);
      toast({
        title: "Connection Error",
        description: "Failed to connect to recorder service",
        variant: "destructive",
      });
    };

    wsRef.current.onclose = () => {
      console.log('WebSocket disconnected from recorder');
    };
  };

  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
  };

  const handleWebSocketMessage = (message: any) => {
    switch (message.type) {
      case 'step_recorded':
        setSteps(prev => [...prev, message.step]);
        break;
      case 'step_updated':
        setSteps(prev => prev.map((step, index) =>
          index === message.index ? message.step : step
        ));
        break;
      case 'steps_cleared':
        setSteps([]);
        break;
      case 'chat_message':
        setChatMessages(prev => [...prev, message.message]);
        break;
      case 'recording_started':
        setIsRecording(true);
        toast({
          title: "Recording Started",
          description: "Browser interactions are now being recorded",
        });
        break;
      case 'recording_stopped':
        setIsRecording(false);
        toast({
          title: "Recording Stopped",
          description: "Browser interaction recording has stopped",
        });
        break;
      case 'session_stopped':
        setIsSessionActive(false);
        setIsRecording(false);
        setSteps([]);
        setChatMessages([]);
        toast({
          title: "Session Ended",
          description: "Browser session has been closed",
        });
        break;
    }
  };

  const createSession = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/recorder/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to create browser session');
      }

      const result = await response.json();
      setIsSessionActive(true);
      setBrowserUrl(result.current_url || 'about:blank');

      toast({
        title: "Session Created",
        description: "Browser session is now active",
      });
    } catch (error) {
      console.error('Error creating session:', error);
      toast({
        title: "Error",
        description: "Failed to create browser session",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const startRecording = async () => {
    try {
      const response = await fetch('/api/recorder/recording/start', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to start recording');
      }
    } catch (error) {
      console.error('Error starting recording:', error);
      toast({
        title: "Error",
        description: "Failed to start recording",
        variant: "destructive",
      });
    }
  };

  const stopRecording = async () => {
    try {
      const response = await fetch('/api/recorder/recording/stop', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to stop recording');
      }
    } catch (error) {
      console.error('Error stopping recording:', error);
      toast({
        title: "Error",
        description: "Failed to stop recording",
        variant: "destructive",
      });
    }
  };

  const stopSession = async () => {
    try {
      const response = await fetch('/api/recorder/sessions/stop', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to stop session');
      }
    } catch (error) {
      console.error('Error stopping session:', error);
      toast({
        title: "Error",
        description: "Failed to stop browser session",
        variant: "destructive",
      });
    }
  };

  const sendChatMessage = async () => {
    if (!chatInput.trim()) return;

    try {
      const response = await fetch('/api/recorder/chat/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: chatInput }),
      });

      if (!response.ok) {
        throw new Error('Failed to send chat message');
      }

      setChatInput("");
    } catch (error) {
      console.error('Error sending chat message:', error);
      toast({
        title: "Error",
        description: "Failed to send message to AI agent",
        variant: "destructive",
      });
    }
  };

  const exportSteps = () => {
    const dataStr = JSON.stringify(steps, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `browser-steps-${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const copyStepsToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(steps, null, 2));
      toast({
        title: "Copied",
        description: "Steps copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const getStepIcon = (step: RecordedStep) => {
    if (step.is_agent_action) return <Zap className="w-4 h-4 text-purple-500" />;

    switch (step.action) {
      case 'click': return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'type': return <MessageSquare className="w-4 h-4 text-green-500" />;
      case 'navigate': return <Eye className="w-4 h-4 text-orange-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Browser Recorder</h1>
          <p className="text-muted-foreground">
            Record browser interactions and AI-assisted test generation
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isSessionActive ? "default" : "secondary"}>
            {isSessionActive ? "Session Active" : "No Session"}
          </Badge>
          <Badge variant={isRecording ? "destructive" : "outline"}>
            {isRecording ? "Recording" : "Not Recording"}
          </Badge>
        </div>
      </div>

      {/* Session Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Browser Session</CardTitle>
          <CardDescription>
            Create and manage browser recording sessions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            {!isSessionActive ? (
              <Button
                onClick={createSession}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Play className="w-4 h-4" />
                Create Session
              </Button>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  onClick={startRecording}
                  disabled={isRecording}
                  variant="default"
                  className="flex items-center gap-2"
                >
                  <Play className="w-4 h-4" />
                  Start Recording
                </Button>
                <Button
                  onClick={stopRecording}
                  disabled={!isRecording}
                  variant="destructive"
                  className="flex items-center gap-2"
                >
                  <Square className="w-4 h-4" />
                  Stop Recording
                </Button>
                <Button
                  onClick={stopSession}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="w-4 h-4" />
                  End Session
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Steps Panel */}
        <Card className="flex flex-col">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recorded Steps</CardTitle>
              <CardDescription>
                {steps.length} step{steps.length !== 1 ? 's' : ''} recorded
              </CardDescription>
            </div>
            {steps.length > 0 && (
              <div className="flex gap-2">
                <Button
                  onClick={copyStepsToClipboard}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Copy className="w-4 h-4" />
                  Copy
                </Button>
                <Button
                  onClick={exportSteps}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Export
                </Button>
              </div>
            )}
          </CardHeader>
          <CardContent className="flex-1">
            <ScrollArea className="h-96">
              {steps.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No steps recorded yet</p>
                  <p className="text-sm">Start recording to see interactions here</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {steps.map((step) => (
                    <div
                      key={step.id}
                      className={`flex items-start gap-3 p-3 rounded-lg border ${
                        step.is_agent_action
                          ? 'bg-purple-50 border-purple-200'
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      {getStepIcon(step)}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{step.description}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {step.action}
                          </Badge>
                          {step.tag && (
                            <Badge variant="secondary" className="text-xs">
                              {step.tag}
                            </Badge>
                          )}
                          <span className="text-xs text-muted-foreground">
                            {new Date(step.timestamp * 1000).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Chat Panel */}
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle>AI Assistant</CardTitle>
            <CardDescription>
              Chat with AI agent for test automation assistance
            </CardDescription>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col">
            <ScrollArea className="flex-1 mb-4">
              <div className="space-y-4">
                {chatMessages.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No messages yet</p>
                    <p className="text-sm">Send a message to start chatting with the AI agent</p>
                  </div>
                ) : (
                  chatMessages.map((message, index) => (
                    <div
                      key={index}
                      className={`flex ${
                        message.role === 'user' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-lg ${
                          message.role === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {new Date(message.timestamp * 1000).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>

            <div className="flex gap-2">
              <Input
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                placeholder="Ask the AI agent for help..."
                onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                className="flex-1"
              />
              <Button
                onClick={sendChatMessage}
                disabled={!chatInput.trim() || !isSessionActive}
                className="flex items-center gap-2"
              >
                <MessageSquare className="w-4 h-4" />
                Send
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Browser Viewport Placeholder */}
      {isSessionActive && (
        <Card>
          <CardHeader>
            <CardTitle>Browser Viewport</CardTitle>
            <CardDescription>
              Browser session is active. Use the Steel Browser interface to interact with web pages.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Eye className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">Steel Browser Active</h3>
              <p className="text-muted-foreground mb-4">
                The browser session is running. Go to the Steel Browser page to interact with web pages.
              </p>
              <Button
                onClick={() => window.open('/steel-browser', '_blank')}
                className="flex items-center gap-2"
              >
                <Eye className="w-4 h-4" />
                Open Steel Browser
              </Button>
              <p className="text-sm text-muted-foreground mt-2">
                Current URL: {browserUrl}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}