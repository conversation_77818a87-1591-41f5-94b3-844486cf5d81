/**
 * Execution API Module Index
 * 
 * Centralized exports for all execution-related API functions including:
 * - V1 (Legacy) execution APIs  
 * - V2 (Current) execution APIs with rich data extraction
 * - Execution history and MongoDB data access
 * - Execution control (pause, resume, stop)
 * 
 * ## API Version Guide:
 * 
 * ### V1 (Legacy - Deprecated)
 * - Basic execution functionality
 * - Limited data extraction
 * - Synchronous execution pattern
 * - Use only for backward compatibility
 * 
 * ### V2 (Current - Recommended)
 * - Rich data extraction with screenshots
 * - Async execution pattern with polling
 * - Multiple execution types (smoke, full, case, suite)
 * - Configuration profiles for different speeds
 * - Better error handling and status tracking
 * 
 * @example
 * ```typescript
 * // Recommended V2 usage
 * import { executeSmokeTestV2, executeTestCaseV2 } from '@/lib/api/execution';
 * 
 * // Legacy V1 (avoid in new code)
 * import { executeTest } from '@/lib/api/execution';
 * 
 * // History and data access
 * import { getTestMongoExecutions, getTestExecutionHistory } from '@/lib/api/execution';
 * ```
 */

/**
 * @fileoverview Execution API Module
 * Centralized exports for all execution-related functionality
 */

// V1 Execution API (Stable)
export * from './v1';

// V2 Execution API (Enhanced with rich data)
export * from './v2';

// Execution History
export * from './history';