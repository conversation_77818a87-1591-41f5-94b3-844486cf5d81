"use client";

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getProjectEnvironments, 
  createProjectEnvironment, 
  updateProjectEnvironment, 
  deleteProjectEnvironment,
  setDefaultEnvironment,
  testEnvironmentUrl,
  getProjectById
} from '@/lib/api';
import type { Environment, EnvironmentCreateInput, EnvironmentUpdateInput, Project } from '@/lib/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Settings, 
  Trash2, 
  CheckCircle, 
  Globe,
  ExternalLink,
  Star,
  AlertCircle,
  Loader2,
  X,
  Key
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import ProjectVariablesManager from '@/components/projects/ProjectVariablesManager';

interface EnvironmentManagementProps {
  projectId: string;
}

interface EnvironmentFormData {
  name: string;
  base_url: string;
  is_default: boolean;
  variables: Array<{ key: string; value: string }>; // Changed to array of key-value pairs
}

const initialFormData: EnvironmentFormData = {
  name: '',
  base_url: '',
  is_default: false,
  variables: [],
};

export function EnvironmentManagement({ projectId }: EnvironmentManagementProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isProjectVariablesOpen, setIsProjectVariablesOpen] = useState(false);
  const [editingEnv, setEditingEnv] = useState<Environment | null>(null);
  const [formData, setFormData] = useState<EnvironmentFormData>(initialFormData);
  const [testingUrl, setTestingUrl] = useState<string | null>(null);
  
  const queryClient = useQueryClient();

  // Query environments
  const { 
    data: environments, 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['environments', projectId],
    queryFn: () => getProjectEnvironments(projectId),
    enabled: !!projectId,
  });

  // Query project for global variables
  const { data: project } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => getProjectById(projectId),
    enabled: !!projectId,
  });

  // Create environment mutation
  const createMutation = useMutation({
    mutationFn: (data: EnvironmentCreateInput) => {
      console.log('Creating environment with data:', data);
      return createProjectEnvironment(projectId, data);
    },
    onSuccess: (response) => {
      console.log('Environment created successfully:', response);
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      setIsDialogOpen(false);
      setFormData(initialFormData);
      toast({
        title: "Environment created",
        description: "The environment has been created successfully.",
      });
    },
    onError: (error) => {
      console.error('Error creating environment:', error);
      toast({
        title: "Error creating environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update environment mutation
  const updateMutation = useMutation({
    mutationFn: ({ envId, data }: { envId: string; data: EnvironmentUpdateInput }) => {
      console.log('Updating environment with data:', data);
      return updateProjectEnvironment(projectId, envId, data);
    },
    onSuccess: (response) => {
      console.log('Environment updated successfully:', response);
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      setIsDialogOpen(false);
      setEditingEnv(null);
      setFormData(initialFormData);
      toast({
        title: "Environment updated",
        description: "The environment has been updated successfully.",
      });
    },
    onError: (error) => {
      console.error('Error updating environment:', error);
      toast({
        title: "Error updating environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete environment mutation
  const deleteMutation = useMutation({
    mutationFn: (envId: string) => deleteProjectEnvironment(projectId, envId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      toast({
        title: "Environment deleted",
        description: "The environment has been deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error deleting environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Set default environment mutation
  const setDefaultMutation = useMutation({
    mutationFn: (envId: string) => setDefaultEnvironment(projectId, envId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['environments', projectId] });
      toast({
        title: "Default environment set",
        description: "The default environment has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error setting default environment",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Test URL mutation
  const testUrlMutation = useMutation({
    mutationFn: ({ envId, path }: { envId: string; path: string }) => 
      testEnvironmentUrl(projectId, envId, path),
    onSuccess: (result) => {
      toast({
        title: `URL Test ${result.accessible ? 'Successful' : 'Failed'}`,
        description: `${result.full_url} is ${result.accessible ? 'accessible' : 'not accessible'}${result.status_code ? ` (${result.status_code})` : ''}`,
        variant: result.accessible ? "default" : "destructive",
      });
    },
    onError: (error) => {
      toast({
        title: "Error testing URL",
        description: error.message,
        variant: "destructive",
      });
    },
    onSettled: () => {
      setTestingUrl(null);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Start with project global variables as base
      let finalVariables: Record<string, string> = {};
      
      if (project && 'global_variables' in project && project.global_variables) {
        finalVariables = { ...project.global_variables };
      }
      
      // Apply environment-specific overrides
      formData.variables.forEach(({ key, value }) => {
        if (key.trim() && value.trim()) {
          finalVariables[key.trim()] = value.trim();
        }
      });

      const envData = {
        name: formData.name,
        description: '', // Default empty description
        base_url: formData.base_url,
        is_default: formData.is_default,
        config_overrides: {}, // Default empty config
        tags: [], // Default empty tags
        variables: finalVariables, // Combined variables
      };

      console.log('Sending environment data:', envData);
      console.log('Project global variables:', project && 'global_variables' in project ? project.global_variables : 'None');
      console.log('Environment overrides:', formData.variables);
      console.log('Final combined variables:', finalVariables);

      if (editingEnv) {
        updateMutation.mutate({ envId: editingEnv.env_id, data: envData });
      } else {
        createMutation.mutate(envData);
      }
    } catch (error) {
      toast({
        title: "Error processing variables",
        description: "Please check your variable entries.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (env: Environment) => {
    setEditingEnv(env);
    
    // For variables, only include those that override project variables
    const variablesArray: Array<{ key: string; value: string }> = [];
    
    if (env.variables && project && 'global_variables' in project && project.global_variables) {
      // Only include variables that are different from project defaults
      Object.entries(env.variables).forEach(([key, value]) => {
        if (project.global_variables && key in project.global_variables && String(value) !== String(project.global_variables[key])) {
          variablesArray.push({ key, value: String(value) });
        }
      });
    }
    
    setFormData({
      name: env.name,
      base_url: env.base_url,
      is_default: env.is_default,
      variables: variablesArray,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (envId: string) => {
    if (confirm('Are you sure you want to delete this environment?')) {
      deleteMutation.mutate(envId);
    }
  };

  const handleTestUrl = (envId: string) => {
    setTestingUrl(envId);
    testUrlMutation.mutate({ envId, path: '/' });
  };

  const resetForm = () => {
    setFormData(initialFormData);
    setEditingEnv(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
        <p className="text-destructive">Failed to load environments</p>
      </div>
    );
  }

  return (
      <div className="space-y-6">
        {/* Project Variables Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold">Project Variables</h3>
              <p className="text-sm text-muted-foreground">
                Define global variables that will be inherited by all environments
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => {
                // Open project variables management modal/section
                setIsProjectVariablesOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Manage Project Variables
            </Button>
          </div>
          
          {/* Show current project variables */}
          {project && 'global_variables' in project && project.global_variables && Object.keys(project.global_variables).length > 0 ? (
            <Card>
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {Object.entries(project.global_variables).map(([key, value]) => (
                    <div key={key} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-center gap-2 mb-1">
                        <Globe className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">{key}</span>
                      </div>
                      <div className="text-xs text-blue-600">{String(value)}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h4 className="text-lg font-semibold mb-2">No project variables defined</h4>
                <p className="text-muted-foreground mb-4">
                  Create global variables that will be shared across all environments.
                </p>
                <Button onClick={() => setIsProjectVariablesOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Variable
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Environments Section */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Environments</h2>
            <p className="text-muted-foreground">
              Manage test execution environments and override variable values
            </p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={(open) => {
            setIsDialogOpen(open);
            if (!open) resetForm();
          }}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Environment
              </Button>
            </DialogTrigger>          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingEnv ? 'Edit Environment' : 'Create Environment'}
              </DialogTitle>
              <DialogDescription>
                {editingEnv 
                  ? 'Update the environment configuration'
                  : 'Add a new environment for test execution'
                }
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-6 py-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                      Environment Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Development, QA, Production"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="base_url" className="text-sm font-medium">Base URL</Label>
                    <Input
                      id="base_url"
                      value={formData.base_url}
                      onChange={(e) => setFormData(prev => ({ ...prev, base_url: e.target.value }))}
                      placeholder="https://app.example.com"
                      type="url"
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_default"
                    checked={formData.is_default}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_default: checked }))}
                  />
                  <Label htmlFor="is_default" className="text-sm font-medium">Set as default environment</Label>
                </div>
              </div>

              {/* Variables Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Key className="h-4 w-4" />
                      Variable Values Override
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      Override values for global project variables in this environment.
                    </p>
                  </div>
                </div>

                {/* Show global variables from project if any */}
                {project && 'global_variables' in project && project.global_variables && Object.keys(project.global_variables).length > 0 ? (
                  <div className="space-y-4">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Globe className="h-4 w-4 text-blue-600" />
                        <Label className="text-sm font-medium text-blue-800">
                          Global Project Variables
                        </Label>
                      </div>
                      <p className="text-xs text-blue-700 mb-3">
                        These variables are defined at the project level. You can override their values for this environment below.
                      </p>
                    </div>

                    <div className="space-y-3">
                      <div className="text-sm font-medium text-gray-700 mb-2">
                        Override Variable Values:
                      </div>
                      {Object.entries(project.global_variables).map(([globalKey, globalValue]) => {
                        // Find if this variable is overridden in formData
                        const override = formData.variables.find(v => v.key === globalKey);
                        const currentValue = override?.value || String(globalValue);
                        
                        return (
                          <div key={globalKey} className="bg-white border rounded-lg p-3">
                            <div className="flex gap-3 items-center">
                              <div className="flex-1">
                                <Label className="text-xs font-medium text-blue-700 mb-1 block">
                                  {globalKey}
                                </Label>
                                <div className="text-xs text-blue-600 mb-2">
                                  Default: {String(globalValue)}
                                </div>
                                <Input
                                  value={currentValue}
                                  onChange={(e) => {
                                    const newVariables = formData.variables.filter(v => v.key !== globalKey);
                                    if (e.target.value !== String(globalValue)) {
                                      // Only add override if value is different from default
                                      newVariables.push({ key: globalKey, value: e.target.value });
                                    }
                                    setFormData(prev => ({ ...prev, variables: newVariables }));
                                  }}
                                  placeholder={`Default: ${String(globalValue)}`}
                                  className={cn(
                                    "mt-1",
                                    currentValue !== String(globalValue) && "border-amber-300 bg-amber-50"
                                  )}
                                />
                                {currentValue !== String(globalValue) && (
                                  <p className="text-xs text-amber-600 mt-1">
                                    ⚠ Overriding default value
                                  </p>
                                )}
                              </div>
                              {currentValue !== String(globalValue) && (
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    const newVariables = formData.variables.filter(v => v.key !== globalKey);
                                    setFormData(prev => ({ ...prev, variables: newVariables }));
                                  }}
                                  className="text-amber-600 hover:text-amber-700"
                                  title="Reset to default value"
                                >
                                  ↺ Reset
                                </Button>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    <div className="text-xs text-muted-foreground bg-green-50 border border-green-200 rounded-lg p-3">
                      <p className="font-medium text-green-800 mb-1">💡 Variable Management:</p>
                      <ul className="text-green-700 space-y-1">
                        <li>• To create new variables, go to <strong>Project Variables</strong> section</li>
                        <li>• Environment variables override project defaults during test execution</li>
                        <li>• Use <code className="bg-green-100 px-1 rounded">{'${variableName}'}</code> in test steps</li>
                        <li>• Variables follow priority: Suite &gt; Environment &gt; Project</li>
                      </ul>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
                    <Key className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground mb-3">
                      No global variables defined in project
                    </p>
                    <p className="text-xs text-muted-foreground mb-3">
                      Create global variables at the project level first, then override values here.
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Navigate to project variables page
                        window.open(`/projects/${projectId}/variables`, '_blank');
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Manage Project Variables
                    </Button>
                  </div>
                )}
              </div>              <DialogFooter className="flex gap-2 pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={createMutation.isPending || updateMutation.isPending}
                  className="min-w-[100px]"
                >
                  {(createMutation.isPending || updateMutation.isPending) && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  {editingEnv ? 'Update Environment' : 'Create Environment'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {!environments || environments.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No environments configured</h3>
            <p className="text-muted-foreground mb-4">
              Create your first environment to manage test execution across different environments.
            </p>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Environment
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {environments.map((env) => (
            <Card key={env.env_id} className={cn(
              "relative",
              env.is_default && "ring-2 ring-primary"
            )}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {env.name}
                    {env.is_default && (
                      <Badge variant="default" className="text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Default
                      </Badge>
                    )}
                  </CardTitle>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleTestUrl(env.env_id)}
                      disabled={testingUrl === env.env_id}
                      title="Test URL accessibility"
                    >
                      {testingUrl === env.env_id ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <ExternalLink className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(env)}
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                    {!env.is_default && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(env.env_id)}
                        disabled={deleteMutation.isPending}
                      >
                        <Trash2 className="h-3 w-3 text-destructive" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-xs font-medium text-muted-foreground">Base URL</Label>
                  <p className="text-sm break-all">{env.base_url || "No base URL"}</p>
                </div>
                
                {/* Show variable count and inheritance info */}
                <div>
                  <Label className="text-xs font-medium text-muted-foreground">Variables</Label>
                  {(() => {
                    const globalVarCount = project && 'global_variables' in project && project.global_variables 
                      ? Object.keys(project.global_variables).length 
                      : 0;
                    const envVarCount = env.variables ? Object.keys(env.variables).length : 0;
                    const totalCount = globalVarCount + envVarCount;
                    
                    if (totalCount === 0) {
                      return (
                        <p className="text-xs text-muted-foreground">No variables defined</p>
                      );
                    }
                    
                    return (
                      <div className="space-y-1">
                        <p className="text-xs text-muted-foreground">
                          {totalCount} total variable(s)
                        </p>
                        {globalVarCount > 0 && (
                          <div className="flex items-center gap-1 text-xs text-blue-600">
                            <Globe className="h-3 w-3" />
                            <span>{globalVarCount} inherited from project</span>
                          </div>
                        )}
                        {envVarCount > 0 && (
                          <div className="text-xs text-green-600">
                            {envVarCount} environment-specific: {Object.keys(env.variables!).join(', ')}
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
                
                {!env.is_default && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setDefaultMutation.mutate(env.env_id)}
                    disabled={setDefaultMutation.isPending}
                    className="w-full"
                  >
                    <Star className="h-3 w-3 mr-2" />
                    Set as Default
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      
      {/* Project Variables Management Modal */}
      <Dialog open={isProjectVariablesOpen} onOpenChange={setIsProjectVariablesOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Manage Project Variables</DialogTitle>
            <DialogDescription>
              Create and manage global variables that will be inherited by all environments.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <ProjectVariablesManager projectId={projectId} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default EnvironmentManagement;