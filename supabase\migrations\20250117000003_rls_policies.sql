-- Migration: Row Level Security Policies
-- Description: Creates RLS policies for all core tables
-- Dependencies: 20250117000002_core_tables.sql

-- Projects RLS Policies
-- Users can only see and manage their own projects
CREATE POLICY "Users can view own projects" ON projects 
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own projects" ON projects 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON projects 
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own projects" ON projects 
  FOR DELETE USING (auth.uid() = user_id);

-- Environments RLS Policies
-- Users can only manage environments of their own projects
CREATE POLICY "Users can view environments of own projects" ON environments 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = environments.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert environments for own projects" ON environments 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = environments.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update environments of own projects" ON environments 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = environments.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete environments of own projects" ON environments 
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = environments.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Test Suites RLS Policies
-- Users can only manage test suites of their own projects
CREATE POLICY "Users can view test suites of own projects" ON test_suites 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_suites.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert test suites for own projects" ON test_suites 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_suites.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update test suites of own projects" ON test_suites 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_suites.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete test suites of own projects" ON test_suites 
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_suites.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Test Cases RLS Policies
-- Users can only manage test cases of their own projects
CREATE POLICY "Users can view test cases of own projects" ON test_cases 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_cases.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert test cases for own projects" ON test_cases 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_cases.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update test cases of own projects" ON test_cases 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_cases.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete test cases of own projects" ON test_cases 
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = test_cases.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Project Variables RLS Policies
-- Users can only manage variables of their own projects
CREATE POLICY "Users can view project variables of own projects" ON project_variables 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = project_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert project variables for own projects" ON project_variables 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = project_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update project variables of own projects" ON project_variables 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = project_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete project variables of own projects" ON project_variables 
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = project_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Suite Variables RLS Policies
-- Users can only manage suite variables of their own projects
CREATE POLICY "Users can view suite variables of own projects" ON suite_variables 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = suite_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert suite variables for own projects" ON suite_variables 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = suite_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update suite variables of own projects" ON suite_variables 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = suite_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete suite variables of own projects" ON suite_variables 
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.project_id = suite_variables.project_id 
      AND projects.user_id = auth.uid()
    )
  );

-- Test File Attachments RLS Policies
-- Users can only manage file attachments of their own test cases
CREATE POLICY "Users can view test file attachments of own projects" ON test_file_attachments 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM test_cases
      JOIN projects ON projects.project_id = test_cases.project_id
      WHERE test_cases.id = test_file_attachments.test_case_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert test file attachments for own projects" ON test_file_attachments 
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM test_cases
      JOIN projects ON projects.project_id = test_cases.project_id
      WHERE test_cases.id = test_file_attachments.test_case_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update test file attachments of own projects" ON test_file_attachments 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM test_cases
      JOIN projects ON projects.project_id = test_cases.project_id
      WHERE test_cases.id = test_file_attachments.test_case_id 
      AND projects.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete test file attachments of own projects" ON test_file_attachments 
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM test_cases
      JOIN projects ON projects.project_id = test_cases.project_id
      WHERE test_cases.id = test_file_attachments.test_case_id 
      AND projects.user_id = auth.uid()
    )
  );