// src/components/AppSidebar.tsx
"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContextSupabase';
import { usePermissions } from '@/hooks/usePermissions';
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { Bot, FolderKanban, Settings, LayoutDashboard, Flame, FileText, Code2, BarChart2, Monitor, Shield, Sparkles, Users, Building2, Brain } from 'lucide-react';
import Image from 'next/image';
import type { NavItem } from '@/lib/types';

// Base navigation items
const baseNavItems: NavItem[] = [
  { title: 'Dashboard', href: '/', icon: <LayoutDashboard /> },
  { title: 'Projects', href: '/projects', icon: <FolderKanban /> },
  { title: 'Steel Browser', href: '/steel-browser', icon: <Monitor /> },
  { title: 'Browser Recorder', href: '/browser-recorder', icon: <Code2 /> },
  { title: 'QAKI Assistant', href: '/qaki', icon: <Sparkles /> },
  { title: 'Smoke Test Playground', href: '/smoke-test-playground', icon: <Flame /> },
];

// Admin navigation items (only for SuperAdmin)
const adminNavItems: NavItem[] = [
  { title: 'Admin Dashboard', href: '/admin', icon: <Shield /> },
  { title: 'Users', href: '/admin/users', icon: <Users /> },
  { title: 'Tenants', href: '/admin/tenants', icon: <Building2 /> },
  { title: 'LLM Providers', href: '/admin/llm/providers', icon: <Brain /> },
];

const settingsNavItem: NavItem = {
  title: 'Settings',
  href: '/settings',
  icon: <Settings />,
};

export function AppSidebar() {
  const pathname = usePathname();
  const { user } = useAuth();
  const permissions = usePermissions();

  const isActive = (href: string) => {
    if (href === '/') return pathname === href;
    return pathname.startsWith(href);
  };

  // Debug logging to see user role and permissions
  console.log('🔍 AppSidebar: Current user:', user);
  console.log('🔍 AppSidebar: User role:', user?.role);
  console.log('🔍 AppSidebar: Permissions:', permissions);

  // Combine navigation items based on user permissions
  const mainNavItems = [
    ...baseNavItems,
    // Mostrar items de admin solo si el usuario tiene permisos
    ...(permissions.canAccessGlobalAdmin ? adminNavItems : []),
    // Mostrar items de tenant admin si es admin de tenant
    ...(permissions.isTenantAdmin ? [
      { title: 'Tenant Settings', href: '/tenant/settings', icon: <Settings /> },
      { title: 'Tenant Users', href: '/tenant/users', icon: <Users /> }
    ] : [])
  ];

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader className="p-4">
        <Link href="/" className="flex items-center gap-2 glow-text group-data-[collapsible=icon]:justify-center">
          <Image 
            src="/favicon.ico" 
            alt="QAK Logo" 
            width={28} 
            height={28} 
            className="rounded"
          />
          <span className="text-xl font-semibold group-data-[collapsible=icon]:hidden">QAK</span>
        </Link>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {mainNavItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              <Link href={item.href} legacyBehavior passHref>
                <SidebarMenuButton
                  asChild
                  isActive={isActive(item.href)}
                  tooltip={{ children: item.title, className: "group-data-[collapsible=icon]:block hidden" }}
                >
                  <a>
                    {item.icon}
                    <span>{item.title}</span>
                  </a>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarSeparator />
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <Link href={settingsNavItem.href} legacyBehavior passHref>
              <SidebarMenuButton 
                asChild 
                isActive={isActive(settingsNavItem.href)}
                tooltip={{ children: settingsNavItem.title, className: "group-data-[collapsible=icon]:block hidden" }}
              >
                <a>
                  {settingsNavItem.icon}
                  <span>{settingsNavItem.title}</span>
                </a>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
