'use client'

import React, { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Play, 
  Square, 
  Circle, 
  MessageSquare, 
  Send, 
  Monitor, 
  Activity,
  Clock,
  MousePointer,
  Keyboard,
  Navigation,
  Bot,
  User,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import type { RecordingStep, ChatMessage } from '@/lib/api-supabase/browser-automation'

interface BrowserAutomationPanelProps {
  testCase?: {
    instructions: string
    user_story: string
    url: string
  }
  onExecutionComplete?: (steps: RecordingStep[]) => void
}

export function BrowserAutomationPanel({ testCase, onExecutionComplete }: BrowserAutomationPanelProps) {
  const [sessionActive, setSessionActive] = useState(false)
  const [recording, setRecording] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [steps, setSteps] = useState<RecordingStep[]>([])
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [sendingMessage, setSendingMessage] = useState(false)
  const [wsConnected, setWsConnected] = useState(false)
  
  const wsRef = useRef<WebSocket | null>(null)
  const chatEndRef = useRef<HTMLDivElement>(null)

  // WebSocket connection for real-time updates
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        const ws = new WebSocket('ws://localhost:8080/ws')
        
        ws.onopen = () => {
          console.log('WebSocket connected to Python service')
          setWsConnected(true)
          setError(null)
        }
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            
            if (data.type === 'step_recorded') {
              setSteps(prev => [...prev, data.step])
            } else if (data.type === 'step_updated') {
              setSteps(prev => {
                const updated = [...prev]
                updated[data.index] = data.step
                return updated
              })
            } else if (data.type === 'steps_cleared') {
              setSteps([])
            } else if (data.type === 'recording_started') {
              setRecording(true)
            } else if (data.type === 'recording_stopped') {
              setRecording(false)
            } else if (data.type === 'session_stopped') {
              setSessionActive(false)
              setRecording(false)
            } else if (data.type === 'chat_message') {
              setChatMessages(prev => [...prev, data.message])
            }
          } catch (err) {
            console.error('Error parsing WebSocket message:', err)
          }
        }
        
        ws.onclose = () => {
          console.log('WebSocket disconnected')
          setWsConnected(false)
          // Attempt to reconnect after 3 seconds
          setTimeout(connectWebSocket, 3000)
        }
        
        ws.onerror = (error) => {
          console.error('WebSocket error:', error)
          setWsConnected(false)
        }
        
        wsRef.current = ws
      } catch (err) {
        console.error('Failed to connect WebSocket:', err)
        setWsConnected(false)
        setTimeout(connectWebSocket, 3000)
      }
    }

    connectWebSocket()

    return () => {
      if (wsRef.current) {
        wsRef.current.close()
      }
    }
  }, [])

  // Auto-scroll chat to bottom
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [chatMessages])

  const handleApiCall = async (apiCall: () => Promise<any>, successMessage?: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await apiCall()
      if (successMessage) {
        console.log(successMessage, result)
      }
      return result
    } catch (err: any) {
      const errorMessage = err.message || 'An error occurred'
      setError(errorMessage)
      console.error('API call failed:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const createSession = async () => {
    await handleApiCall(async () => {
      const response = await fetch('/api/browser-automation/session', {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error(`Failed to create session: ${response.statusText}`)
      }
      
      const result = await response.json()
      setSessionActive(true)
      return result
    }, 'Browser session created')
  }

  const stopSession = async () => {
    await handleApiCall(async () => {
      const response = await fetch('/api/browser-automation/session', {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error(`Failed to stop session: ${response.statusText}`)
      }
      
      const result = await response.json()
      setSessionActive(false)
      setRecording(false)
      setSteps([])
      setChatMessages([])
      return result
    }, 'Browser session stopped')
  }

  const startRecording = async () => {
    await handleApiCall(async () => {
      const response = await fetch('/api/browser-automation/recording?action=start', {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error(`Failed to start recording: ${response.statusText}`)
      }
      
      return await response.json()
    }, 'Recording started')
  }

  const stopRecording = async () => {
    await handleApiCall(async () => {
      const response = await fetch('/api/browser-automation/recording?action=stop', {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error(`Failed to stop recording: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      // Notify parent component of completion
      if (onExecutionComplete && steps.length > 0) {
        onExecutionComplete(steps)
      }
      
      return result
    }, 'Recording stopped')
  }

  const sendChatMessage = async () => {
    if (!newMessage.trim()) return
    
    setSendingMessage(true)
    const messageToSend = newMessage
    setNewMessage('')
    
    try {
      const response = await fetch('/api/browser-automation/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: messageToSend }),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to send message: ${response.statusText}`)
      }
    } catch (err: any) {
      setError(err.message)
      setNewMessage(messageToSend) // Restore message on error
    } finally {
      setSendingMessage(false)
    }
  }

  const executeTestCase = async () => {
    if (!testCase) return
    
    await handleApiCall(async () => {
      const response = await fetch('/api/browser-automation/execute-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to execute test case: ${response.statusText}`)
      }
      
      return await response.json()
    }, 'Test case execution started')
  }

  const getStepIcon = (step: RecordingStep) => {
    if (step.is_agent_action) {
      return <Bot className="h-4 w-4 text-blue-500" />
    }
    
    switch (step.type) {
      case 'click':
        return <MousePointer className="h-4 w-4 text-green-500" />
      case 'type':
        return <Keyboard className="h-4 w-4 text-blue-500" />
      case 'navigate':
        return <Navigation className="h-4 w-4 text-purple-500" />
      case 'key':
        return <Keyboard className="h-4 w-4 text-orange-500" />
      default:
        return <Activity className="h-4 w-4 text-gray-500" />
    }
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleTimeString()
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
      {/* Control Panel */}
      <Card className="flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Browser Automation Control
          </CardTitle>
          <CardDescription>
            Control the browser automation service and view real-time interactions
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 space-y-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            <Circle className={`h-3 w-3 ${wsConnected ? 'fill-green-500 text-green-500' : 'fill-red-500 text-red-500'}`} />
            <span className="text-sm">
              {wsConnected ? 'Connected to automation service' : 'Disconnected from automation service'}
            </span>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Session Controls */}
          <div className="space-y-2">
            <h3 className="font-medium">Session Control</h3>
            <div className="flex gap-2">
              <Button
                onClick={createSession}
                disabled={sessionActive || loading}
                variant={sessionActive ? "secondary" : "default"}
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                {sessionActive ? 'Session Active' : 'Start Session'}
              </Button>
              <Button
                onClick={stopSession}
                disabled={!sessionActive || loading}
                variant="destructive"
              >
                <Square className="h-4 w-4 mr-2" />
                Stop Session
              </Button>
            </div>
          </div>

          {/* Recording Controls */}
          <div className="space-y-2">
            <h3 className="font-medium">Recording Control</h3>
            <div className="flex gap-2">
              <Button
                onClick={startRecording}
                disabled={!sessionActive || recording || loading}
                variant={recording ? "secondary" : "default"}
              >
                <Circle className={`h-4 w-4 mr-2 ${recording ? 'fill-red-500 text-red-500' : ''}`} />
                {recording ? 'Recording...' : 'Start Recording'}
              </Button>
              <Button
                onClick={stopRecording}
                disabled={!recording || loading}
                variant="outline"
              >
                <Square className="h-4 w-4 mr-2" />
                Stop Recording
              </Button>
            </div>
          </div>

          {/* Test Case Execution */}
          {testCase && (
            <div className="space-y-2">
              <h3 className="font-medium">Test Case Execution</h3>
              <div className="p-3 bg-muted rounded-lg space-y-2">
                <div>
                  <span className="font-medium text-sm">URL:</span>
                  <p className="text-sm text-muted-foreground">{testCase.url}</p>
                </div>
                <div>
                  <span className="font-medium text-sm">User Story:</span>
                  <p className="text-sm text-muted-foreground">{testCase.user_story}</p>
                </div>
                <div>
                  <span className="font-medium text-sm">Instructions:</span>
                  <p className="text-sm text-muted-foreground">{testCase.instructions}</p>
                </div>
              </div>
              <Button
                onClick={executeTestCase}
                disabled={!sessionActive || loading}
                className="w-full"
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Bot className="h-4 w-4 mr-2" />}
                Execute Test Case with AI Agent
              </Button>
            </div>
          )}

          {/* Recorded Steps */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Recorded Steps</h3>
              <Badge variant="secondary">{steps.length} steps</Badge>
            </div>
            <ScrollArea className="h-64 border rounded-lg p-2">
              {steps.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-8">
                  No steps recorded yet. Start a session and begin recording to see interactions.
                </p>
              ) : (
                <div className="space-y-2">
                  {steps.map((step, index) => (
                    <div key={step.id} className="flex items-start gap-2 p-2 rounded border">
                      {getStepIcon(step)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium">{step.description}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline" className="text-xs">
                            {step.type}
                          </Badge>
                          <span className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatTimestamp(step.timestamp)}
                          </span>
                        </div>
                        {step.is_agent_action && (
                          <Badge variant="secondary" className="text-xs mt-1">
                            AI Agent
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>
        </CardContent>
      </Card>

      {/* Chat Panel */}
      <Card className="flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            AI Agent Chat
          </CardTitle>
          <CardDescription>
            Communicate with the AI agent to control browser automation
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          {/* Chat Messages */}
          <ScrollArea className="flex-1 border rounded-lg p-4 mb-4">
            {chatMessages.length === 0 ? (
              <p className="text-sm text-muted-foreground text-center py-8">
                No messages yet. Send a message to start interacting with the AI agent.
              </p>
            ) : (
              <div className="space-y-4">
                {chatMessages.map((message, index) => (
                  <div key={index} className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`flex gap-2 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      <div className="flex-shrink-0">
                        {message.role === 'user' ? (
                          <User className="h-6 w-6 text-blue-500" />
                        ) : (
                          <Bot className="h-6 w-6 text-green-500" />
                        )}
                      </div>
                      <div className={`rounded-lg p-3 ${
                        message.role === 'user' 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-muted'
                      }`}>
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs opacity-70">
                            {formatTimestamp(message.timestamp)}
                          </span>
                          {message.is_step_update && (
                            <Badge variant="outline" className="text-xs">
                              Step Update
                            </Badge>
                          )}
                          {message.is_final_result && (
                            <Badge variant="secondary" className="text-xs">
                              Final Result
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={chatEndRef} />
              </div>
            )}
          </ScrollArea>

          {/* Message Input */}
          <div className="flex gap-2">
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type a message to the AI agent..."
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault()
                  sendChatMessage()
                }
              }}
              disabled={!sessionActive || sendingMessage}
            />
            <Button
              onClick={sendChatMessage}
              disabled={!sessionActive || !newMessage.trim() || sendingMessage}
            >
              {sendingMessage ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}