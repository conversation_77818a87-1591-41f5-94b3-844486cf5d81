"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Zap,
  Plus,
  X,
  FileText,
  Globe,
  Code,
  Eye,
  Play,
  Save,
  User,
  Loader2
} from "lucide-react";
import { TestCaseCreateInput, ComputedVariable } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { TestStepsEditor } from "./TestStepsEditor";
import { VariableSelector } from "../ui/VariableSelector";
import { FileUpload } from "../ui/file-upload";

interface TestStep {
  id: string;
  text: string;
}

interface ModernTestCreatorProps {
  onSubmit: (data: TestCaseCreateInput) => Promise<void>;
  isSubmitting: boolean;
  availableVariables?: ComputedVariable[];  // Variables disponibles del proyecto y suite
}

export function ModernTestCreator({ onSubmit, isSubmitting, availableVariables = [] }: ModernTestCreatorProps) {
  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [testSteps, setTestSteps] = useState<TestStep[]>([]);
  const [contentMode, setContentMode] = useState<"guided" | "freeform">("guided");
  const [freeTextContent, setFreeTextContent] = useState("");
  const [userStory, setUserStory] = useState("");
  const [gherkin, setGherkin] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  
  const { toast } = useToast();

  // Variables management
  const handleVariableInsert = (variable: ComputedVariable) => {
    if (contentMode === "freeform") {
      // Insert at current cursor position or at the end
      const variableText = `{${variable.name}}`;
      setFreeTextContent(prev => prev + (prev ? ' ' : '') + variableText);
    } else {
      // For guided mode, we could add it to the last step or create a new one
      if (testSteps.length === 0) {
        addStep();
      }
      const lastStep = testSteps[testSteps.length - 1];
      const variableText = `{${variable.name}}`;
      const updatedText = lastStep.text + (lastStep.text ? ' ' : '') + variableText;
      updateStep(lastStep.id, updatedText);
    }
  };

  // Bidirectional sync functions for hybrid content
  const syncGuidedToFreeText = () => {
    if (testSteps.length > 0) {
      const formattedText = testSteps.map((step, index) => `${index + 1}. ${step.text}`).join('\n');
      setFreeTextContent(formattedText);
    }
  };

  const syncFreeTextToGuided = () => {
    if (freeTextContent.trim()) {
      const lines = freeTextContent.trim().split('\n').filter(line => line.trim());
      const parsedSteps: TestStep[] = lines.map((line, index) => {
        const cleanText = line.replace(/^\s*(\d+[\.\)]\s*|Step\s*\d+:\s*)/i, '').trim();
        return {
          id: `step-${Date.now()}-${index}`,
          text: cleanText || `Step ${index + 1}`
        };
      });
      setTestSteps(parsedSteps);
    }
  };

  const handleContentModeChange = (newMode: "guided" | "freeform") => {
    if (newMode === contentMode) return;
    
    if (newMode === "freeform") {
      syncGuidedToFreeText();
    } else {
      syncFreeTextToGuided();
    }
    
    setContentMode(newMode);
  };

  // Add step function
  const addStep = () => {
    const newStep: TestStep = {
      id: `step-${Date.now()}`,
      text: ""
    };
    setTestSteps([...testSteps, newStep]);
  };



  // Update step function
  const updateStep = (stepId: string, text: string) => {
    setTestSteps(testSteps.map(step => 
      step.id === stepId ? { ...step, text } : step
    ));
  };

  // Add tag function
  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  // Remove tag function
  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!name.trim()) {
      toast({
        title: "Name required",
        description: "Please enter a test case name",
        variant: "destructive"
      });
      return;
    }

    // Sync content based on current mode before submission
    let stepsText = "";
    if (contentMode === "guided") {
      if (testSteps.length === 0) {
        toast({
          title: "Steps required", 
          description: "Please add at least one test step",
          variant: "destructive"
        });
        return;
      }
      stepsText = testSteps.map((step, index) => `${index + 1}. ${step.text}`).join('\n');
    } else {
      if (!freeTextContent.trim()) {
        toast({
          title: "Steps required", 
          description: "Please enter test steps",
          variant: "destructive"
        });
        return;
      }
      stepsText = freeTextContent;
    }

    const testData: TestCaseCreateInput = {
      name: name.trim(),
      description: description.trim() || `Test: ${name.trim()}`,
      instructions: stepsText,
      user_story: userStory.trim(),
      gherkin: gherkin.trim(),
      url: "",
      tags: tags.length > 0 ? tags : ["automated"],
      priority: "Medium",
      type: "Functional",
      file_attachments: attachedFiles.length > 0 ? attachedFiles : undefined,
    };

    try {
      await onSubmit(testData);
      // Reset form on success
      setName("");
      setDescription("");
      setTestSteps([]);
      setFreeTextContent("");
      setUserStory("");
      setGherkin("");
      setTags([]);
      setAttachedFiles([]);
    } catch (error) {
      console.error("Error submitting test:", error);
    }
  };

  return (
    <div className="h-full grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Content - 2/3 width */}
      <div className="lg:col-span-2 space-y-6">
        {/* Header */}
        <div>
          <p className="text-gray-600">Build automated tests with our visual editor or choose from templates</p>
        </div>

        {/* Main Tabs */}
        <Tabs defaultValue="visual" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Quick Templates
            </TabsTrigger>
            <TabsTrigger value="visual" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Visual Builder
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Advanced Options
            </TabsTrigger>
          </TabsList>

          {/* Quick Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Test Templates</CardTitle>
                <p className="text-sm text-muted-foreground">Start with pre-built test templates</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col gap-2"
                    onClick={() => {
                      setName("User Login Test");
                      setDescription("Test user authentication flow");
                      setTestSteps([
                        { id: "1", text: "Navigate to login page" },
                        { id: "2", text: "Enter valid credentials" },
                        { id: "3", text: "Click login button" },
                        { id: "4", text: "Verify successful login" }
                      ]);
                    }}
                  >
                    <User className="h-6 w-6" />
                    <span className="text-sm">Login Flow</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col gap-2"
                    onClick={() => {
                      setName("Form Submission Test");
                      setDescription("Test form validation and submission");
                      setTestSteps([
                        { id: "1", text: "Fill out form fields" },
                        { id: "2", text: "Submit form" },
                        { id: "3", text: "Verify success message" }
                      ]);
                    }}
                  >
                    <FileText className="h-6 w-6" />
                    <span className="text-sm">Form Test</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col gap-2"
                    onClick={() => {
                      setName("Navigation Test");
                      setDescription("Test site navigation and links");
                      setTestSteps([
                        { id: "1", text: "Click navigation menu" },
                        { id: "2", text: "Navigate to different pages" },
                        { id: "3", text: "Verify page loads correctly" }
                      ]);
                    }}
                  >
                    <Globe className="h-6 w-6" />
                    <span className="text-sm">Navigation</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-20 flex-col gap-2"
                    onClick={() => {
                      setName("Custom Test");
                      setDescription("");
                      setTestSteps([]);
                    }}
                  >
                    <Plus className="h-6 w-6" />
                    <span className="text-sm">Start Fresh</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Visual Builder Tab */}
          <TabsContent value="visual" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Test Configuration</CardTitle>
                <p className="text-sm text-muted-foreground">Basic information about your test case</p>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Test Name */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Test Name</label>
                  <Input
                    placeholder="Enter test name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                  />
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    placeholder="Describe what this test validates"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Test Steps */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle>Test Steps</CardTitle>
                    <p className="text-sm text-muted-foreground">Define the actions to perform during testing</p>
                  </div>
                  {availableVariables.length > 0 && (
                    <VariableSelector 
                      variables={availableVariables}
                      onVariableInsert={handleVariableInsert}
                    />
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={contentMode} onValueChange={(value) => handleContentModeChange(value as "guided" | "freeform")}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="guided">Guided Steps</TabsTrigger>
                    <TabsTrigger value="freeform">Free Text</TabsTrigger>
                  </TabsList>
                  
                  <div className="mt-4">
                    <TabsContent value="guided" className="mt-0">
                      <TestStepsEditor 
                        steps={testSteps}
                        onChange={setTestSteps}
                      />
                    </TabsContent>
                    
                    <TabsContent value="freeform" className="mt-0">
                      <Textarea
                        placeholder="Enter test steps (one per line)..."
                        value={freeTextContent}
                        onChange={(e) => setFreeTextContent(e.target.value)}
                        className="min-h-[150px] resize-none"
                      />
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Advanced Options Tab */}
          <TabsContent value="advanced" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              {/* User Story */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    User Story (Optional)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="As a [user type], I want to [action] so that [benefit]...&#10;&#10;Example:&#10;As a customer, I want to log into my account so that I can view my order history."
                    value={userStory}
                    onChange={(e) => setUserStory(e.target.value)}
                    rows={4}
                  />
                </CardContent>
              </Card>

              {/* Gherkin */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Code className="h-5 w-5" />
                    Gherkin Scenario (Optional)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Feature: User Login&#10;&#10;Scenario: Successful login&#10;  Given I am on the login page&#10;  When I enter valid credentials&#10;  And I click the login button&#10;  Then I should be logged in successfully"
                    value={gherkin}
                    onChange={(e) => setGherkin(e.target.value)}
                    rows={6}
                    className="font-mono text-sm"
                  />
                </CardContent>
              </Card>

              {/* File Attachments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    File Attachments (Optional)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <FileUpload
                    onFilesChange={setAttachedFiles}
                    maxFiles={5}
                    maxSize={10 * 1024 * 1024} // 10MB
                    acceptedFileTypes={[".pdf", ".png", ".jpg", ".jpeg", ".gif", ".txt", ".doc", ".docx"]}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Preview Panel - 1/3 width */}
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Preview
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Test Name */}
            <div>
              <p className="text-sm font-medium text-gray-600">Test Name</p>
              <p className="text-base">{name || "Untitled Test"}</p>
            </div>

            {/* Type and Priority */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Type</p>
                <Badge variant="secondary">Functional</Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Priority</p>
                <Badge variant="default">Medium</Badge>
              </div>
            </div>

            {/* Steps */}
            <div>
              <p className="text-sm font-medium text-gray-600">Steps ({contentMode === "guided" ? "Guided" : "Free Text"})</p>
              {contentMode === "guided" ? (
                <>
                  <p className="text-sm text-muted-foreground">{testSteps.length} steps defined</p>
                  {testSteps.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {testSteps.slice(0, 3).map((step, index) => (
                        <p key={step.id} className="text-xs text-gray-500">
                          {index + 1}. {step.text || "Empty step"}
                        </p>
                      ))}
                      {testSteps.length > 3 && (
                        <p className="text-xs text-gray-400">... and {testSteps.length - 3} more</p>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <>
                  <p className="text-sm text-muted-foreground">
                    {freeTextContent.trim() ? `${freeTextContent.split('\n').filter(line => line.trim()).length} lines` : "No content"}
                  </p>
                  {freeTextContent.trim() && (
                    <div className="mt-2 max-h-20 overflow-hidden">
                      <p className="text-xs text-gray-500 whitespace-pre-line">
                        {freeTextContent.substring(0, 100)}{freeTextContent.length > 100 ? "..." : ""}
                      </p>
                    </div>
                  )}
                </>
              )}
            </div>

            {/* Tags */}
            <div>
              <p className="text-sm font-medium text-gray-600">Tags</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {tags.length === 0 && (
                  <p className="text-sm text-muted-foreground">No tags</p>
                )}
              </div>
            </div>

            {/* File Attachments */}
            <div>
              <p className="text-sm font-medium text-gray-600">Attachments</p>
              <div className="mt-1">
                {attachedFiles.length > 0 ? (
                  <div className="space-y-1">
                    {attachedFiles.map((file, index) => (
                      <p key={index} className="text-xs text-gray-500">
                        📎 {file.name} ({(file.size / 1024).toFixed(1)} KB)
                      </p>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No attachments</p>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-2 pt-4 border-t">
              <p className="text-sm font-medium text-gray-600">Actions</p>
              <Button 
                onClick={handleSubmit} 
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-purple-600 to-orange-500 hover:from-purple-700 hover:to-orange-600"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Test Case
                  </>
                )}
              </Button>
              <Button variant="outline" className="w-full" disabled>
                <Play className="mr-2 h-4 w-4" />
                Run Test
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tags Management */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Tags</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex gap-2">
                <Input
                  placeholder="Add tag..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                  className="text-sm"
                />
                <Button onClick={addTag} size="sm" variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}