/**
 * Client-side utilities that safely handle browser APIs
 * These functions check for client-side environment before accessing browser APIs
 */

export function getCookie(name: string): string | null {
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return null;
  }
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}

export function setCookie(name: string, value: string, options: { path?: string; maxAge?: number } = {}): void {
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    return;
  }
  
  const { path = '/', maxAge } = options;
  let cookieString = `${name}=${value}; path=${path}`;
  
  if (maxAge) {
    cookieString += `; max-age=${maxAge}`;
  }
  
  document.cookie = cookieString;
}

export function isClient(): boolean {
  return typeof window !== 'undefined';
}
