"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  ArrowLeft, 
  Lightbulb, 
  Sparkles, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Copy,
  Download,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { enhanceUserStory } from '@/lib/api/ai';

interface SelectedProject {
  id: string;
  name: string;
  description?: string;
}

interface UserStoryEnhancementProps {
  project: SelectedProject;
  onBack: () => void;
}

interface EnhancementResult {
  enhancedStory: string;
  suggestions: string[];
  enhancementType: string;
  originalStory: string;
  context?: string;
  llmMetadata?: {
    provider: string;
    model: string;
    usage?: {
      promptTokens?: number;
      completionTokens?: number;
      totalTokens?: number;
      estimatedCost?: number;
    };
  };
}

const enhancementTypes = [
  { value: 'detailed', label: 'Detallado', description: 'Análisis completo con criterios de aceptación' },
  { value: 'concise', label: 'Conciso', description: 'Mejoras esenciales manteniendo brevedad' },
  { value: 'technical', label: 'Técnico', description: 'Enfoque en aspectos técnicos y implementación' },
  { value: 'business', label: 'Negocio', description: 'Enfoque en valor de negocio y usuarios' },
  { value: 'testing', label: 'Testing', description: 'Optimizado para generación de casos de prueba' },
];

export function UserStoryEnhancement({ project, onBack }: UserStoryEnhancementProps) {
  const [userStory, setUserStory] = useState('');
  const [context, setContext] = useState('');
  const [enhancementType, setEnhancementType] = useState('detailed');
  const [enhancementResult, setEnhancementResult] = useState<EnhancementResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [tenantId, setTenantId] = useState('default-tenant'); // This should come from auth context
  const { toast } = useToast();

  const handleEnhance = async () => {
    if (!userStory.trim()) {
      toast({
        title: "Historia requerida",
        description: "Por favor ingresa una historia de usuario para mejorar",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      setEnhancementResult(null);

      // Use the API function instead of direct fetch
      const result = await enhanceUserStory({
        userStory: userStory.trim(),
        language: 'es' // Default to Spanish
      });

      if (result.enhancedUserStory) {
        setEnhancementResult({
          enhancedStory: result.enhancedUserStory,
          suggestions: [], // The API doesn't return suggestions separately
          enhancementType,
          originalStory: userStory,
          context: context || undefined,
          // Note: llmMetadata would need to be added to the API response if needed
        });
        toast({
          title: "Historia mejorada",
          description: "La historia de usuario ha sido mejorada exitosamente",
        });
      } else {
        throw new Error('No enhanced story received from API');
      }

    } catch (error) {
      console.error('Enhancement error:', error);
      toast({
        title: "Error en la mejora",
        description: error instanceof Error ? error.message : 'Error desconocido al mejorar la historia',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copiado",
      description: "Texto copiado al portapapeles",
    });
  };

  const downloadAsText = () => {
    if (!enhancementResult) return;

    const content = `Historia Original:
${enhancementResult.originalStory}

Historia Mejorada:
${enhancementResult.enhancedStory}

Sugerencias:
${enhancementResult.suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n')}

Tipo de mejora: ${enhancementResult.enhancementType}
Proyecto: ${project.name}
Fecha: ${new Date().toLocaleString('es-ES')}
`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `historia-mejorada-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const reset = () => {
    setUserStory('');
    setContext('');
    setEnhancementResult(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft size={16} />
            </Button>
            <div className="flex items-center gap-2">
              <Lightbulb size={20} className="text-purple-500" />
              <CardTitle>Mejora de Historias - {project.name}</CardTitle>
            </div>
          </div>
          <CardDescription>
            Utiliza IA para enriquecer y mejorar historias de usuario con contexto del Knowledge Base
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Historia de Usuario</CardTitle>
              <CardDescription>
                Ingresa la historia que quieres mejorar
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="story">Historia de Usuario *</Label>
                <Textarea
                  id="story"
                  placeholder="Como [rol], quiero [funcionalidad] para [beneficio]..."
                  value={userStory}
                  onChange={(e) => setUserStory(e.target.value)}
                  rows={6}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Ejemplo: "Como usuario administrador, quiero poder gestionar usuarios para mantener la seguridad del sistema"
                </p>
              </div>

              <div>
                <Label htmlFor="context">Contexto Adicional (opcional)</Label>
                <Textarea
                  id="context"
                  placeholder="Información adicional sobre el dominio, restricciones, o requisitos específicos..."
                  value={context}
                  onChange={(e) => setContext(e.target.value)}
                  rows={3}
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="enhancement-type">Tipo de Mejora</Label>
                <Select value={enhancementType} onValueChange={setEnhancementType}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {enhancementTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        <div>
                          <div className="font-medium">{type.label}</div>
                          <div className="text-xs text-muted-foreground">{type.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2 pt-2">
                <Button 
                  onClick={handleEnhance} 
                  disabled={loading || !userStory.trim()}
                  className="flex-1"
                >
                  {loading ? (
                    <>
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                      Mejorando...
                    </>
                  ) : (
                    <>
                      <Sparkles size={16} className="mr-2" />
                      Mejorar Historia
                    </>
                  )}
                </Button>
                {enhancementResult && (
                  <Button variant="outline" onClick={reset}>
                    Nueva Historia
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Tips */}
          <Alert>
            <Lightbulb size={16} />
            <AlertDescription>
              <strong>Tips para mejores resultados:</strong>
              <ul className="mt-2 text-sm space-y-1">
                <li>• Incluye el rol específico del usuario</li>
                <li>• Describe claramente la funcionalidad deseada</li>
                <li>• Explica el beneficio o valor esperado</li>
                <li>• Agrega contexto técnico o de negocio relevante</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {loading && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <RefreshCw size={32} className="mx-auto mb-4 animate-spin text-muted-foreground" />
                    <p className="text-muted-foreground">Analizando historia con IA...</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      Consultando Knowledge Base del proyecto
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {enhancementResult && (
            <>
              {/* Enhanced Story */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle size={20} className="text-green-500" />
                      Historia Mejorada
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => copyToClipboard(enhancementResult.enhancedStory)}
                      >
                        <Copy size={16} />
                      </Button>
                      <Button variant="outline" size="sm" onClick={downloadAsText}>
                        <Download size={16} />
                      </Button>
                    </div>
                  </div>
                  <CardDescription>
                    Historia optimizada por IA con contexto del proyecto
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <p className="whitespace-pre-wrap">{enhancementResult.enhancedStory}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Suggestions */}
              {enhancementResult.suggestions.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertCircle size={20} className="text-blue-500" />
                      Sugerencias de Mejora
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {enhancementResult.suggestions.map((suggestion, index) => (
                        <div key={index} className="flex gap-3">
                          <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium flex-shrink-0 mt-0.5">
                            {index + 1}
                          </div>
                          <p className="text-sm">{suggestion}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Metadata */}
              {enhancementResult.llmMetadata && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock size={20} className="text-gray-500" />
                      Información del Procesamiento
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Proveedor:</span>
                        <Badge variant="secondary">{enhancementResult.llmMetadata.provider}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Modelo:</span>
                        <span>{enhancementResult.llmMetadata.model}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tipo de mejora:</span>
                        <Badge variant="outline">{enhancementResult.enhancementType}</Badge>
                      </div>
                      {enhancementResult.llmMetadata.usage && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Tokens totales:</span>
                            <span>{enhancementResult.llmMetadata.usage.totalTokens || 'N/A'}</span>
                          </div>
                          {enhancementResult.llmMetadata.usage.estimatedCost && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Costo estimado:</span>
                              <span>${enhancementResult.llmMetadata.usage.estimatedCost.toFixed(4)}</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}

          {!loading && !enhancementResult && (
            <Card className="border-dashed">
              <CardContent className="pt-6">
                <div className="text-center py-8 text-muted-foreground">
                  <Sparkles size={48} className="mx-auto mb-4" />
                  <p>Los resultados de la mejora aparecerán aquí</p>
                  <p className="text-sm mt-2">
                    Completa la historia de usuario y haz clic en "Mejorar Historia"
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}