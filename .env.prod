# ===========================================
# Configuración de Producción - QAK
# Generado automáticamente: Mon Sep 15 22:24:46 -03 2025
# ===========================================

# Supabase Cloud Configuration (Sincronizado con desarrollo)
SUPABASE_URL=https://qekegvxuykuuokipfple.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFla2Vndnh1eWt1dW9raXBmcGxlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTc5Nzc2NDksImV4cCI6MjA3MzU1MzY0OX0.7sFgaSFtsiwdU4uKWjkXKVNlykK62A7itHGnx81kM-I
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFla2Vndnh1eWt1dW9raXBmcGxlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Nzk3NzY0OSwiZXhwIjoyMDczNTUzNjQ5fQ.WgHMszFAfr79TYIacsxADhXTOMIqqiGIkmR-FbKNias

# Database Configuration (Supabase PostgreSQL)
DATABASE_URL=postgresql://postgres:[YOUR_DB_PASSWORD]@db.qekegvxuykuuokipfple.supabase.co:5432/postgres
POSTGRES_PASSWORD=[CONFIGURAR_EN_SUPABASE]

# JWT Configuration
JWT_SECRET=sb_publishable_S4WBYm84Tst7ffUcKkywOw_dEL2X5aIsb_publishable_S4WBYm84Tst7ffUcKkywOw_dEL2X5aI
JWT_EXPIRY=7d

# NextAuth Configuration
NEXTAUTH_URL=https://tu-dominio.com
NEXTAUTH_SECRET=SjybDPRd/AwmWPTZJvUT1mZ4dsdr9ongFWGr27D6d9A=

# Google OAuth (si está habilitado)
ENABLE_GOOGLE_OAUTH=true
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Steel Browser Configuration
STEEL_DOMAIN=steel.tu-dominio.com
STEEL_CDP_DOMAIN=cdp.tu-dominio.com

# AI Configuration (Sincronizado con desarrollo)
GEMINI_API_KEY=[CONFIGURAR_SI_USAS_GEMINI]
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4
AGENT_MAX_STEPS=10

# URLs Configuration
API_EXTERNAL_URL=https://api.tu-dominio.com
SITE_URL=https://tu-dominio.com

# Authentication Settings
DISABLE_SIGNUP=false

# PostgREST Configuration
PGRST_DB_SCHEMAS=public,auth,storage

# Image Proxy Configuration
IMGPROXY_ENABLE_WEBP_DETECTION=true

# ===========================================
# NOTAS IMPORTANTES:
# 1. Reemplaza 'tu-dominio.com' con tu dominio real
# 2. Configura las claves de AI si las necesitas
# 3. Ajusta DISABLE_SIGNUP según tus necesidades
# 4. Configura SSL/TLS para producción
# ===========================================