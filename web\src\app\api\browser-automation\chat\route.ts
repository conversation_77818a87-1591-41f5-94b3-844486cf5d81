import { NextRequest } from 'next/server'
import { BrowserAutomationApiHandler } from '@/lib/api-supabase/browser-automation'

export async function GET(request: NextRequest) {
  const handler = new BrowserAutomationApiHandler()
  return handler.handleRequest(() => handler.handleGetChatMessages())
}

export async function POST(request: NextRequest) {
  const handler = new BrowserAutomationApiHandler()
  return handler.handleRequest(async () => {
    const body = await request.json()
    const { message } = body
    
    if (!message) {
      throw new Error('Message is required')
    }
    
    return handler.handleSendChatMessage(message)
  })
}