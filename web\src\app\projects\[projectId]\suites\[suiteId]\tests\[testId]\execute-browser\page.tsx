import { notFound } from 'next/navigation'
import { BrowserAutomationPanel } from '@/components/browser-automation/BrowserAutomationPanel'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Bot, Monitor } from 'lucide-react'
import Link from 'next/link'

interface PageProps {
  params: Promise<{
    projectId: string
    suiteId: string
    testId: string
  }>
}

async function getTestCase(projectId: string, suiteId: string, testId: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:9001'}/api/projects/${projectId}/suites/${suiteId}/test-cases/${testId}`,
      {
        cache: 'no-store',
      }
    )

    if (!response.ok) {
      return null
    }

    const result = await response.json()
    return result.data
  } catch (error) {
    console.error('Error fetching test case:', error)
    return null
  }
}

export default async function ExecuteBrowserPage({ params }: PageProps) {
  const resolvedParams = await params
  const testCase = await getTestCase(resolvedParams.projectId, resolvedParams.suiteId, resolvedParams.testId)

  if (!testCase) {
    notFound()
  }

  const handleExecutionComplete = async (steps: any[]) => {
    // Here you could save the execution results back to the test case
    console.log('Test execution completed with steps:', steps)
    
    // Update test case status to completed
    try {
      await fetch(
        `/api/projects/${resolvedParams.projectId}/suites/${resolvedParams.suiteId}/test-cases/${resolvedParams.testId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: 'Passed', // You might want to determine this based on the steps
            last_result: `Executed ${steps.length} steps successfully`,
          }),
        }
      )
    } catch (error) {
      console.error('Error updating test case status:', error)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Link href={`/projects/${resolvedParams.projectId}/suites/${resolvedParams.suiteId}/tests/${resolvedParams.testId}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Test Case
              </Button>
            </Link>
          </div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Bot className="h-8 w-8 text-blue-500" />
            Browser Automation Execution
          </h1>
          <p className="text-muted-foreground">
            Execute test case using AI-powered browser automation
          </p>
        </div>
      </div>

      {/* Test Case Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            Test Case: {testCase.name}
          </CardTitle>
          <CardDescription>
            This test case will be executed using the browser automation service
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span className="font-medium text-sm">Status:</span>
              <div className="mt-1">
                <Badge variant={
                  testCase.status === 'Passed' ? 'default' :
                  testCase.status === 'Failed' ? 'destructive' :
                  testCase.status === 'Running' ? 'secondary' :
                  'outline'
                }>
                  {testCase.status}
                </Badge>
              </div>
            </div>
            <div>
              <span className="font-medium text-sm">Priority:</span>
              <div className="mt-1">
                <Badge variant={
                  testCase.priority === 'Critical' ? 'destructive' :
                  testCase.priority === 'High' ? 'secondary' :
                  'outline'
                }>
                  {testCase.priority}
                </Badge>
              </div>
            </div>
            <div>
              <span className="font-medium text-sm">Type:</span>
              <div className="mt-1">
                <Badge variant="outline">{testCase.type}</Badge>
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <span className="font-medium text-sm">URL:</span>
              <p className="text-sm text-muted-foreground mt-1">{testCase.url}</p>
            </div>
            <div>
              <span className="font-medium text-sm">User Story:</span>
              <p className="text-sm text-muted-foreground mt-1">{testCase.user_story}</p>
            </div>
            <div>
              <span className="font-medium text-sm">Instructions:</span>
              <p className="text-sm text-muted-foreground mt-1 whitespace-pre-wrap">{testCase.instructions}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Browser Automation Panel */}
      <div className="h-[calc(100vh-400px)]">
        <BrowserAutomationPanel
          testCase={{
            instructions: testCase.instructions,
            user_story: testCase.user_story,
            url: testCase.url,
          }}
          onExecutionComplete={handleExecutionComplete}
        />
      </div>
    </div>
  )
}