// Test script for date-utils.ts
// Run with: node test-date-utils.js

const { formatSafeDate, parseSafeDate, isValidDate } = require('./src/lib/date-utils.ts');

console.log('Testing formatSafeDate function...\n');

// Test cases that could cause "Invalid time value" error
const testCases = [
  { input: null, description: 'null value' },
  { input: undefined, description: 'undefined value' },
  { input: '', description: 'empty string' },
  { input: 'invalid-date', description: 'invalid date string' },
  { input: '2024-01-01T10:30:00Z', description: 'valid ISO string' },
  { input: '2024-01-01', description: 'valid date string' },
  { input: new Date(), description: 'valid Date object' },
  { input: new Date('invalid'), description: 'invalid Date object' },
  { input: 'Not a date at all', description: 'completely invalid string' },
  { input: '0000-00-00T00:00:00Z', description: 'zero date' },
];

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`Input: ${testCase.input}`);
  
  try {
    const result = formatSafeDate(testCase.input);
    console.log(`Result: "${result}"`);
    console.log(`✅ No error thrown\n`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}\n`);
  }
});

console.log('All tests completed!');
