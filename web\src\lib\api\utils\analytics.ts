/**
 * Analytics and Reporting Functions
 * 
 * Functions for generating analytics, reports, and data insights.
 * This module provides both current and legacy analytics endpoints.
 * 
 * @module AnalyticsAPI
 */

import { fetchApi } from '../core/fetch';

// ============================================================================
// ANALYTICS TYPES
// ============================================================================

export type AnalyticsResponse = {
  totalTests: number;
  testsRun: number;
  passRate: number; // 0-1
  avgDuration: number; // seconds
  dailyExecutions: Array<{
    date: string;
    passed: number;
    failed: number;
    total: number;
  }>;
  suiteStats: Array<{
    suiteId: string;
    suiteName: string;
    // Lifetime stats from summary
    lifetimeTests: number;
    lifetimeRuns: number;
    lifetimePassed: number;
    lifetimeFailed: number;
    lifetimeSuccessRate: number; // 0-1
    lastExecutionAt: string | null;
    // Date-range specific stats
    runsInDateRange: number;
    passedInDateRange: number;
  }>;
  testDetails?: Array<{
    testId: string;
    testName: string;
    suiteName: string;
    suiteId: string;
    projectId: string;
    status: string;
    lastExecution: string;
    duration?: number; // seconds - new field from v2
  }>;
  // New fields from analytics-v2 endpoint
  dataSource?: string; // Indicates data source ("ExecutionRepository")
  dateRange?: {
    start: string;
    end: string;
  };
};

// ============================================================================
// ANALYTICS FUNCTIONS
// ============================================================================

/**
 * Get analytics data using the new improved endpoint.
 * Uses ExecutionRepository as single source of truth for consistent data.
 * @param params - Analytics parameters
 * @returns Promise<AnalyticsResponse> Analytics data
 * @example
 * ```typescript
 * const analytics = await getAnalytics({
 *   startDate: '2024-01-01',
 *   endDate: '2024-01-31',
 *   projectId: 'project-123',
 *   detailed: true
 * });
 * console.log(`Pass rate: ${(analytics.passRate * 100).toFixed(1)}%`);
 * ```
 */
export const getAnalytics = (params?: {
  startDate?: string;
  endDate?: string;
  projectId?: string;
  suiteId?: string;
  detailed?: boolean;
}): Promise<AnalyticsResponse> => {
  const query = new URLSearchParams();
  if (params) {
    if (params.startDate) query.append('start_date', params.startDate);
    if (params.endDate) query.append('end_date', params.endDate);
    if (params.projectId) query.append('project_id', params.projectId);
    if (params.suiteId) query.append('suite_id', params.suiteId);
    if (params.detailed) query.append('detailed', 'true');
  }
  const qs = query.toString();
  return fetchApi<AnalyticsResponse>(`/analytics-v2${qs ? `?${qs}` : ''}`);
};

/**
 * Legacy analytics function - will be removed in future versions.
 * @deprecated Use getAnalytics() instead. This function uses the legacy endpoint.
 * @param params - Analytics parameters
 * @returns Promise<AnalyticsResponse> Analytics data
 */
export const getAnalyticsLegacy = (params?: {
  startDate?: string;
  endDate?: string;
  projectId?: string;
  suiteId?: string;
  detailed?: boolean;
  includeHistory?: boolean;
}): Promise<AnalyticsResponse> => {
  const query = new URLSearchParams();
  if (params) {
    if (params.startDate) query.append('start_date', params.startDate);
    if (params.endDate) query.append('end_date', params.endDate);
    if (params.projectId) query.append('project_id', params.projectId);
    if (params.suiteId) query.append('suite_id', params.suiteId);
    if (params.detailed) query.append('detailed', 'true');
    if (params.includeHistory) query.append('include_history', 'true');
  }
  const qs = query.toString();
  return fetchApi<AnalyticsResponse>(`/analytics${qs ? `?${qs}` : ''}`);
};

/**
 * Compare analytics data between legacy and new systems.
 * Useful for debugging and validation during migration.
 * @param params - Comparison parameters
 * @returns Promise<any> Comparison data
 */
export const compareAnalytics = (params?: {
  startDate?: string;
  endDate?: string;
  projectId?: string;
  suiteId?: string;
}): Promise<any> => {
  const query = new URLSearchParams();
  if (params) {
    if (params.startDate) query.append('start_date', params.startDate);
    if (params.endDate) query.append('end_date', params.endDate);
    if (params.projectId) query.append('project_id', params.projectId);
    if (params.suiteId) query.append('suite_id', params.suiteId);
  }
  const qs = query.toString();
  return fetchApi<any>(`/analytics-comparison${qs ? `?${qs}` : ''}`);
};