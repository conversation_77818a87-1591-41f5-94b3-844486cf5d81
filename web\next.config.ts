import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  // Use standalone output only for Docker builds
  output: process.env.DOCKER_BUILD === 'true' ? 'standalone' : undefined,
  serverExternalPackages: ['@supabase/supabase-js'],
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ];
  },
};

export default nextConfig;
