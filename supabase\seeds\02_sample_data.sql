-- Seed: Sample Project Data
-- Description: Creates sample projects, environments, test suites and test cases for development
-- Dependencies: 01_development_users.sql

-- Sample project for the test user
INSERT INTO projects (
  project_id,
  name,
  description,
  tags,
  user_id,
  github_config,
  created_at,
  updated_at
) VALUES (
  '********-0000-0000-0000-************'::uuid,
  'E-commerce Demo',
  'Sample e-commerce application for testing automation',
  ARRAY['demo', 'ecommerce', 'web'],
  '00000000-0000-0000-0000-************'::uuid, -- test user
  '{"repository": "https://github.com/example/ecommerce-demo", "branch": "main"}',
  NOW(),
  NOW()
) ON CONFLICT (project_id) DO NOTHING;

-- Sample environment for the project
INSERT INTO environments (
  env_id,
  project_id,
  name,
  description,
  base_url,
  is_default,
  config_overrides,
  headless,
  wait_time,
  timeout,
  viewport_width,
  viewport_height,
  user_agent,
  tags,
  variables,
  created_at,
  updated_at
) VALUES (
  '*************-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  'Development',
  'Development environment for testing',
  'https://demo-ecommerce.example.com',
  true,
  '{"slowMo": 100}',
  false,
  1000,
  30000,
  1920,
  1080,
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  ARRAY['dev', 'testing'],
  '{"API_KEY": "dev-api-key", "DEBUG": "true"}',
  NOW(),
  NOW()
) ON CONFLICT (env_id) DO NOTHING;

-- Update project with default environment
UPDATE projects 
SET default_environment_id = '*************-0000-0000-************'::uuid
WHERE project_id = '********-0000-0000-0000-************'::uuid;

-- Sample test suite
INSERT INTO test_suites (
  suite_id,
  project_id,
  name,
  description,
  type,
  tags,
  execution_times,
  created_at,
  updated_at
) VALUES (
  '********-0000-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  'User Authentication Suite',
  'Test suite for user login, registration and authentication flows',
  'manual',
  ARRAY['auth', 'critical', 'smoke'],
  0,
  NOW(),
  NOW()
) ON CONFLICT (suite_id) DO NOTHING;

-- Sample test cases
INSERT INTO test_cases (
  id,
  suite_id,
  project_id,
  name,
  description,
  instructions,
  user_story,
  gherkin,
  url,
  tags,
  status,
  priority,
  type,
  created_at,
  updated_at
) VALUES 
(
  '********-0000-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  'User Login - Valid Credentials',
  'Test successful user login with valid email and password',
  '1. Navigate to login page\n2. Enter valid email: <EMAIL>\n3. Enter valid password: password123\n4. Click Login button\n5. Verify user is redirected to dashboard',
  'As a registered user, I want to login with my credentials so that I can access my account',
  'Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in successfully',
  '/login',
  ARRAY['login', 'positive', 'smoke'],
  'Pending',
  'High',
  'Functional',
  NOW(),
  NOW()
),
(
  '********-0000-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  'User Login - Invalid Credentials',
  'Test login failure with invalid credentials',
  '1. Navigate to login page\n2. Enter invalid email: <EMAIL>\n3. Enter invalid password: wrongpassword\n4. Click Login button\n5. Verify error message is displayed',
  'As a user, I want to see an error message when I enter invalid credentials so that I know my login attempt failed',
  'Given I am on the login page\nWhen I enter invalid credentials\nThen I should see an error message',
  '/login',
  ARRAY['login', 'negative', 'validation'],
  'Pending',
  'Medium',
  'Functional',
  NOW(),
  NOW()
) ON CONFLICT (id) DO NOTHING;

-- Sample project variables
INSERT INTO project_variables (
  var_id,
  project_id,
  name,
  value,
  description,
  is_secret,
  tags,
  created_at,
  updated_at
) VALUES 
(
  '*************-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  'TEST_EMAIL',
  '<EMAIL>',
  'Default test user email for login tests',
  false,
  ARRAY['auth', 'credentials'],
  NOW(),
  NOW()
),
(
  '*************-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  'TEST_PASSWORD',
  'password123',
  'Default test user password for login tests',
  true,
  ARRAY['auth', 'credentials'],
  NOW(),
  NOW()
) ON CONFLICT (var_id) DO NOTHING;

-- Sample suite variables
INSERT INTO suite_variables (
  var_id,
  suite_id,
  project_id,
  name,
  value,
  description,
  is_secret,
  overrides_project,
  tags,
  created_at,
  updated_at
) VALUES (
  '*************-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  '********-0000-0000-0000-************'::uuid,
  'LOGIN_TIMEOUT',
  '5000',
  'Timeout for login operations in milliseconds',
  false,
  false,
  ARRAY['timeout', 'performance'],
  NOW(),
  NOW()
) ON CONFLICT (var_id) DO NOTHING;