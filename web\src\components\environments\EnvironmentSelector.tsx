"use client";

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getProjectEnvironments } from '@/lib/api';
import type { Environment } from '@/lib/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Globe, CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnvironmentSelectorProps {
  projectId: string;
  selectedEnvId?: string;
  onSelect: (envId: string) => void;
  className?: string;
  label?: string;
  showLabel?: boolean;
  disabled?: boolean;
}

export function EnvironmentSelector({
  projectId,
  selectedEnvId,
  onSelect,
  className,
  label = "Environment",
  showLabel = true,
  disabled = false
}: EnvironmentSelectorProps) {
  const { data: environments, isLoading, error } = useQuery({
    queryKey: ['environments', projectId],
    queryFn: () => getProjectEnvironments(projectId),
    enabled: !!projectId,
  });

  // Don't show selector if no environments at all
  if (!environments || environments.length === 0) {
    if (isLoading) {
      return (
        <div className={cn("space-y-3", className)}>
          {showLabel && (
            <Label className="text-sm font-medium flex items-center gap-2">
              <div className="w-4 h-4 rounded bg-gray-200 animate-pulse" />
              {label}
            </Label>
          )}
          <div className="flex items-center gap-2 p-3 border border-gray-200 rounded-lg bg-gray-50">
            <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
            <span className="text-sm text-muted-foreground">Loading environments...</span>
          </div>
        </div>
      );
    }
    
    return (
      <div className={cn("space-y-3", className)}>
        {showLabel && (
          <Label className="text-sm font-medium flex items-center gap-2">
            <Globe className="h-4 w-4 text-gray-400" />
            {label}
          </Label>
        )}
        <div className="flex items-center gap-2 p-3 border-2 border-dashed border-gray-200 rounded-lg bg-gray-50">
          <AlertCircle className="h-4 w-4 text-amber-500" />
          <span className="text-sm text-muted-foreground">No environments available for this project</span>
        </div>
      </div>
    );
  }

  // Find selected environment for display
  const selectedEnvironment = environments.find(env => env.env_id === selectedEnvId);
  const defaultEnvironment = environments.find(env => env.is_default);

  return (
    <div className={cn("space-y-3", className)}>
      {showLabel && (
        <Label htmlFor="environment-selector" className="text-sm font-medium flex items-center gap-2">
          <div className="w-4 h-4 rounded bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center">
            <Globe className="h-2.5 w-2.5 text-white" />
          </div>
          {label}
        </Label>
      )}
      
      <Select
        value={selectedEnvId || ''}
        onValueChange={onSelect}
        disabled={disabled || isLoading || environments.length <= 1}
      >
        <SelectTrigger className={cn(
          "w-full h-11 border-2",
          selectedEnvironment?.is_default 
            ? "border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50" 
            : "border-gray-200"
        )} id="environment-selector">
          <SelectValue placeholder={
            isLoading 
              ? "Loading environments..." 
              : environments.length === 1
                ? `${environments[0].name}${environments[0].is_default ? ' (default)' : ''}`
                : defaultEnvironment 
                  ? `Select environment (default: ${defaultEnvironment.name})` 
                  : "Select environment"
          }>
            {selectedEnvironment && (
              <div className="flex items-center gap-3 w-full">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    selectedEnvironment.is_default 
                      ? "bg-gradient-to-r from-blue-500 to-purple-500" 
                      : "bg-gray-400"
                  )} />
                  <Badge variant={selectedEnvironment.is_default ? "default" : "secondary"} className="text-xs">
                    {selectedEnvironment.name}
                  </Badge>
                  {selectedEnvironment.is_default && (
                    <CheckCircle className="h-3 w-3 text-blue-500" />
                  )}
                </div>
                <span className="text-xs text-muted-foreground max-w-48 truncate flex-1 text-left">
                  {selectedEnvironment.base_url}
                </span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent className="max-w-lg">
          {environments.map((env) => (
            <SelectItem key={env.env_id} value={env.env_id} className="py-3">
              <div className="flex items-center gap-3 w-full min-w-0">
                <div className="flex items-center gap-2 flex-shrink-0">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    env.is_default 
                      ? "bg-gradient-to-r from-blue-500 to-purple-500" 
                      : "bg-gray-400"
                  )} />
                  <Badge variant={env.is_default ? "default" : "secondary"} className="text-xs">
                    {env.name}
                  </Badge>
                  {env.is_default && (
                    <CheckCircle className="h-3 w-3 text-blue-500" />
                  )}
                </div>
                <div className="flex-1 min-w-0 text-right">
                  <span className="text-xs text-muted-foreground truncate block">
                    {env.base_url || 'No URL configured'}
                  </span>
                  {env.description && (
                    <span className="text-xs text-muted-foreground/70 truncate block">
                      {env.description}
                    </span>
                  )}
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {error && (
        <div className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="h-4 w-4 text-red-500" />
          <p className="text-sm text-red-600">
            Failed to load environments
          </p>
        </div>
      )}

      {environments.length === 1 && (
        <div className="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded-lg">
          <CheckCircle className="h-4 w-4 text-blue-500" />
          <p className="text-xs text-blue-600">
            Only one environment is available for this project
          </p>
        </div>
      )}
      
      {selectedEnvironment && (
        <div className="space-y-3 p-4 bg-gradient-to-br from-gray-50 to-slate-50 border border-gray-200 rounded-lg">
          <div className="text-xs font-medium text-gray-600 uppercase tracking-wide flex items-center gap-2">
            <Globe className="h-3 w-3" />
            Environment Details
          </div>
          
          <div className="grid grid-cols-1 gap-2 text-xs">
            <div className="flex justify-between items-center">
              <span className="font-medium text-muted-foreground">URL:</span>
              <span className="text-right font-mono">{selectedEnvironment.base_url || "No base URL"}</span>
            </div>
            
            {selectedEnvironment.description && (
              <div className="flex justify-between items-start">
                <span className="font-medium text-muted-foreground">Description:</span>
                <span className="text-right max-w-32 break-words">{selectedEnvironment.description}</span>
              </div>
            )}
            
            {selectedEnvironment.tags.length > 0 && (
              <div className="pt-2 border-t border-gray-200">
                <div className="flex flex-wrap gap-1">
                  {selectedEnvironment.tags.slice(0, 4).map((tag, index) => (
                    <Badge key={`${selectedEnvironment.env_id}-tag-${index}-${tag}`} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {selectedEnvironment.tags.length > 4 && (
                    <Badge variant="outline" className="text-xs">
                      +{selectedEnvironment.tags.length - 4}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default EnvironmentSelector;