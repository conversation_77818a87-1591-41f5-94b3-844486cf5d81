"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, FolderKanban, Users, Calendar, ChevronRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getProjects } from '@/lib/api/projects';

interface Project {
  id: string;
  name: string;
  description?: string;
  status?: string;
  createdAt?: string;
  testSuiteCount?: number;
  testCaseCount?: number;
}

interface ProjectSelectorProps {
  onProjectSelect: (project: { id: string; name: string; description?: string }) => void;
}

export function ProjectSelector({ onProjectSelect }: ProjectSelectorProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    if (searchTerm.trim()) {
      const filtered = projects.filter(project =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProjects(filtered);
    } else {
      setFilteredProjects(projects);
    }
  }, [searchTerm, projects]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const apiProjects = await getProjects();
      
      // Map API projects to local Project interface
      const projectList = apiProjects.map(project => ({
        id: project.project_id,
        name: project.name,
        description: project.description,
        status: 'active', // Default status
        createdAt: project.created_at,
        updatedAt: project.updated_at,
        testSuiteCount: project.test_suites?.length || 0,
        testCaseCount: project.test_suites?.reduce((count, suite) => count + (suite.test_cases?.length || 0), 0) || 0,
        tags: project.tags || []
      }));
      
      setProjects(projectList);
      setFilteredProjects(projectList);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: "Error",
        description: "No se pudieron cargar los proyectos. Por favor intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch {
      return 'N/A';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-500';
      case 'inactive':
        return 'bg-yellow-500';
      case 'archived':
        return 'bg-gray-500';
      default:
        return 'bg-blue-500';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderKanban size={20} />
            Seleccionar Proyecto Objetivo
          </CardTitle>
          <CardDescription>
            Elige el proyecto donde aplicar las funcionalidades de QAKI
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-muted animate-pulse rounded-lg" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderKanban size={20} />
          Seleccionar Proyecto Objetivo
        </CardTitle>
        <CardDescription>
          Elige el proyecto donde aplicar las funcionalidades de QAKI
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
          <Input
            placeholder="Buscar proyectos..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Projects List */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredProjects.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm ? 'No se encontraron proyectos con ese criterio' : 'No hay proyectos disponibles'}
            </div>
          ) : (
            filteredProjects.map((project) => (
              <div
                key={project.id}
                className="p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer group"
                onClick={() => onProjectSelect({
                  id: project.id,
                  name: project.name,
                  description: project.description
                })}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
                        {project.name}
                      </h3>
                      {project.status && (
                        <Badge variant="secondary" className="text-xs">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(project.status)} mr-1`} />
                          {project.status}
                        </Badge>
                      )}
                    </div>
                    
                    {project.description && (
                      <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
                        {project.description}
                      </p>
                    )}

                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      {project.testSuiteCount !== undefined && (
                        <div className="flex items-center gap-1">
                          <Users size={12} />
                          <span>{project.testSuiteCount} suites</span>
                        </div>
                      )}
                      {project.testCaseCount !== undefined && (
                        <div className="flex items-center gap-1">
                          <FolderKanban size={12} />
                          <span>{project.testCaseCount} tests</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Calendar size={12} />
                        <span>{formatDate(project.createdAt)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <ChevronRight 
                    size={20} 
                    className="text-muted-foreground group-hover:text-primary transition-colors flex-shrink-0 ml-4"
                  />
                </div>
              </div>
            ))
          )}
        </div>

        {/* Create New Project Hint */}
        <div className="text-center pt-4 border-t">
          <p className="text-sm text-muted-foreground">
            ¿No encuentras tu proyecto?{' '}
            <Button variant="link" className="p-0 h-auto" onClick={() => window.location.href = '/projects'}>
              Crear nuevo proyecto
            </Button>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}