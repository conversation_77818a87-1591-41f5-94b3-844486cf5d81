import { NextRequest } from 'next/server'
import { BrowserAutomationApiHandler } from '@/lib/api-supabase/browser-automation'

export async function POST(request: NextRequest) {
  const handler = new BrowserAutomationApiHandler()
  return handler.handleRequest(async () => {
    const body = await request.json()
    const { instructions, user_story, url } = body
    
    if (!instructions || !user_story || !url) {
      throw new Error('instructions, user_story, and url are required')
    }
    
    return handler.handleExecuteTestCase({
      instructions,
      user_story,
      url
    })
  })
}