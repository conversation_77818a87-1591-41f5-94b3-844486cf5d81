"use client";

import React, { useState, useEffect } from 'react';
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Globe, 
  Key, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff, 
  Copy,
  CheckCircle
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface SimplifiedEnvironmentConfigProps {
  value?: EnvironmentConfig;
  onChange: (config: EnvironmentConfig) => void;
}

interface EnvironmentConfig {
  name: string;
  description: string;
  target_url: string;
  variables: Record<string, string>;
  browser_config: {
    headless: boolean;
    wait_time: number;
    timeout: number;
    viewport_width: number;
    viewport_height: number;
  };
  auto_generate_domains: boolean;
  additional_domains: string[];
}

const defaultConfig: EnvironmentConfig = {
  name: '',
  description: '',
  target_url: '',
  variables: {},
  browser_config: {
    headless: false,
    wait_time: 2.0,
    timeout: 30000,
    viewport_width: 1920,
    viewport_height: 1080,
  },
  auto_generate_domains: true,
  additional_domains: [],
};

export function SimplifiedEnvironmentConfig({ value = defaultConfig, onChange }: SimplifiedEnvironmentConfigProps) {
  const [config, setConfig] = useState<EnvironmentConfig>(value);
  const [newVariable, setNewVariable] = useState({ key: '', value: '' });
  const [showVariableValues, setShowVariableValues] = useState<Record<string, boolean>>({});
  const [newDomain, setNewDomain] = useState('');

  useEffect(() => {
    onChange(config);
  }, [config, onChange]);

  // Auto-generate allowed domains from target URL
  const getGeneratedDomains = (targetUrl: string): string[] => {
    if (!targetUrl) return [];
    
    try {
      const url = new URL(targetUrl);
      return [
        `https://${url.hostname}`,
        `https://*.${url.hostname}`,
      ];
    } catch {
      return [];
    }
  };

  const getAllowedDomains = (): string[] => {
    const generated = config.auto_generate_domains ? getGeneratedDomains(config.target_url) : [];
    return [...generated, ...config.additional_domains];
  };

  const updateConfig = (updates: Partial<EnvironmentConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  };

  const addVariable = () => {
    if (!newVariable.key.trim() || !newVariable.value.trim()) {
      toast({
        title: "Invalid variable",
        description: "Both variable name and value are required",
        variant: "destructive",
      });
      return;
    }

    const variableName = newVariable.key.trim().toUpperCase().replace(/\s+/g, '_');
    
    updateConfig({
      variables: {
        ...config.variables,
        [variableName]: newVariable.value.trim()
      }
    });
    
    setNewVariable({ key: '', value: '' });
    toast({
      title: "Variable added",
      description: `${variableName} has been added to your environment variables`,
    });
  };

  const removeVariable = (key: string) => {
    const newVariables = { ...config.variables };
    delete newVariables[key];
    updateConfig({ variables: newVariables });
    
    toast({
      title: "Variable removed",
      description: `${key} has been removed from your environment variables`,
    });
  };

  const toggleVariableVisibility = (key: string) => {
    setShowVariableValues(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const copyVariableReference = (key: string) => {
    const reference = `\${${key}}`;
    navigator.clipboard.writeText(reference);
    toast({
      title: "Copied to clipboard",
      description: `Variable reference ${reference} copied`,
    });
  };

  const addAdditionalDomain = () => {
    if (!newDomain.trim()) return;
    
    updateConfig({
      additional_domains: [...config.additional_domains, newDomain.trim()]
    });
    setNewDomain('');
  };

  const removeAdditionalDomain = (index: number) => {
    const newDomains = config.additional_domains.filter((_, i) => i !== index);
    updateConfig({ additional_domains: newDomains });
  };

  const variableEntries = Object.entries(config.variables);

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Environment Configuration
          </CardTitle>
          <CardDescription>
            Configure your test environment with target URL and variables in one place
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="env-name">Environment Name</Label>
              <Input
                id="env-name"
                value={config.name}
                onChange={(e) => updateConfig({ name: e.target.value })}
                placeholder="e.g., Production, Staging, QA"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="target-url">Target URL</Label>
              <Input
                id="target-url"
                value={config.target_url}
                onChange={(e) => updateConfig({ target_url: e.target.value })}
                placeholder="https://app.example.com"
                type="url"
              />
              {config.target_url && (
                <p className="text-xs text-muted-foreground">
                  ✓ Domain will be automatically configured for sensitive data
                </p>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={config.description}
              onChange={(e) => updateConfig({ description: e.target.value })}
              placeholder="Brief description of this environment"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Environment Variables */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Environment Variables
          </CardTitle>
          <CardDescription>
            Define reusable variables like usernames, passwords, and API keys.
            Use them in test instructions with ${`{VARIABLE_NAME}`} syntax.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {variableEntries.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              <Key className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No variables configured</p>
              <p className="text-sm">Add variables to make your tests more maintainable</p>
            </div>
          ) : (
            <div className="space-y-3">
              {variableEntries.map(([key, value]) => (
                <div
                  key={key}
                  className="flex items-center gap-3 p-3 border rounded-lg bg-muted/30"
                >
                  <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono">
                        ${`{${key}}`}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyVariableReference(key)}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <code className="flex-1 text-sm font-mono bg-background px-2 py-1 rounded border">
                        {showVariableValues[key] ? value : '•'.repeat(value.length)}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleVariableVisibility(key)}
                        className="h-6 w-6 p-0"
                      >
                        {showVariableValues[key] ? (
                          <EyeOff className="h-3 w-3" />
                        ) : (
                          <Eye className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeVariable(key)}
                    className="text-destructive hover:text-destructive h-6 w-6 p-0"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Add new variable */}
          <div className="flex items-end gap-2 pt-2 border-t">
            <div className="flex-1 space-y-2">
              <Label htmlFor="var-name">Variable Name</Label>
              <Input
                id="var-name"
                value={newVariable.key}
                onChange={(e) => setNewVariable({ ...newVariable, key: e.target.value })}
                placeholder="LOGIN_USERNAME, API_TOKEN, etc."
              />
            </div>
            <div className="flex-1 space-y-2">
              <Label htmlFor="var-value">Variable Value</Label>
              <Input
                id="var-value"
                type="password"
                value={newVariable.value}
                onChange={(e) => setNewVariable({ ...newVariable, value: e.target.value })}
                placeholder="Actual value"
              />
            </div>
            <Button onClick={addVariable} className="mb-0">
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>

          {/* Usage example */}
          {variableEntries.length > 0 && (
            <div className="bg-muted/50 p-3 rounded-lg border-l-4 border-primary">
              <p className="text-sm font-medium mb-1">💡 Usage in test instructions:</p>
              <code className="text-xs text-muted-foreground">
                "Navigate to login page and enter ${`{${variableEntries[0][0]}}`} and ${`{LOGIN_PASSWORD}`}"
              </code>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Allowed Domains */}
      <Card>
        <CardHeader>
          <CardTitle>Security & Domain Configuration</CardTitle>
          <CardDescription>
            Configure which domains can access your environment variables for security
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-domains"
              checked={config.auto_generate_domains}
              onCheckedChange={(checked) => updateConfig({ auto_generate_domains: checked })}
            />
            <Label htmlFor="auto-domains">Auto-generate domains from target URL</Label>
          </div>

          <div>
            <Label className="text-sm font-medium">Allowed Domains</Label>
            <div className="mt-2 space-y-2">
              {getAllowedDomains().map((domain, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Badge variant={config.auto_generate_domains && index < 2 ? "default" : "secondary"}>
                    {domain}
                  </Badge>
                  {config.auto_generate_domains && index < 2 && (
                    <span className="text-xs text-muted-foreground">(auto-generated)</span>
                  )}
                  {(!config.auto_generate_domains || index >= 2) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAdditionalDomain(index - (config.auto_generate_domains ? 2 : 0))}
                      className="h-4 w-4 p-0"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-end gap-2">
            <div className="flex-1 space-y-2">
              <Label htmlFor="additional-domain">Additional Domain</Label>
              <Input
                id="additional-domain"
                value={newDomain}
                onChange={(e) => setNewDomain(e.target.value)}
                placeholder="https://api.example.com"
              />
            </div>
            <Button onClick={addAdditionalDomain}>
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Browser Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Browser Configuration</CardTitle>
          <CardDescription>
            Configure browser settings for test execution
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="headless">Headless Mode</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="headless"
                  checked={config.browser_config.headless}
                  onCheckedChange={(checked) => updateConfig({
                    browser_config: { ...config.browser_config, headless: checked }
                  })}
                />
                <Label htmlFor="headless" className="text-sm text-muted-foreground">
                  Run without UI
                </Label>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="wait-time">Wait Time (seconds)</Label>
              <Input
                id="wait-time"
                type="number"
                step="0.1"
                value={config.browser_config.wait_time}
                onChange={(e) => updateConfig({
                  browser_config: { ...config.browser_config, wait_time: parseFloat(e.target.value) || 2.0 }
                })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="timeout">Timeout (ms)</Label>
              <Input
                id="timeout"
                type="number"
                value={config.browser_config.timeout}
                onChange={(e) => updateConfig({
                  browser_config: { ...config.browser_config, timeout: parseInt(e.target.value) || 30000 }
                })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="viewport-width">Viewport Width</Label>
              <Input
                id="viewport-width"
                type="number"
                value={config.browser_config.viewport_width}
                onChange={(e) => updateConfig({
                  browser_config: { ...config.browser_config, viewport_width: parseInt(e.target.value) || 1920 }
                })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="viewport-height">Viewport Height</Label>
              <Input
                id="viewport-height"
                type="number"
                value={config.browser_config.viewport_height}
                onChange={(e) => updateConfig({
                  browser_config: { ...config.browser_config, viewport_height: parseInt(e.target.value) || 1080 }
                })}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default SimplifiedEnvironmentConfig;