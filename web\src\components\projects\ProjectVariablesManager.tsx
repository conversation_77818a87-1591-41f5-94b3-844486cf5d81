'use client';

import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2, Save, Loader2, Globe2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { getProjectById, updateProject } from '@/lib/api/projects';
import { Project } from '@/lib/types';

interface ProjectVariablesManagerProps {
  projectId: string;
}

interface VariablePair {
  key: string;
  value: string;
}

export default function ProjectVariablesManager({ projectId }: ProjectVariablesManagerProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [variables, setVariables] = useState<VariablePair[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Get project data
  const { data: project, isLoading } = useQuery({
    queryKey: ['project', projectId],
    queryFn: () => getProjectById(projectId)
  });

  // Update variables when project data changes
  React.useEffect(() => {
    if (project?.global_variables) {
      const variableArray = Object.entries(project.global_variables).map(([key, value]) => ({
        key,
        value: String(value)
      }));
      setVariables(variableArray);
    } else {
      setVariables([]);
    }
    setHasUnsavedChanges(false);
  }, [project]);

  // Update project mutation
  const updateMutation = useMutation({
    mutationFn: async (globalVariables: Record<string, string>) => {
      console.log('Updating project global variables:', globalVariables);
      
      return updateProject(projectId, {
        global_variables: globalVariables
      });
    },
    onSuccess: () => {
      toast({
        title: "Variables guardadas",
        description: "Las variables globales del proyecto se guardaron correctamente"
      });
      setHasUnsavedChanges(false);
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
    },
    onError: (error: any) => {
      console.error('Error updating global variables:', error);
      toast({
        title: "Error",
        description: "No se pudieron guardar las variables globales",
        variant: "destructive"
      });
    }
  });

  const addVariable = () => {
    setVariables([...variables, { key: '', value: '' }]);
    setHasUnsavedChanges(true);
  };

  const removeVariable = (index: number) => {
    const newVariables = variables.filter((_, i) => i !== index);
    setVariables(newVariables);
    setHasUnsavedChanges(true);
  };

  const updateVariable = (index: number, field: 'key' | 'value', newValue: string) => {
    const newVariables = [...variables];
    newVariables[index][field] = newValue;
    setVariables(newVariables);
    setHasUnsavedChanges(true);
  };

  const handleSubmit = () => {
    // Convert array to object, filtering out empty keys
    const globalVariables: Record<string, string> = {};
    variables.forEach(({ key, value }) => {
      if (key.trim()) {
        globalVariables[key.trim()] = value;
      }
    });

    console.log('Submitting global variables:', globalVariables);
    updateMutation.mutate(globalVariables);
  };

  const handleReset = () => {
    if (project && 'global_variables' in project && project.global_variables) {
      const variableArray = Object.entries(project.global_variables).map(([key, value]) => ({
        key,
        value: String(value)
      }));
      setVariables(variableArray);
    } else {
      setVariables([]);
    }
    setHasUnsavedChanges(false);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Cargando variables del proyecto...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe2 className="h-5 w-5" />
          Variables Globales del Proyecto
        </CardTitle>
        <CardDescription>
          Variables compartidas que se heredan automáticamente en todos los ambientes. 
          Los ambientes pueden sobrescribir valores específicos sin duplicar toda la configuración.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {variables.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Globe2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No hay variables globales configuradas</p>
              <p className="text-sm">Agrega variables que se compartirán en todos los ambientes</p>
            </div>
          ) : (
            variables.map((variable, index) => (
              <div key={index} className="grid grid-cols-12 gap-3 items-center">
                <div className="col-span-5">
                  <Label htmlFor={`key-${index}`} className="sr-only">
                    Nombre de variable
                  </Label>
                  <Input
                    id={`key-${index}`}
                    placeholder="Nombre de variable"
                    value={variable.key}
                    onChange={(e) => updateVariable(index, 'key', e.target.value)}
                  />
                </div>
                <div className="col-span-6">
                  <Label htmlFor={`value-${index}`} className="sr-only">
                    Valor
                  </Label>
                  <Input
                    id={`value-${index}`}
                    placeholder="Valor por defecto"
                    value={variable.value}
                    onChange={(e) => updateVariable(index, 'value', e.target.value)}
                  />
                </div>
                <div className="col-span-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeVariable(index)}
                    className="h-9 w-9 p-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>

        <div className="flex justify-between items-center pt-4 border-t">
          <Button variant="outline" onClick={addVariable}>
            <Plus className="h-4 w-4 mr-2" />
            Agregar Variable
          </Button>

          <div className="flex gap-2">
            {hasUnsavedChanges && (
              <Button variant="ghost" onClick={handleReset}>
                Cancelar
              </Button>
            )}
            <Button 
              onClick={handleSubmit} 
              disabled={!hasUnsavedChanges || updateMutation.isPending}
            >
              {updateMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Guardar Variables
            </Button>
          </div>
        </div>

        {hasUnsavedChanges && (
          <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
            <p className="text-sm text-amber-800">
              Tienes cambios sin guardar. No olvides hacer clic en "Guardar Variables" para aplicar los cambios.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}