import { fetchApi } from '../core/fetch';
import { AuthService } from '@/lib/auth';

/**
 * Knowledge Base document upload function with direct fetch
 * This bypasses the standard fetchApi to handle FormData uploads properly
 * 
 * @param file - The file to upload to the knowledge base
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to upload result
 * 
 * @example
 * ```typescript
 * const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
 * const file = fileInput.files?.[0];
 * if (file) {
 *   const result = await uploadKnowledgeBaseDocument(file, "tenant-123");
 *   console.log(`Document uploaded: ${result.document_id}`);
 * }
 * ```
 */
export async function uploadKnowledgeBaseDocument(file: File, tenantId: string): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('tenant_id', tenantId);
  
  const response = await fetch('/api/knowledge-base/documents/upload', {
    method: 'POST',
    body: formData,
    headers: {
      'ngrok-skip-browser-warning': 'true',
      ...AuthService.getAuthHeaders(),
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Upload failed: ${response.status} ${errorText}`);
  }

  return response.json();
}

/**
 * Gets knowledge base documents for a specific project
 * 
 * @param tenantId - The tenant identifier
 * @param projectId - The project identifier
 * @returns Promise resolving to documents list
 * 
 * @example
 * ```typescript
 * const documents = await getKnowledgeBaseDocuments("tenant-123", "project-456");
 * documents.forEach(doc => console.log(doc.filename, doc.upload_date));
 * ```
 */
export async function getKnowledgeBaseDocuments(tenantId: string, projectId: string): Promise<any> {
  return fetchApi(`/knowledge-base/documents/${tenantId}/${projectId}`);
}

/**
 * Generates manual test cases from knowledge base documents
 * 
 * @param data - The generation request data including context and preferences
 * @returns Promise resolving to generated test cases
 * 
 * @example
 * ```typescript
 * const testCases = await generateManualTestsFromKB({
 *   project_id: "project-456",
 *   context: "User authentication flow",
 *   language: "es",
 *   test_type: "functional"
 * });
 * ```
 */
export async function generateManualTestsFromKB(data: any): Promise<any> {
  return fetchApi('/knowledge-base/generate-manual-tests', {
    method: 'POST',
    body: JSON.stringify(data)
  });
}