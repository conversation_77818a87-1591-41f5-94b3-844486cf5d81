/**
 * LLM Management Functions
 * 
 * Functions for managing Large Language Model providers, models, and credentials.
 * These functions handle the configuration and testing of AI/ML services.
 * 
 * @module LLMAdmin
 */

import { fetchApi } from '../core/fetch';

// ============================================================================
// LLM PROVIDERS MANAGEMENT
// ============================================================================

/**
 * Get all LLM providers
 * @returns Promise<{ data?: any[]; items?: any[] } | any[]> List of LLM providers
 * @example
 * ```typescript
 * const providers = await getLLMProviders();
 * const providerList = Array.isArray(providers) ? providers : (providers.data || providers.items || []);
 * ```
 */
export const getLLMProviders = async (): Promise<{ data?: any[]; items?: any[] } | any[]> => {
  return fetchApi('/v2/llm/providers');
};

/**
 * Create a new LLM provider
 * @param providerData - Provider configuration data
 * @returns Promise<any> Created provider data
 */
export const createLLMProvider = async (providerData: any): Promise<any> => {
  return fetchApi('/v2/llm/providers', {
    method: 'POST',
    body: JSON.stringify(providerData)
  });
};

/**
 * Update an existing LLM provider
 * @param providerId - Provider ID to update
 * @param providerData - Updated provider data
 * @returns Promise<any> Updated provider data
 */
export const updateLLMProvider = async (providerId: string, providerData: any): Promise<any> => {
  return fetchApi(`/v2/llm/providers/${providerId}`, {
    method: 'PUT',
    body: JSON.stringify(providerData)
  });
};

/**
 * Delete an LLM provider
 * @param providerId - Provider ID to delete
 * @returns Promise<any> Deletion result
 */
export const deleteLLMProvider = async (providerId: string): Promise<any> => {
  return fetchApi(`/v2/llm/providers/${providerId}`, {
    method: 'DELETE'
  });
};

// ============================================================================
// LLM MODELS MANAGEMENT
// ============================================================================

/**
 * Get all LLM models
 * @returns Promise<{ data?: any[]; items?: any[] } | any[]> List of LLM models
 */
export const getLLMModels = async (): Promise<{ data?: any[]; items?: any[] } | any[]> => {
  return fetchApi('/v2/llm/models');
};

/**
 * Create a new LLM model
 * @param modelData - Model configuration data
 * @returns Promise<any> Created model data
 */
export const createLLMModel = async (modelData: any): Promise<any> => {
  return fetchApi('/v2/llm/models', {
    method: 'POST',
    body: JSON.stringify(modelData)
  });
};

/**
 * Update an existing LLM model
 * @param modelId - Model ID to update
 * @param modelData - Updated model data
 * @returns Promise<any> Updated model data
 */
export const updateLLMModel = async (modelId: string, modelData: any): Promise<any> => {
  return fetchApi(`/v2/llm/models/${modelId}`, {
    method: 'PUT',
    body: JSON.stringify(modelData)
  });
};

/**
 * Delete an LLM model
 * @param modelId - Model ID to delete
 * @returns Promise<any> Deletion result
 */
export const deleteLLMModel = async (modelId: string): Promise<any> => {
  return fetchApi(`/v2/llm/models/${modelId}`, {
    method: 'DELETE'
  });
};

// ============================================================================
// LLM CREDENTIALS MANAGEMENT
// ============================================================================

/**
 * Get all LLM credentials
 * @returns Promise<{ data?: any[]; items?: any[] } | any[]> List of LLM credentials
 */
export const getLLMCredentials = async (): Promise<{ data?: any[]; items?: any[] } | any[]> => {
  return fetchApi('/v2/llm/credentials');
};

/**
 * Create a new LLM credential
 * @param credentialData - Credential configuration data
 * @returns Promise<any> Created credential data
 */
export const createLLMCredential = async (credentialData: any): Promise<any> => {
  return fetchApi('/v2/llm/credentials', {
    method: 'POST',
    body: JSON.stringify(credentialData)
  });
};

/**
 * Update an existing LLM credential
 * @param credentialId - Credential ID to update
 * @param credentialData - Updated credential data
 * @returns Promise<any> Updated credential data
 */
export const updateLLMCredential = async (credentialId: string, credentialData: any): Promise<any> => {
  return fetchApi(`/v2/llm/credentials/${credentialId}`, {
    method: 'PUT',
    body: JSON.stringify(credentialData)
  });
};

/**
 * Delete an LLM credential
 * @param credentialId - Credential ID to delete
 * @returns Promise<any> Deletion result
 */
export const deleteLLMCredential = async (credentialId: string): Promise<any> => {
  return fetchApi(`/v2/llm/credentials/${credentialId}`, {
    method: 'DELETE'
  });
};

/**
 * Test an LLM credential to verify it works
 * @param credentialId - Credential ID to test
 * @returns Promise<any> Test result
 * @example
 * ```typescript
 * try {
 *   const result = await testLLMCredential('cred-123');
 *   if (result.success) {
 *     console.log('Credential test passed');
 *   }
 * } catch (error) {
 *   console.error('Credential test failed:', error);
 * }
 * ```
 */
export const testLLMCredential = async (credentialId: string): Promise<any> => {
  return fetchApi(`/v2/llm/credentials/${credentialId}/test`, {
    method: 'POST'
  });
};