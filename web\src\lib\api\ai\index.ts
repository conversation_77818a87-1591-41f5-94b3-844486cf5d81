/**
 * AI Module - Test Generation and Enhancement
 * 
 * This module provides AI-powered functionality for test automation including:
 * - User story enhancement and management
 * - Test script and code generation 
 * - Test generation from various sources
 * - AI prompt management and optimization
 * 
 * @module AI
 */

// Stories and user story management
export * from './stories';

// Test generation from multiple sources  
export * from './generation';

// Script and code generation for various frameworks
export * from './scripts';

// AI prompt management
export * from './prompts';

// User Story Enhancement
export {
  enhanceUserStory,
  generateManualTestCases,
  type EnhanceUserStoryInput,
  type EnhanceUserStoryOutput,
  type GenerateManualTestCasesInput,
  type GenerateManualTestCasesOutput
} from './stories';

// Test Generation & Code Generation
export {
  generateGherkin,
  generateCode,
  summarizeTestResults,
  type GenerateGherkinInput,
  type GenerateGherkinOutput,
  type GenerateCodeInput,
  type GenerateCodeOutput,
  type SummarizeTestResultsInput,
  type SummarizeTestResultsOutput
} from './generation';

// Prompt Management & Test History
export {
  saveTestHistory,
  fetchPrompts,
  fetchPromptDetail,
  updatePrompt,
  type SaveTestHistoryInput,
  type PromptUpdateData
} from './prompts';

// Legacy aliases for backward compatibility
export {
  enhanceUserStory as callEnhanceUserStory,
  generateManualTestCases as callGenerateManualTestCases
} from './stories';

export {
  generateGherkin as callGenerateGherkin,
  generateCode as callGenerateCode,
  summarizeTestResults as callSummarizeTestResults
} from './generation';