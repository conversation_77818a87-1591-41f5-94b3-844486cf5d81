"use client";

import { ExpandableText } from "@/components/ExpandableContent";
import { AnalysisProgressIndicator } from "@/components/ui/AnalysisProgressIndicator";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { API_BASE_URL } from "@/lib/config";
import { processTestResult } from "@/lib/resultProcessor";
import { StandardResult, StandardTestStep } from "@/lib/types";
import { formatSafeDate } from '@/lib/date-utils';
import { CheckCircle, CheckCircle2, Clock, ExternalLink, Image as ImageIcon, Info, Maximize2, Monitor, Mouse, Navigation, Target, Type, X, XCircle, Activity, Eye } from "lucide-react";
import React, { useState } from 'react';

// Utility function to safely render any value as string
function safeRender(value: any): string {
  if (value === null || value === undefined) return '';
  if (typeof value === 'string') return value;
  if (typeof value === 'number' || typeof value === 'boolean') return String(value);
  if (typeof value === 'object') {
    try {
      return JSON.stringify(value, null, 2); // Pretty print JSON
    } catch {
      return '[Object]';
    }
  }
  return String(value);
}

// Utility function to format AI analysis objects into readable text
function formatAIAnalysis(value: any): string {
  if (value === null || value === undefined) return '';
  if (typeof value === 'string') return value;

  if (typeof value === 'object') {
    try {
      // If it's an AI analysis object, format it nicely
      if (value.what_i_accomplished || value.test_flow_review || value.final_outcome) {
        let formatted = '';

        if (value.what_i_accomplished) {
          formatted += `**Lo que logré:**\n${value.what_i_accomplished}\n\n`;
        }

        if (value.test_flow_review) {
          formatted += `**Revisión del flujo de prueba:**\n${value.test_flow_review}\n\n`;
        }

        if (value.final_outcome) {
          formatted += `**Resultado final:**\n${value.final_outcome}\n\n`;
        }

        if (value.user_experience) {
          formatted += `**Experiencia de usuario:**\n${value.user_experience}\n\n`;
        }

        if (value.goal_achievement) {
          formatted += `**Logro del objetivo:**\n${value.goal_achievement}\n\n`;
        }

        if (value.visual_analysis) {
          formatted += `**Análisis visual:**\n${value.visual_analysis}`;
        }

        return formatted.trim();
      }

      // For other objects, just pretty print JSON
      return JSON.stringify(value, null, 2);
    } catch {
      return '[Object]';
    }
  }

  return String(value);
}

function getScreenshotSource(screenshotData: string | undefined | null): { src: string, caption: string, isBase64: boolean } | null {
  if (!screenshotData) {
    return null;
  }

  // The base URL for the C# backend API, e.g., "http://localhost:5001"
  const baseUrl = API_BASE_URL.replace('/api', '');

  // Case 1: The data is a full URL
  if (screenshotData.startsWith('http://') || screenshotData.startsWith('https://')) {
    return { src: screenshotData, caption: 'External screenshot URL', isBase64: false };
  }

  // Case 2: The data is a base64 string
  if (screenshotData.startsWith('data:image')) {
    return { src: screenshotData, caption: 'Data URL screenshot', isBase64: true };
  }

  // Case 3: For simulated screenshots (for demonstration purposes)
  if (screenshotData.includes('screenshot_step_') || screenshotData.includes('screenshot_final_')) {
    // Generate a placeholder screenshot for demonstration
    const width = 800;
    const height = 600;
    const stepNumber = screenshotData.match(/screenshot_step_(\d+)/)?.[1] || screenshotData.includes('final') ? 'Final' : '1';
    const placeholderUrl = `https://via.placeholder.com/${width}x${height}/007ACC/FFFFFF?text=Step+${stepNumber}+Screenshot`;
    return { src: placeholderUrl, caption: `Simulated screenshot: ${screenshotData}`, isBase64: false };
  }

  // Case 4: The data is a relative path to an artifact
  // But first check if it's base64 that happens to contain "/" characters
  if (screenshotData.includes('/')) {
    // Quick check: if it's very long and starts with typical base64 patterns, it's probably base64
    if (screenshotData.length > 1000 && (screenshotData.startsWith('iVBORw0KGgo') || screenshotData.match(/^[A-Za-z0-9+/]{100,}={0,2}$/))) {
      try {
        const decoded = atob(screenshotData);
        if (decoded.length > 100) {
          console.log('✅ Converting path-like base64 to data URL');
          return { src: `data:image/png;base64,${screenshotData}`, caption: 'Base64 screenshot (path-like)', isBase64: true };
        }
      } catch (e) {
        console.log('❌ Failed to decode path-like base64, treating as path');
      }
    }

    const path = screenshotData.startsWith('/') ? screenshotData : `/${screenshotData}`;
    const finalUrl = `${baseUrl}${path}`;
    return { src: finalUrl, caption: `Artifact: ${path}`, isBase64: false };
  }

  // Fallback for raw base64 data without the data:image prefix
  // This is less common but included for robustness.
  try {
    const decoded = atob(screenshotData); // Check if it's valid base64

    if (decoded.length > 100) {
      console.log('✅ getScreenshotSource converting to data URL');
      return { src: `data:image/png;base64,${screenshotData}`, caption: 'Base64 screenshot from state', isBase64: true };
    }
  } catch (e) {
    // If it's not valid base64 and not a path/URL, we can't process it.
    console.error(`Invalid screenshot data format: ${screenshotData.substring(0, 100)}...`);
    return null;
  }

  console.error(`No valid screenshot processing method found for: ${screenshotData.substring(0, 100)}...`);
  return null;
}

function findScreenshotInMetadata(step: StandardTestStep): string | null {
  // Look for screenshots in multiple locations within step metadata
  if (!step.metadata) {
    return null;
  }

  // Check common screenshot locations
  const locations = [
    step.screenshot_url,
    step.metadata.state?.screenshot,
    step.metadata.screenshot,
    step.metadata.result?.[0]?.screenshot,
    step.metadata.primary_action?.screenshot,
  ];

  // Check if there are screenshots in the actions array
  if (step.metadata.actions && Array.isArray(step.metadata.actions)) {
    for (const action of step.metadata.actions) {
      if (action.screenshot) {
        locations.push(action.screenshot);
      }
    }
  }

  // Check result array for screenshots
  if (step.metadata.result && Array.isArray(step.metadata.result)) {
    for (const resultItem of step.metadata.result) {
      if (resultItem && typeof resultItem === 'object' && resultItem.screenshot) {
        locations.push(resultItem.screenshot);
      }
    }
  }

  // Return the first valid screenshot found
  for (const location of locations) {
    if (location && typeof location === 'string' && location.length > 10) {
      console.log(`✅ Step ${step.step_number}: Found screenshot at ${location}`);
      return location;
    }
  }

  console.log(`❌ Step ${step.step_number}: No valid screenshot found`);
  return null;
}

export function getScreenshotUrlFromArtifactPath(artifactPath: string): string {
  // Convert artifact paths to accessible URLs for C# backend compatibility
  const baseUrl = API_BASE_URL.replace('/api', '');

  // Quick sanity-check
  if (!artifactPath) return artifactPath;

  // ----------------------------------------------------------------------------------
  // 1) BASE64 DETECTION (💡 must run BEFORE the "/" check)
  // ----------------------------------------------------------------------------------
  // a) Already formatted data-URL → return as-is
  if (artifactPath.startsWith('data:image')) {
    return artifactPath;
  }
  // b) Raw base64 string (no prefix). We do a cheap validation – try atob & length heuristics.
  //    Raw base64 often contains both "+" and "/" characters therefore it would wrongly
  //    match the artifact path branch below if we don't detect it first.
  try {
    // atob throws if not valid base64
    const decoded = atob(artifactPath);
    console.log('🔍 Base64 validation:', {
      pathLength: artifactPath.length,
      decodedLength: decoded.length,
      isValidBase64: true,
      pathStart: artifactPath.substring(0, 50)
    });
    // Require a minimum length to avoid treating short IDs as base64
    if (decoded.length > 100) {
      // Assume PNG by default – we can refine later if necessary via magic-number sniffing.
      console.log('✅ Converting to data URL:', artifactPath.substring(0, 50) + '...');
      return `data:image/png;base64,${artifactPath}`;
    }
  } catch (e) {
    console.log('❌ Base64 validation failed:', {
      pathLength: artifactPath.length,
      pathStart: artifactPath.substring(0, 50),
      error: e
    });
    // Not a raw base64 string – continue
  }

  // ----------------------------------------------------------------------------------
  // 2) SCREENSHOT FILE DETECTION (C# Backend Compatibility)
  // ----------------------------------------------------------------------------------
  // Check if this is a screenshot filename that should use the C# backend screenshot route
  if (artifactPath.includes('screenshot_') && artifactPath.endsWith('.png')) {
    // Extract just the filename if it's a full path
    const filename = artifactPath.split('/').pop() || artifactPath;
    
    // Check if it matches the expected formats:
    // Format 1: screenshot_{timestamp}_{number}.png
    // Format 2: screenshot_step_{number}_{uuid}.png
    // Format 3: screenshot_final_{uuid}.png
    if (filename.match(/^screenshot_\d{8}_\d{6}_\d{3}\.png$/) || 
        filename.match(/^screenshot_(step_\d+|final)_[a-f0-9-]+\.png$/)) {
      const screenshotUrl = `${baseUrl}/artifacts/${filename}`;
      console.log('✅ C# Backend Screenshot URL:', screenshotUrl, 'for filename:', filename);
      return screenshotUrl;
    }
  }

  // ----------------------------------------------------------------------------------
  // 3) R2-only VIRTUAL PATHS
  // ----------------------------------------------------------------------------------
  if (artifactPath.startsWith('r2-only://')) {
    try {
      const pathParts = artifactPath.replace('r2-only://', '').split('/');
      if (pathParts.length >= 2) {
        const filename = pathParts[pathParts.length - 1]; // Get the last part (filename)

        // Check if this is a screenshot file that should use the screenshot route
        if (filename.includes('screenshot_') && filename.endsWith('.png')) {
          // Extract the clean filename and use the screenshot route
          const cleanFilename = filename.replace(/^[a-f0-9-]+_/, ''); // Remove UUID prefix if present
          if (cleanFilename.match(/^screenshot_\d{8}_\d{6}_\d{3}\.png$/)) {
            const screenshotUrl = `${baseUrl}/artifacts/${cleanFilename}`;
            console.log('✅ R2-only Screenshot URL:', screenshotUrl);
            return screenshotUrl;
          }
        }

        // Extract artifact ID from filename (UUID format or 32 hex characters before underscore)
        // Pattern: 4c90e1afa8a5a5ef31d799e747e543a2_screenshot_20250703_120949_001.png
        // Or UUID: 170eb46b-2348-4450-91f0-a3e6665e79c4_screenshot_20250703_120949_001.png
        const artifactIdMatch = filename.match(/^([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}|[a-f0-9]{32})/);
        if (artifactIdMatch) {
          const artifactId = artifactIdMatch[1];
          const convertedUrl = `${baseUrl}/artifacts/${artifactId}`;
          console.log('✅ Converted R2-only path to:', convertedUrl);
          return convertedUrl;
        } else {
          console.warn('❌ Could not extract artifact ID from filename:', filename);
          console.warn('❌ Filename format:', filename);
          // Fallback: try using the execution_id/filename as the artifact path
          const fallbackUrl = `${baseUrl}/artifacts/${pathParts.join('/')}`;
          console.log('⚠️ Using fallback path for R2-only artifact:', fallbackUrl);
          return fallbackUrl;
        }
      } else {
        console.warn('❌ Invalid R2-only path format:', artifactPath);
      }
    } catch (e) {
      console.error(`Failed to parse R2-only path: ${artifactPath}`, e);
    }
  }

  // ----------------------------------------------------------------------------------
  // 4) REGULAR ARTIFACT PATHS
  // ----------------------------------------------------------------------------------
  // Handle regular artifact paths
  if (artifactPath.includes('/')) {
    const path = artifactPath.startsWith('/') ? artifactPath : `/${artifactPath}`;
    const convertedUrl = `${baseUrl}${path}`;
    console.log('✅ Converted regular path to:', convertedUrl);
    return convertedUrl;
  }

  // ----------------------------------------------------------------------------------
  // 5) DIRECT FILENAME FALLBACK
  // ----------------------------------------------------------------------------------
  // If it's just a filename, try the generic artifacts route
  const fallbackUrl = `${baseUrl}/artifacts/${artifactPath}`;
  console.log('⚠️ Using direct filename fallback:', fallbackUrl);
  return fallbackUrl;
}

interface RichResultsViewerProps {
  result: StandardResult;
  onAnalysisComplete?: () => void;
}

interface ScreenshotWithStep {
  stepNumber: number;
  stepDescription: string;
  screenshotInfo: { src: string; caption: string; isBase64: boolean };
  originalSource: string;
  actionType?: string;
}

interface EnhancedScreenshotsViewerProps {
  result: StandardResult;
}



function EnhancedScreenshotsViewer({ result }: EnhancedScreenshotsViewerProps) {
  const [selectedScreenshot, setSelectedScreenshot] = useState<ScreenshotWithStep | null>(null);

  // Check if this is a simulated execution
  const isSimulatedExecution = React.useMemo(() => {
    // Check for simulation indicators in metadata or raw_data
    if (result.metadata?.simulation === true) return true;
    if (result.raw_data?.execution_type === 'simulated') return true;
    if (result.metadata?.source?.includes('simulation')) return true;
    
    // Check if screenshots have UUID patterns but are likely mock
    if (result.artifacts?.screenshots?.length > 0) {
      const hasUuidScreenshots = result.artifacts.screenshots.some(screenshot => 
        screenshot.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/)
      );
      const hasSimulationNote = result.metadata?.note?.includes('simulation');
      return hasUuidScreenshots && hasSimulationNote;
    }
    
    return false;
  }, [result]);

  // Collect and order screenshots by step number
  const orderedScreenshots: ScreenshotWithStep[] = React.useMemo(() => {
    const screenshots: ScreenshotWithStep[] = [];

    console.log('🔍 EnhancedScreenshotsViewer processing result:', {
      steps_count: result.steps?.length || 0,
      artifacts_screenshots_count: result.artifacts?.screenshots?.length || 0,
      artifacts_screenshots: result.artifacts?.screenshots || []
    });

    // First, get screenshots from steps (most reliable ordering)
    if (result.steps) {
      result.steps.forEach((step: StandardTestStep) => {
        const screenshotData = findScreenshotInMetadata(step);
        if (screenshotData) {
          const screenshotInfo = getScreenshotSource(screenshotData);
          if (screenshotInfo) {
            screenshots.push({
              stepNumber: step.step_number,
              stepDescription: step.description,
              screenshotInfo,
              originalSource: screenshotData,
              actionType: getActionType(step)
            });
          }
        }
      });
    }

    // If we have artifacts screenshots but no step screenshots, use the artifacts directly
    if (result.artifacts?.screenshots?.length > 0 && screenshots.length === 0) {
      result.artifacts.screenshots.forEach((artifactPath: string, index: number) => {
        const convertedUrl = getScreenshotUrlFromArtifactPath(artifactPath);
        const screenshotInfo = getScreenshotSource(convertedUrl);
        if (screenshotInfo) {
          screenshots.push({
            stepNumber: index + 1, // Estimated step number
            stepDescription: `Step ${index + 1} screenshot`,
            screenshotInfo,
            originalSource: artifactPath,
            actionType: 'screenshot'
          });
        }
      });
    }

    // If we still don't have screenshots from steps but have artifacts, try to match them by index
    if (result.artifacts?.screenshots?.length > 0 && screenshots.length < result.artifacts.screenshots.length) {

      // Get the artifact indices that are already used
      const usedIndices = new Set<number>();
      screenshots.forEach(screenshot => {
        if (screenshot.originalSource && result.artifacts?.screenshots) {
          const index = result.artifacts.screenshots.indexOf(screenshot.originalSource);
          if (index >= 0) {
            usedIndices.add(index);
          }
        }
      });

      // Add any unused artifacts
      result.artifacts.screenshots.forEach((artifactPath: string, index: number) => {
        if (!usedIndices.has(index)) {
          const convertedUrl = getScreenshotUrlFromArtifactPath(artifactPath);
          const screenshotInfo = getScreenshotSource(convertedUrl);
          if (screenshotInfo) {
            screenshots.push({
              stepNumber: index + 1,
              stepDescription: `Additional screenshot ${index + 1}`,
              screenshotInfo,
              originalSource: artifactPath,
              actionType: 'screenshot'
            });
          }
        }
      });
    }

    console.log('✅ Final screenshots processed:', {
      total_screenshots: screenshots.length,
      screenshots: screenshots.map(s => ({ stepNumber: s.stepNumber, description: s.stepDescription, originalSource: s.originalSource }))
    });

    // Sort by step number
    return screenshots.sort((a, b) => a.stepNumber - b.stepNumber);
  }, [result.steps, result.artifacts?.screenshots]);

  if (orderedScreenshots.length === 0) {
    return (
      <div className="text-center py-8">
        <ImageIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        <p className="text-muted-foreground dark:text-gray-400">No screenshots available for this execution.</p>
        <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
          Checked both step metadata and artifacts ({result.artifacts?.screenshots?.length || 0} artifacts).
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Simulation Warning Banner */}
      {isSimulatedExecution && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Simulated Execution:</strong> This execution was run in simulation mode. Screenshots are placeholder references and may not be available.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header with count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-blue-600 dark:text-blue-400">
          Found {orderedScreenshots.length} screenshots ordered by execution steps
        </p>
        <Badge variant="outline" className="text-xs">
          Click to expand
        </Badge>
      </div>

      {/* Screenshots grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {orderedScreenshots.map((screenshot, index) => {
          const { src, caption, isBase64 } = screenshot.screenshotInfo;

          return (
            <div
              key={`${screenshot.stepNumber}-${index}`}
              className="border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer group"
              onClick={() => setSelectedScreenshot(screenshot)}
            >
              {/* Image container */}
              <div className="relative bg-gray-100 dark:bg-gray-700 rounded-t-lg p-2 flex items-center justify-center">
                <img
                  src={src}
                  alt={`Step ${screenshot.stepNumber} screenshot`}
                  className="w-full h-auto object-contain max-h-[800px] rounded shadow-sm group-hover:scale-[1.02] transition-transform duration-200"
                  style={{
                    maxHeight: '800px',
                    width: '100%',
                    height: 'auto',
                    objectFit: 'contain',
                    aspectRatio: 'unset'
                  }}
                  onLoad={(e) => {
                    const img = e.target as HTMLImageElement;
                    console.log(`📸 Image loaded - Step ${screenshot.stepNumber}:`, {
                      naturalWidth: img.naturalWidth,
                      naturalHeight: img.naturalHeight,
                      displayWidth: img.width,
                      displayHeight: img.height,
                      src: src.substring(0, 100) + '...'
                    });
                  }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    console.error(`Failed to load screenshot: ${screenshot.originalSource}`);

                    // Check if this is a simulated execution (common pattern: UUID in screenshot name but no actual file)
                    const isSimulated = screenshot.originalSource.includes('-') && 
                                      screenshot.originalSource.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/);
                    
                    if (isSimulated) {
                      console.log(`🔧 Detected simulated execution screenshot: ${screenshot.originalSource}`);
                    }

                    // Try fallback URL if original fails and not already using artifacts route
                    if (!target.src.includes('/artifacts/screenshot_')) {
                      const parts = screenshot.originalSource.split('/');
                      const filename = parts[parts.length - 1];
                      if (filename && filename.includes('screenshot_')) {
                        const fallbackUrl = `${API_BASE_URL.replace('/api', '')}/artifacts/${filename}`;
                        console.log(`Trying fallback: ${fallbackUrl}`);
                        target.src = fallbackUrl;
                        return;
                      }
                    }

                    // Hide image and show appropriate placeholder
                    target.style.display = 'none';
                    const placeholder = target.nextElementSibling as HTMLElement;
                    if (placeholder) {
                      // Update placeholder text based on whether this is simulated
                      const placeholderText = placeholder.querySelector('p');
                      if (placeholderText && isSimulated) {
                        placeholderText.textContent = 'Simulated Screenshot';
                        placeholderText.className = 'text-xs text-yellow-600';
                      }
                      placeholder.style.display = 'flex';
                    }
                  }}
                />
                <div style={{ display: 'none' }} className="absolute inset-0 flex flex-col items-center justify-center">
                  <XCircle className="h-6 w-6 mb-1 text-red-500" />
                  <p className="text-xs text-red-500">Load Failed</p>
                  <p className="text-xs text-gray-500 mt-1 text-center px-2">
                    {screenshot.originalSource.includes('screenshot_') ? 'Screenshot not available' : 'Image not found'}
                  </p>
                </div>

                {/* Overlay with expand icon */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 flex items-center justify-center transition-all duration-200">
                  <Maximize2 className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                </div>

                {/* Step number badge */}
                <div className="absolute top-2 left-2">
                  <Badge variant="secondary" className="text-xs font-mono bg-black bg-opacity-70 text-white border-none">
                    Step {screenshot.stepNumber}
                  </Badge>
                </div>

                {/* Action type badge */}
                {screenshot.actionType && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="outline" className="text-xs bg-white bg-opacity-90 text-gray-700 border-gray-300">
                      {screenshot.actionType}
                    </Badge>
                  </div>
                )}
              </div>

              {/* Description */}
              <div className="p-2 bg-gray-50 dark:bg-gray-700">
                <p className="text-xs text-gray-600 dark:text-gray-300 truncate" title={screenshot.stepDescription}>
                  {screenshot.stepDescription.slice(0, 60)}
                  {screenshot.stepDescription.length > 60 ? '...' : ''}
                </p>
              </div>
            </div>
          );
        })}
      </div>

      {/* Expandable Modal */}
      <Dialog open={!!selectedScreenshot} onOpenChange={() => setSelectedScreenshot(null)}>
        <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden">
          <DialogHeader>
            <div className="flex items-center justify-between">
              <DialogTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                Step {selectedScreenshot?.stepNumber} Screenshot
                {selectedScreenshot?.actionType && (
                  <Badge variant="outline" className="text-sm">
                    {selectedScreenshot.actionType}
                  </Badge>
                )}
              </DialogTitle>
              <DialogClose asChild>
                <Button variant="ghost" size="sm">
                  <X className="h-4 w-4" />
                </Button>
              </DialogClose>
            </div>
          </DialogHeader>

          {selectedScreenshot && (
            <div className="space-y-4">
              {/* Step description */}
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  {selectedScreenshot.stepDescription}
                </p>
              </div>

              {/* Large screenshot */}
              <div className="flex justify-center bg-gray-100 dark:bg-gray-700 rounded-lg p-4 max-h-[80vh] overflow-auto">
                <img
                  src={selectedScreenshot.screenshotInfo.src}
                  alt={`Step ${selectedScreenshot.stepNumber} screenshot`}
                  className="max-w-full max-h-full object-contain rounded shadow-lg"
                  onError={(e) => {
                    console.error(`Failed to load expanded screenshot: ${selectedScreenshot.originalSource}`);
                  }}
                />
              </div>

              {/* Metadata */}
              <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                <p><strong>Source:</strong> {selectedScreenshot.screenshotInfo.caption}</p>
                <p><strong>Type:</strong> {selectedScreenshot.screenshotInfo.isBase64 ? 'Base64 Data' : 'File URL'}</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Helper function to safely get action type from step (handles both 'action' and 'action_type' fields)
function getActionType(step: any): string | undefined {
  return step.action_type || step.action || undefined;
}

// Helper function to determine if a step was successful based on status
function getStepSuccess(step: any): boolean {
  // If 'success' field exists, use it
  if (typeof step.success === 'boolean') {
    return step.success;
  }
  
  // Otherwise, determine from status field
  if (typeof step.status === 'string') {
    const status = step.status.toLowerCase();
    return status === 'completed' || status === 'success' || status === 'passed';
  }
  
  // Default to false if we can't determine
  return false;
}

// Helper function to process StandardResult and ensure compatibility
function processStandardResult(result: any): any {
  if (!result) return result;
  
  // Process steps to add success field if missing
  if (result.steps && Array.isArray(result.steps)) {
    result.steps = result.steps.map((step: any) => ({
      ...step,
      success: getStepSuccess(step)
    }));
  }
  
  return result;
}

function getActionIcon(actionType: string | undefined) {
  if (!actionType || typeof actionType !== 'string') {
    return <Activity className="h-4 w-4" />;
  }
  
  switch (actionType.toLowerCase()) {
    case 'go_to_url':
    case 'navigate':
    case 'navigate_to_url':
      return <Navigation className="h-4 w-4" />;
    case 'click':
      return <Mouse className="h-4 w-4" />;
    case 'type':
      return <Type className="h-4 w-4" />;
    case 'done':
    case 'complete_test':
      return <CheckCircle2 className="h-4 w-4" />;
    case 'scroll':
      return <Monitor className="h-4 w-4" />;
    case 'verify_elements':
    case 'verify':
      return <Eye className="h-4 w-4" />;
    default:
      return <Target className="h-4 w-4" />;
  }
}

function StepCard({ step, index, result }: { step: StandardTestStep; index: number; result: StandardResult }) {
  const statusColor = step.success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
  const borderColor = step.success ? 'border-green-200 dark:border-green-800' : 'border-red-200 dark:border-red-800';
  const bgColor = step.success ? 'bg-green-50 dark:bg-green-900/10' : 'bg-red-50 dark:bg-red-900/10';

  return (
    <Card className={`mb-3 sm:mb-4 ${borderColor} ${bgColor} dark:bg-gray-800 overflow-hidden`}>
      <CardHeader className="p-4 sm:p-6 pb-2 sm:pb-3">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0">
          <div className="flex items-center space-x-2 min-w-0 flex-wrap gap-2">
            <div className="flex items-center space-x-2">
              {getActionIcon(getActionType(step))}
              <CardTitle className="text-base sm:text-lg dark:text-white">Step {step.step_number}</CardTitle>
            </div>
            <div className="flex items-center space-x-2 flex-wrap gap-1">
              <Badge
                variant={step.success ? "default" : "destructive"}
                className={`text-xs sm:text-sm ${step.success ? "bg-green-600 hover:bg-green-700 text-white border-green-600" : "bg-red-600 hover:bg-red-700 text-white border-red-600"}`}
              >
                {step.success ? "✅ Success" : "❌ Failed"}
              </Badge>
              <Badge variant="outline" className="dark:border-gray-600 dark:text-gray-300 text-xs sm:text-sm">{getActionType(step) || 'Unknown'}</Badge>
            </div>
          </div>
          {step.duration_ms && (
            <div className="flex items-center text-xs sm:text-sm text-muted-foreground dark:text-gray-400 flex-shrink-0">
              <Clock className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              {step.duration_ms}ms
            </div>
          )}
        </div>
        <CardDescription className="mt-2">
          <ExpandableText
            text={safeRender(step.description)}
            maxLength={150}
            className="text-sm sm:text-base font-medium dark:text-gray-300"
          />
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3 sm:space-y-4 overflow-hidden p-4 sm:p-6 pt-0">
        {/* URL Information */}
        {step.url && (
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md overflow-hidden">
            <div className="flex items-center space-x-2 flex-shrink-0">
              <ExternalLink className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span className="text-sm font-medium text-blue-800 dark:text-blue-200">URL:</span>
            </div>
            <a
              href={step.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline text-xs sm:text-sm font-mono break-all block min-w-0"
              title={step.url}
            >
              {step.url}
            </a>
          </div>
        )}

        {/* Element Information */}
        {step.element_info && (
          <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-md">
            <div className="flex items-center space-x-2 mb-3">
              <Target className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              <span className="text-sm font-medium text-purple-800 dark:text-purple-200">Element Interaction:</span>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs sm:text-sm text-gray-700 dark:text-gray-300">
              {step.element_info.tag && (
                <div className="break-words"><span className="font-medium">Tag:</span> {step.element_info.tag}</div>
              )}
              {step.element_info.selector && (
                <div className="col-span-1 sm:col-span-2 overflow-hidden"><span className="font-medium">Selector:</span> <code className="text-xs bg-gray-100 dark:bg-gray-700 px-1 rounded break-all max-w-full inline-block">{step.element_info.selector}</code></div>
              )}
              {step.element_info.text && (
                <div className="break-words"><span className="font-medium">Text:</span> "{step.element_info.text}"</div>
              )}
              {step.element_info.coordinate && (
                <div className="break-words"><span className="font-medium">Position:</span> ({step.element_info.coordinate[0]}, {step.element_info.coordinate[1]})</div>
              )}
              {step.element_info.id && (
                <div className="break-words"><span className="font-medium">ID:</span> {step.element_info.id}</div>
              )}
              {step.element_info.class && (
                <div className="break-words"><span className="font-medium">Class:</span> {step.element_info.class}</div>
              )}
              {/* Show ALL additional element properties */}
              {Object.entries(step.element_info)
                .filter(([key]) => !['tag', 'selector', 'text', 'coordinate', 'id', 'class'].includes(key))
                .map(([key, value]) => (
                  <div key={key} className="col-span-1 sm:col-span-2 text-xs overflow-hidden">
                    <span className="font-medium capitalize">{key.replace(/_/g, ' ')}:</span>
                    <span className="ml-2 break-all max-w-full inline-block">
                      {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                    </span>
                  </div>
                ))}
            </div>
          </div>
        )}

        {/* Screenshot - Enhanced detection with fallback for incorrect URLs */}
        {(() => {
          const screenshotData = findScreenshotInMetadata(step);
          console.log(`🖼️ Step ${step.step_number}: Screenshot data found:`, screenshotData);

          const screenshotInfo = getScreenshotSource(screenshotData);
          console.log(`🖼️ Step ${step.step_number}: Screenshot info processed:`, screenshotInfo);

          if (!screenshotInfo) {
            console.log(`🖼️ Step ${step.step_number}: No screenshot info available, not rendering screenshot section`);
            return null;
          }

          const { src, caption, isBase64 } = screenshotInfo;
          console.log(`🖼️ Step ${step.step_number}: About to render screenshot with src:`, src);

          const ScreenshotImage = () => {
            const [currentSrc, setCurrentSrc] = useState(src);
            const [hasErrored, setHasErrored] = useState(false);

            const handleImageError = async () => {
              console.error(`❌ Step ${step.step_number}: Failed to load screenshot from ${currentSrc}`);
              console.log(`❌ Step ${step.step_number}: Original source was: ${screenshotData || 'null'}`);

              // Try alternative URL patterns for R2/artifacts
              if (!hasErrored && !isBase64 && screenshotData) {
                // Try extracting screenshot name from path for direct artifact access
                const parts = screenshotData.split('/');
                const filename = parts[parts.length - 1];
                if (filename && filename.includes('screenshot_')) {
                  const fallbackUrl = `${API_BASE_URL.replace('/api', '')}/artifacts/${filename}`;
                  setCurrentSrc(fallbackUrl);
                  return;
                }
              }

              setHasErrored(true);
            };

            const handleImageLoad = () => {
              console.log(`✅ Step ${step.step_number}: Screenshot loaded successfully from ${currentSrc}`);
              setHasErrored(false); // Reset error state if image loads successfully
            };

            if (hasErrored) {
              return (
                <div className="flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-700 rounded border border-dashed border-red-400 dark:border-red-600 p-8">
                  <XCircle className="h-10 w-10 text-red-500 dark:text-red-400 mb-2" />
                  <p className="font-semibold text-red-600 dark:text-red-500">Image Failed to Load</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{isBase64 ? 'Corrupt base64 data' : 'Image not found'}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 break-all">Tried: {src}</p>
                </div>
              );
            }

            return (
              <img
                src={currentSrc}
                alt={`Screenshot for step ${step.step_number}`}
                className="max-w-full h-auto rounded border shadow-sm dark:border-gray-600 bg-gray-200 dark:bg-gray-800"
                style={{ maxHeight: '600px' }}
                onLoad={handleImageLoad}
                onError={handleImageError}
              />
            );
          };

          return (
            <div className="p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-md">
              <div className="flex items-center space-x-2 mb-2">
                <ImageIcon className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                <span className="text-sm font-medium text-indigo-800 dark:text-indigo-200">Step Screenshot:</span>
              </div>
              <div className="mt-2 group relative">
                <ScreenshotImage />
                <p className="text-xs text-gray-500 dark:text-gray-400 pt-2">{caption}</p>
              </div>
            </div>
          );
        })()}

        {/* AI Reasoning & Metadata */}
        {step.metadata && Object.keys(step.metadata).length > 0 && (
          <div className="space-y-3">
            {/* Thinking Process */}
            {step.metadata.thinking && (
              <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <Info className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                  <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">AI Reasoning:</span>
                </div>
                <ExpandableText
                  text={safeRender(step.metadata.thinking)}
                  maxLength={300}
                  className="text-sm"
                  contentClassName="text-gray-700 dark:text-gray-300 italic"
                />
              </div>
            )}

            {/* AI Quality Analysis */}
            {renderAIStepAnalysis(step, result)}

            {/* Actions Metadata */}
            {step.metadata.actions && Array.isArray(step.metadata.actions) && step.metadata.actions.length > 0 && (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">Actions Performed:</span>
                </div>
                <div className="space-y-2">
                  {step.metadata.actions.map((action, idx) => (
                    <div key={idx} className="text-xs bg-white dark:bg-gray-700 p-2 rounded border border-gray-200 dark:border-gray-600 overflow-hidden">
                      <div className="font-medium text-gray-900 dark:text-gray-100">Type: {action.type}</div>
                      {action.url && <div className="text-gray-700 dark:text-gray-300 break-all">URL: {action.url}</div>}
                      {action.text && <div className="text-gray-700 dark:text-gray-300 break-words">Text: "{action.text}"</div>}
                      {action.coordinate && <div className="text-gray-700 dark:text-gray-300">Position: ({action.coordinate[0]}, {action.coordinate[1]})</div>}
                      {action.selector && <div className="text-gray-700 dark:text-gray-300 overflow-hidden">Selector: <code className="bg-gray-100 dark:bg-gray-600 px-1 break-all max-w-full inline-block">{action.selector}</code></div>}
                      {/* Show ALL other action properties */}
                      {Object.entries(action)
                        .filter(([key]) => !['type', 'url', 'text', 'coordinate', 'selector'].includes(key))
                        .map(([key, value]) => (
                          <div key={key} className="text-gray-600 dark:text-gray-400 overflow-hidden">
                            <span className="font-medium capitalize">{key}:</span> <span className="break-all">{JSON.stringify(value)}</span>
                          </div>
                        ))}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Primary Action */}
            {step.metadata.primary_action && (
              <div className="p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                  <span className="text-sm font-medium text-indigo-800 dark:text-indigo-200">Primary Action:</span>
                </div>
                <pre className="text-xs bg-white dark:bg-gray-700 p-2 rounded border dark:border-gray-600 overflow-x-auto whitespace-pre-wrap break-all max-w-full text-gray-900 dark:text-gray-100">
                  {JSON.stringify(step.metadata.primary_action, null, 2)}
                </pre>
              </div>
            )}

            {/* State Information */}
            {step.metadata.state && (
              <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <Monitor className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">Browser State:</span>
                </div>
                <div className="grid grid-cols-1 gap-2 text-xs text-gray-700 dark:text-gray-300">
                  {step.metadata.state.url && <div className="overflow-hidden"><span className="font-medium">URL:</span> <span className="break-all">{step.metadata.state.url}</span></div>}
                  {step.metadata.state.title && <div className="overflow-hidden"><span className="font-medium">Title:</span> <span className="break-words">{step.metadata.state.title}</span></div>}
                  {step.metadata.state.screenshot && <div><span className="font-medium">Screenshot:</span> Available</div>}
                  {/* Show ALL other state properties */}
                  {Object.entries(step.metadata.state)
                    .filter(([key]) => !['url', 'title', 'screenshot'].includes(key))
                    .map(([key, value]) => (
                      <div key={key} className="text-gray-600 dark:text-gray-400 overflow-hidden">
                        <span className="font-medium capitalize">{key}:</span> <span className="break-all">{JSON.stringify(value)}</span>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* Result Data */}
            {step.metadata.result && Array.isArray(step.metadata.result) && step.metadata.result.length > 0 && (
              <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md">
                <div className="flex items-center space-x-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-gray-800 dark:text-gray-200">Step Results:</span>
                </div>
                <StepResultsDisplay results={step.metadata.result} />
              </div>
            )}

            {/* ALL OTHER METADATA FIELDS - Show everything we haven't already displayed */}
            {Object.entries(step.metadata)
              .filter(([key]) => !['thinking', 'actions', 'primary_action', 'state', 'result'].includes(key))
              .length > 0 && (
                <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md">
                  <div className="flex items-center space-x-2 mb-2">
                    <Info className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-sm font-medium text-gray-800 dark:text-gray-200">Additional Metadata:</span>
                  </div>
                  <div className="space-y-2">
                    {Object.entries(step.metadata)
                      .filter(([key]) => !['thinking', 'actions', 'primary_action', 'state', 'result'].includes(key))
                      .map(([key, value]) => (
                        <div key={key} className="text-xs bg-white dark:bg-gray-700 p-2 rounded border dark:border-gray-600 overflow-hidden">
                          <div className="font-medium capitalize text-gray-700 dark:text-gray-300">{key.replace(/_/g, ' ')}:</div>
                          <div className="mt-1">
                            <ExpandableText
                              text={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
                              maxLength={150}
                              className="text-xs"
                              contentClassName="text-gray-600 dark:text-gray-400 whitespace-pre-wrap break-all max-w-full font-mono"
                            />
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
          </div>
        )}

        {/* Error Message */}
        {step.error_message && (
          <div className="p-3 bg-red-50 rounded-md border border-red-200 overflow-hidden">
            <div className="flex items-center space-x-2 mb-2">
              <XCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">Error:</span>
            </div>
            <ExpandableText
              text={step.error_message}
              maxLength={200}
              className="text-sm"
              contentClassName="text-red-700 text-sm break-words whitespace-pre-wrap"
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ExecutionSummary({ result }: { result: StandardResult }) {
  const duration = result.duration_ms ? `${result.duration_ms}ms` : 'Unknown';
  const startTime = formatSafeDate(result.started_at, 'PPpp', 'Unknown');
  const endTime = formatSafeDate(result.completed_at, 'PPpp', 'In progress');

  // Use actual data instead of potentially incorrect summary
  const actualTotalSteps = result.steps?.length || 0;
  const actualSuccessfulSteps = result.steps?.filter(step => step.success).length || 0;
  const actualFailedSteps = actualTotalSteps - actualSuccessfulSteps;
  const actualSuccessRate = actualTotalSteps > 0 ? Math.round((actualSuccessfulSteps / actualTotalSteps) * 100) : 0;

  // Log the comparison for debugging
  console.log("📊 Summary Comparison:");
  console.log(`   Reported: ${result.summary?.total_steps} total, ${result.summary?.successful_steps} successful`);
  console.log(`   Actual: ${actualTotalSteps} total, ${actualSuccessfulSteps} successful`);

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card className="dark:bg-gray-800">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-center dark:text-white">{actualTotalSteps}</div>
          <div className="text-sm text-muted-foreground dark:text-gray-400 text-center">Total Steps</div>
          {result.summary?.total_steps !== actualTotalSteps && (
            <div className="text-xs text-orange-600 dark:text-orange-400 text-center mt-1">
              (Summary reports: {result.summary?.total_steps || 0})
            </div>
          )}
        </CardContent>
      </Card>
      <Card className="dark:bg-gray-800">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400 text-center">{actualSuccessfulSteps}</div>
          <div className="text-sm text-muted-foreground dark:text-gray-400 text-center">Successful</div>
          {result.summary?.successful_steps !== actualSuccessfulSteps && (
            <div className="text-xs text-orange-600 dark:text-orange-400 text-center mt-1">
              (Summary reports: {result.summary?.successful_steps || 0})
            </div>
          )}
        </CardContent>
      </Card>
      <Card className="dark:bg-gray-800">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400 text-center">{actualFailedSteps}</div>
          <div className="text-sm text-muted-foreground dark:text-gray-400 text-center">Failed</div>
        </CardContent>
      </Card>
      <Card className="dark:bg-gray-800">
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-center dark:text-white">{actualSuccessRate}%</div>
          <div className="text-sm text-muted-foreground dark:text-gray-400 text-center">Success Rate</div>
        </CardContent>
      </Card>
      <Card className="col-span-2 md:col-span-4 dark:bg-gray-800">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm dark:text-gray-300">
            <div>
              <span className="font-medium">Started:</span> {startTime}
            </div>
            <div>
              <span className="font-medium">Completed:</span> {endTime}
            </div>
            <div>
              <span className="font-medium">Duration:</span> {duration}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function MetadataTab({ result }: { result: StandardResult }) {
  const hasVisitedUrls = result.metadata?.visited_urls && Array.isArray(result.metadata.visited_urls) && result.metadata.visited_urls.length > 0;
  const hasInteractedElements = result.metadata?.interacted_elements && Array.isArray(result.metadata.interacted_elements) && result.metadata.interacted_elements.length > 0;
  const hasAdditionalMetadata = result.metadata && typeof result.metadata === 'object' && Object.keys(result.metadata).some(key => !['visited_urls', 'interacted_elements'].includes(key));

  return (
    <div className="space-y-6 min-h-[300px]">
      {/* Visited URLs */}
      {hasVisitedUrls && (
        <Card className="dark:bg-gray-800 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-200 dark:border-blue-800">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <ExternalLink className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <CardTitle className="text-lg dark:text-white text-blue-900 dark:text-blue-100">Visited URLs</CardTitle>
            </div>
            <CardDescription className="dark:text-blue-200 text-blue-700">Pages navigated during test execution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {result.metadata?.visited_urls?.map((url, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-white/60 dark:bg-blue-900/30 rounded-lg border border-blue-200/50 dark:border-blue-700/50 overflow-hidden min-w-0 hover:shadow-md transition-all">
                  <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center flex-shrink-0">
                    <ExternalLink className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:underline font-mono text-sm truncate block min-w-0 font-medium"
                    title={url}
                  >
                    {url}
                  </a>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Interacted Elements */}
      {result.metadata?.interacted_elements && Array.isArray(result.metadata.interacted_elements) && result.metadata.interacted_elements.length > 0 && (
        <Card className="dark:bg-gray-800 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Mouse className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              <CardTitle className="text-lg dark:text-white text-purple-900 dark:text-purple-100">Interacted Elements</CardTitle>
            </div>
            <CardDescription className="dark:text-purple-200 text-purple-700">DOM elements that were clicked, typed into, or otherwise interacted with</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {result.metadata.interacted_elements.map((element, index) => (
                <div key={index} className="p-4 bg-white/60 dark:bg-purple-900/30 rounded-lg border border-purple-200/50 dark:border-purple-700/50 hover:shadow-md transition-all">
                  <div className="grid grid-cols-2 gap-3 text-sm dark:text-gray-300">
                    {element.tag && (
                      <div className="flex items-center space-x-2">
                        <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                        <span className="font-medium text-purple-700 dark:text-purple-300">Tag:</span>
                        <code className="bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded text-xs">{safeRender(element.tag)}</code>
                      </div>
                    )}
                    {element.id && (
                      <div className="flex items-center space-x-2">
                        <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span className="font-medium text-purple-700 dark:text-purple-300">ID:</span>
                        <code className="bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded text-xs">{safeRender(element.id)}</code>
                      </div>
                    )}
                    {element.class && (
                      <div className="col-span-2 flex items-start space-x-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full mt-1"></span>
                        <span className="font-medium text-purple-700 dark:text-purple-300">Class:</span>
                        <code className="bg-green-100 dark:bg-green-800 px-2 py-1 rounded text-xs break-all flex-1">{safeRender(element.class)}</code>
                      </div>
                    )}
                    {element.text && (
                      <div className="col-span-2 flex items-start space-x-2">
                        <span className="w-2 h-2 bg-yellow-500 rounded-full mt-1"></span>
                        <span className="font-medium text-purple-700 dark:text-purple-300">Text:</span>
                        <span className="bg-yellow-100 dark:bg-yellow-800 px-2 py-1 rounded text-xs break-words flex-1">"{safeRender(element.text)}"</span>
                      </div>
                    )}
                    {/* Show ALL additional properties */}
                    {Object.entries(element).filter(([key]) => !['tag', 'id', 'class', 'text'].includes(key)).map(([key, value]) => (
                      <div key={key} className="col-span-2 text-xs overflow-hidden flex items-start space-x-2">
                        <span className="w-2 h-2 bg-gray-400 rounded-full mt-1"></span>
                        <span className="font-medium capitalize text-purple-700 dark:text-purple-300">{key.replace(/_/g, ' ')}:</span>
                        <span className="ml-2 break-all flex-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Additional Metadata */}
      {result.metadata && typeof result.metadata === 'object' && Object.keys(result.metadata).some(key => !['visited_urls', 'interacted_elements'].includes(key)) && (
        <Card className="dark:bg-gray-800 bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border-orange-200 dark:border-orange-800">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Info className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              <CardTitle className="text-lg dark:text-white text-orange-900 dark:text-orange-100">Additional Metadata</CardTitle>
            </div>
            <CardDescription className="dark:text-orange-200 text-orange-700">Other metadata collected during execution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(result.metadata)
                .filter(([key]) => !['visited_urls', 'interacted_elements'].includes(key))
                .map(([key, value]) => (
                  <div key={key} className="p-4 bg-white/60 dark:bg-orange-900/30 rounded-lg border border-orange-200/50 dark:border-orange-700/50 hover:shadow-md transition-all">
                    <div className="flex items-center space-x-2 mb-3">
                      <span className="w-3 h-3 bg-orange-500 rounded-full mr-2"></span>
                      <div className="font-medium text-sm capitalize text-orange-800 dark:text-orange-200">{key.replace(/_/g, ' ')}</div>
                    </div>
                    <div className="max-h-40 overflow-y-auto">
                      <ExpandableText
                        text={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
                        maxLength={200}
                        className="text-xs"
                        contentClassName="bg-white dark:bg-gray-700 p-3 rounded border dark:border-gray-600 whitespace-pre-wrap break-all max-w-full text-gray-900 dark:text-gray-100 shadow-inner font-mono block"
                      />
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Configuration */}
      {result.configuration && Object.keys(result.configuration).length > 0 && (
        <Card className="dark:bg-gray-800">
          <CardHeader>
            <CardTitle className="text-lg dark:text-white">Execution Configuration</CardTitle>
            <CardDescription className="dark:text-gray-300">Settings and parameters used for test execution</CardDescription>
          </CardHeader>
          <CardContent>
            <ExpandableText
              text={JSON.stringify(result.configuration, null, 2)}
              maxLength={300}
              className="text-xs"
              contentClassName="bg-gray-50 dark:bg-gray-700 p-3 rounded border dark:border-gray-600 text-gray-900 dark:text-gray-100 font-mono whitespace-pre-wrap break-all max-w-full block"
            />
          </CardContent>
        </Card>
      )}

      {/* Raw Data */}
      {result.raw_data && Object.keys(result.raw_data).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Raw Execution Data</CardTitle>
            <CardDescription>Unprocessed data from test execution for debugging and analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <ExpandableText
              text={JSON.stringify(result.raw_data, null, 2)}
              maxLength={300}
              className="text-xs"
              contentClassName="bg-gray-50 p-3 rounded border font-mono whitespace-pre-wrap break-all max-w-full block"
            />
          </CardContent>
        </Card>
      )}

      {/* Errors */}
      {result.errors && result.errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg text-red-600">Errors</CardTitle>
            <CardDescription>Issues encountered during execution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {result.errors.map((error, index) => (
                <div key={index} className="p-3 bg-red-50 border border-red-200 rounded overflow-hidden">
                  <ExpandableText
                    text={error}
                    maxLength={200}
                    className="text-sm"
                    contentClassName="text-red-700 text-sm break-words whitespace-pre-wrap"
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fallback content if no metadata is available */}
      {!hasVisitedUrls && !hasInteractedElements && !hasAdditionalMetadata && (!result.errors || result.errors.length === 0) && (
        <Card className="dark:bg-gray-800">
          <CardContent className="p-8 text-center">
            <Info className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-muted-foreground dark:text-gray-400 mb-2">No additional metadata available</p>
            <p className="text-xs text-gray-500 dark:text-gray-500">
              This execution did not generate additional metadata beyond the basic step information.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Component to display step results in a user-friendly format
function StepResultsDisplay({ results }: { results: any[] }) {
  if (!Array.isArray(results) || results.length === 0) {
    return <span className="text-gray-500 italic">No results</span>;
  }

  return (
    <div className="space-y-2">
      {results.map((result, index) => {
        // Check if this is a technical string that needs parsing
        if (typeof result === 'string' && result.includes('is_done=') && result.includes('success=')) {
          // Use the existing resultProcessor to parse this technical string
          const processed = processTestResult([result]);

          return (
            <div key={index} className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  {processed.success ? (
                    <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {processed.humanMessage}
                  </div>
                  <div className="mt-1 flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${processed.success
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      }`}>
                      {processed.success ? '✅ Exitoso' : '❌ Fallido'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        }

        // Handle ActionResult format
        if (typeof result === 'string' && result.includes('ActionResult')) {
          return (
            <div key={index} className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center space-x-2 mb-2">
                <Mouse className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">Acción Ejecutada</span>
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                {result.includes('success=True') && (
                  <span className="inline-flex items-center space-x-1">
                    <CheckCircle2 className="h-3 w-3 text-green-600" />
                    <span>Acción completada exitosamente</span>
                  </span>
                )}
                {result.includes('success=False') && (
                  <span className="inline-flex items-center space-x-1">
                    <XCircle className="h-3 w-3 text-red-600" />
                    <span>La acción falló</span>
                  </span>
                )}
                {!result.includes('success=') && <span>Acción ejecutada</span>}
              </div>
            </div>
          );
        }

        // Handle DoneResult format
        if (typeof result === 'string' && result.includes('DoneResult')) {
          return (
            <div key={index} className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="text-sm font-medium text-green-800 dark:text-green-200">Tarea Completada</span>
              </div>
              <div className="text-sm text-green-700 dark:text-green-300">
                {result.includes('success=True') && (
                  <span className="inline-flex items-center space-x-1">
                    <CheckCircle2 className="h-3 w-3 text-green-600" />
                    <span>Tarea completada exitosamente</span>
                  </span>
                )}
                {result.includes('success=False') && (
                  <span className="inline-flex items-center space-x-1">
                    <XCircle className="h-3 w-3 text-red-600" />
                    <span>La tarea falló</span>
                  </span>
                )}
              </div>
            </div>
          );
        }

        // Try to parse as JSON object
        let parsedResult = result;
        if (typeof result === 'string') {
          try {
            parsedResult = JSON.parse(result);
          } catch (e) {
            // If parsing fails, treat as string
          }
        }

        // Handle object results
        if (typeof parsedResult === 'object' && parsedResult !== null) {
          return (
            <div key={index} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="space-y-2">
                {Object.entries(parsedResult).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                      {key.replace(/_/g, ' ')}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {typeof value === 'boolean' ? (
                        <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${value ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                          }`}>
                          {value ? <CheckCircle2 className="h-3 w-3" /> : <XCircle className="h-3 w-3" />}
                          <span>{value ? 'Exitoso' : 'Fallido'}</span>
                        </span>
                      ) : typeof value === 'string' && value.length > 50 ? (
                        <span title={value}>{value.substring(0, 50)}...</span>
                      ) : (
                        String(value)
                      )}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          );
        }

        // Fallback for simple string values
        return (
          <div key={index} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-start space-x-2">
              <Info className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-gray-700 dark:text-gray-300">
                {String(result).length > 100 ? (
                  <details className="cursor-pointer">
                    <summary className="font-medium hover:text-gray-900 dark:hover:text-gray-100">
                      {String(result).substring(0, 100)}...
                    </summary>
                    <div className="mt-2 text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                      {String(result)}
                    </div>
                  </details>
                ) : (
                  String(result)
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

export function RichResultsViewer({ result, onAnalysisComplete }: RichResultsViewerProps) {
  // Process the result to ensure compatibility with our display logic
  const processedResult = processStandardResult(result);
  
  // Enhanced debug logging to see EVERYTHING we're receiving
  console.log("🔍 RichResultsViewer received result:", result);
  console.log("🔧 Processed result:", processedResult);
  console.log("📊 Steps count (processedResult.steps?.length):", processedResult.steps?.length || 0);
  console.log("📈 Summary object:", processedResult.summary);
  console.log("📈 Summary.total_steps:", processedResult.summary?.total_steps);
  console.log("📈 Summary.successful_steps:", processedResult.summary?.successful_steps);
  console.log("🎯 Success flag:", processedResult.success);
  console.log("⚡ Status:", processedResult.status);
  console.log("🎬 Artifacts:", processedResult.artifacts);
  console.log("🖼️ Screenshots in artifacts:", processedResult.artifacts?.screenshots);
  console.log("📸 Screenshots count:", processedResult.artifacts?.screenshots?.length || 0);
  console.log("🔍 First 3 steps details:", processedResult.steps?.slice(0, 3));

  // DEBUG: AI Analysis data
  console.log("🤖 AI Analysis Debug:");
  console.log("📋 Metadata:", processedResult.metadata);
  console.log("🧠 AI Analysis:", processedResult.metadata?.ai_analysis);
  console.log("📊 AI Analysis Status:", processedResult.metadata?.ai_analysis_status);
  console.log("🆔 AI Analysis Job ID:", processedResult.metadata?.ai_analysis_job_id);
  if (processedResult.metadata?.ai_analysis) {
    console.log("📝 Step Analyses:", processedResult.metadata.ai_analysis.step_analyses);
    console.log("✅ Completion Analysis:", processedResult.metadata.ai_analysis.completion_analysis);
    console.log("📋 Summary:", processedResult.metadata.ai_analysis.summary);
  }
  console.log("📸 Steps with screenshots:", processedResult.steps?.filter((step: any) => step.screenshot_url || step.metadata?.state?.screenshot).map((step: any) => ({
    step_number: step.step_number,
    screenshot_url: step.screenshot_url,
    has_metadata_screenshot: !!step.metadata?.state?.screenshot
  })));
  console.log("🗃️ All result keys:", Object.keys(processedResult));
  console.log("📦 Raw metadata:", processedResult.metadata);

  // Fix summary if it's incorrect but we have steps
  const actualStepsCount = processedResult.steps?.length || 0;
  const reportedStepsCount = processedResult.summary?.total_steps || 0;

  if (actualStepsCount !== reportedStepsCount) {
    console.warn(`⚠️ MISMATCH: Actual steps (${actualStepsCount}) != Reported steps (${reportedStepsCount})`);
  }

  const statusColor = processedResult.success ? 'text-green-600' : 'text-red-600';
  const statusIcon = processedResult.success ? <CheckCircle2 className="h-6 w-6" /> : <XCircle className="h-6 w-6" />;

  return (
    <div className="space-y-4 sm:space-y-6 max-w-full overflow-hidden">
      {/* Header */}
      <Card className="dark:bg-gray-800">
        <CardHeader className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
              <div className={statusColor}>
                {statusIcon}
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-xl dark:text-white">
                  Test Execution Results
                </CardTitle>
                <CardDescription className="dark:text-gray-300 text-sm break-words">
                  <div className="space-y-1">
                    <div>Execution ID: <span className="font-mono text-xs">{result.execution_id}</span></div>
                    <div>Type: {result.test_type}{result.message && ` • ${result.message}`}</div>
                  </div>
                </CardDescription>
                {/* Show data debugging info */}
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 break-words">
                  Steps in data: {actualStepsCount} • Summary reports: {reportedStepsCount}
                  {actualStepsCount !== reportedStepsCount && (
                    <span className="text-orange-600 dark:text-orange-400 font-medium"> • MISMATCH DETECTED</span>
                  )}
                </div>
              </div>
            </div>
            <Badge variant={(processedResult.steps && processedResult.steps.length > 0 ? processedResult.steps.every((s: any) => s.success) : processedResult.success) ? "default" : "destructive"} className="text-sm sm:text-lg px-3 sm:px-4 py-1 sm:py-2 flex-shrink-0 self-start sm:self-center">
              {(processedResult.steps && processedResult.steps.length > 0 ? processedResult.steps.every((s: any) => s.success) : processedResult.success) ? "PASSED" : "FAILED"}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Summary */}
      <ExecutionSummary result={processedResult} />

      {/* Detailed Results */}
      <Tabs defaultValue="steps" className="w-full">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 dark:bg-gray-800 h-auto">
          <TabsTrigger value="steps" className="dark:text-gray-300 dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white h-10 text-xs sm:text-sm px-2 sm:px-4">Steps</TabsTrigger>
          <TabsTrigger value="artifacts" className="dark:text-gray-300 dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white h-10 text-xs sm:text-sm px-2 sm:px-4">Artifacts</TabsTrigger>
          <TabsTrigger value="metadata" className="dark:text-gray-300 dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white h-10 text-xs sm:text-sm px-2 sm:px-4">Metadata</TabsTrigger>
          <TabsTrigger value="raw" className="dark:text-gray-300 dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white h-10 text-xs sm:text-sm px-2 sm:px-4">Raw Data</TabsTrigger>
        </TabsList>

        <TabsContent value="steps" className="mt-4 sm:mt-6 overflow-hidden min-h-[300px] sm:min-h-[400px]">
          <div className="space-y-3 sm:space-y-4 max-w-full overflow-hidden">
            {/* AI Test Analysis Summary */}
            {renderAITestSummary(processedResult, onAnalysisComplete)}

            {processedResult.steps && processedResult.steps.length > 0 ? (
              processedResult.steps.map((step: any, index: number) => (
                <StepCard key={step.step_number} step={step} index={index} result={processedResult} />
              ))
            ) : (
              <Card className="dark:bg-gray-800">
                <CardContent className="p-6 text-center">
                  <p className="text-gray-500 dark:text-gray-400">No steps available for this execution</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="artifacts" className="mt-4 sm:mt-6 overflow-hidden min-h-[300px] sm:min-h-[400px]">
          <div className="space-y-3 sm:space-y-4 max-w-full overflow-hidden">
            {/* Screenshots */}
            <Card className="dark:bg-gray-800">
              <CardHeader>
                <CardTitle className="dark:text-white">Screenshots</CardTitle>
                <CardDescription className="dark:text-gray-300">
                  Visual captures from test execution (ordered by steps)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <EnhancedScreenshotsViewer result={processedResult} />
              </CardContent>
            </Card>

            {/* Videos */}
            {processedResult.artifacts?.videos?.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Videos</CardTitle>
                  <CardDescription>
                    Video recordings from test execution
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {processedResult.artifacts.videos.map((video: any, index: number) => (
                      <div key={index} className="p-3 bg-blue-50 rounded border text-center overflow-hidden">
                        <Monitor className="h-8 w-8 mx-auto mb-2 text-blue-400" />
                        <p className="text-sm font-mono break-all">{video}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Logs */}
            {processedResult.artifacts?.logs?.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Logs</CardTitle>
                  <CardDescription>
                    Execution logs and debug information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {processedResult.artifacts.logs.map((log: any, index: number) => (
                      <div key={index} className="p-3 bg-gray-50 rounded border overflow-hidden">
                        <p className="text-sm font-mono break-all">{log}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Generated Code */}
            {processedResult.artifacts?.generated_code && (
              <Card>
                <CardHeader>
                  <CardTitle>Generated Code</CardTitle>
                  <CardDescription>
                    Auto-generated test code from execution
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="p-3 bg-green-50 rounded border overflow-hidden">
                    <p className="text-sm font-mono break-all">{processedResult.artifacts.generated_code}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* History File */}
            {processedResult.artifacts?.history_file && (
              <Card>
                <CardHeader>
                  <CardTitle>History File</CardTitle>
                  <CardDescription>
                    Detailed execution history file
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="p-3 bg-yellow-50 rounded border overflow-hidden">
                    <p className="text-sm font-mono break-all">{processedResult.artifacts.history_file}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Gherkin Scenarios */}
            {processedResult.artifacts?.gherkin_scenarios?.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Gherkin Scenarios</CardTitle>
                  <CardDescription>
                    Generated Gherkin scenarios from test execution
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {processedResult.artifacts.gherkin_scenarios.map((scenario: any, index: number) => (
                      <div key={index} className="p-3 bg-purple-50 rounded border overflow-hidden">
                        <pre className="text-sm font-mono whitespace-pre-wrap break-all max-w-full">{scenario}</pre>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>
        <TabsContent value="metadata" className="mt-6 overflow-hidden min-h-[400px]">
          <MetadataTab result={processedResult} />
        </TabsContent>

        <TabsContent value="raw" className="mt-6 overflow-hidden min-h-[400px]">
          <Card className="dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="dark:text-white">Complete Result Object</CardTitle>
              <CardDescription className="dark:text-gray-300">
                Full StandardResult object for advanced debugging and analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ExpandableText
                text={JSON.stringify(processedResult, null, 2)}
                maxLength={500}
                className="text-xs"
                contentClassName="bg-gray-50 dark:bg-gray-900 p-4 rounded border whitespace-pre-wrap break-all max-w-full text-gray-900 dark:text-gray-100 font-mono block"
                buttonTexts={{ showMore: 'Show full JSON', showLess: 'Hide full JSON' }}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// AI Analysis Components
function renderAIStepAnalysis(step: StandardTestStep, result: StandardResult) {
  // Get AI analysis from result metadata
  const aiAnalysis = result.metadata?.ai_analysis;
  if (!aiAnalysis || !aiAnalysis.step_analyses) {
    return null;
  }

  // Find analysis for this specific step
  const stepAnalysis = aiAnalysis.step_analyses.find(
    (analysis: any) => analysis.step_number === step.step_number
  );

  if (!stepAnalysis || stepAnalysis.fallback_analysis) {
    return null;
  }

  console.log(`🔍 Rendering step ${step.step_number} AI analysis:`, {
    overallAssessment: stepAnalysis.overall_assessment,
    confidenceScore: stepAnalysis.confidence_score,
    issuesCount: stepAnalysis.issues_detected?.length || 0,
    hasPositiveIndicators: !!stepAnalysis.positive_indicators?.length
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
      case 'warning': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';
      case 'failure': return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle2 className="h-4 w-4" />;
      case 'warning': return <Info className="h-4 w-4" />;
      case 'failure': return <XCircle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <div className={`p-3 rounded-md ${getStatusColor(stepAnalysis.overall_assessment)}`}>
      <div className="flex items-center space-x-2 mb-2">
        {getStatusIcon(stepAnalysis.overall_assessment)}
        <span className="text-sm font-medium">AI Quality Analysis</span>
        <Badge variant="outline" className="text-xs">
          {Math.round(stepAnalysis.confidence_score * 100)}% confident
        </Badge>
      </div>

      {/* Summary */}
      <p className="text-sm mb-3">{stepAnalysis.summary}</p>

      {/* Issues Detected */}
      {stepAnalysis.issues_detected && stepAnalysis.issues_detected.length > 0 && (
        <div className="space-y-2 mb-3">
          <span className="text-xs font-medium">Issues Detected:</span>
          {stepAnalysis.issues_detected.map((issue: any, idx: number) => (
            <div key={idx} className="text-xs p-2 bg-white/50 dark:bg-black/20 rounded border">
              <div className="flex items-center space-x-1 mb-1">
                <Badge variant={issue.severity === 'high' ? 'destructive' : issue.severity === 'medium' ? 'secondary' : 'outline'} className="text-xs">
                  {safeRender(issue.severity) || 'unknown'}
                </Badge>
                <span className="font-medium">{safeRender(issue.category) || 'General'}</span>
              </div>
              <p>{safeRender(issue.description) || 'No description'}</p>
              {issue.suggestion && (
                <p className="text-xs italic mt-1">💡 {safeRender(issue.suggestion)}</p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Positive Indicators */}
      {stepAnalysis.positive_indicators && stepAnalysis.positive_indicators.length > 0 && (
        <div className="mb-3">
          <span className="text-xs font-medium">Positive Signs:</span>
          <ul className="text-xs mt-1 space-y-1">
            {stepAnalysis.positive_indicators.map((indicator: any, idx: number) => (
              <li key={idx} className="flex items-start space-x-1">
                <span className="text-green-600 dark:text-green-400">✓</span>
                <span>{safeRender(indicator)}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Recommendations */}
      {stepAnalysis.recommendations && stepAnalysis.recommendations.length > 0 && (
        <ExpandableText
          text={Array.isArray(stepAnalysis.recommendations) ? stepAnalysis.recommendations.map(safeRender).join('; ') : safeRender(stepAnalysis.recommendations)}
          maxLength={150}
          className="text-xs"
        />
      )}
    </div>
  );
}

function renderManualAnalysisTrigger(result: StandardResult, onAnalysisComplete?: () => void) {
  const [isStartingAnalysis, setIsStartingAnalysis] = useState(false);
  const [analysisJobId, setAnalysisJobId] = useState<string | null>(null);
  const startAnalysis = async () => {
    setIsStartingAnalysis(true);
    try {
      const response = await fetch(`${API_BASE_URL}/v2/background-jobs/analysis/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          execution_id: result.execution_id,
          result_data: result,
          screenshot_data: result.artifacts?.screenshots || []
        })
      });

      if (response.ok) {
        const data = await response.json();
        setAnalysisJobId(data.job_id);
      } else {
        console.error('Failed to start analysis:', await response.text());
      }
    } catch (error) {
      console.error('Error starting analysis:', error);
    } finally {
      setIsStartingAnalysis(false);
    }
  };

  // If analysis has been started, show progress indicator
  if (analysisJobId) {
    return (
      <AnalysisProgressIndicator
        jobId={analysisJobId}
        onAnalysisComplete={(result) => {
          console.log('Manual analysis completed:', result);
          setAnalysisJobId(null);
          if (onAnalysisComplete) {
            onAnalysisComplete();
          }
        }}
        onAnalysisError={(error) => {
          console.error('Manual analysis failed:', error);
          setAnalysisJobId(null);
        }}
        className="mb-4"
      />
    );
  }

  return (
    <Card className="mb-4 border-dashed border-2 border-gray-300 dark:border-gray-600">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-gray-500" />
            <CardTitle className="text-lg text-gray-700 dark:text-gray-300">AI Analysis Not Available</CardTitle>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          This execution doesn't have AI analysis results. You can generate them now to get detailed insights about test quality and step-by-step analysis.
        </p>
        <Button
          onClick={startAnalysis}
          disabled={isStartingAnalysis}
          className="w-full"
        >
          {isStartingAnalysis ? 'Starting Analysis...' : 'Generate AI Analysis'}
        </Button>
      </CardContent>
    </Card>
  );
}

function renderFailedAnalysisCard(result: StandardResult, onAnalysisComplete?: () => void) {
  const [isRetrying, setIsRetrying] = useState(false);

  const retryAnalysis = async () => {
    setIsRetrying(true);
    try {
      const response = await fetch(`${API_BASE_URL}/v2/background-jobs/analysis/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          execution_id: result.execution_id,
          result_data: result,
          screenshot_data: result.artifacts?.screenshots || []
        })
      });

      if (response.ok && onAnalysisComplete) {
        setTimeout(onAnalysisComplete, 2000); // Trigger refresh after starting
      }
    } catch (error) {
      console.error('Error retrying analysis:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  return (
    <Card className="mb-4 border-2 border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <CardTitle className="text-lg text-red-700 dark:text-red-300">AI Analysis Failed</CardTitle>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-red-600 dark:text-red-400">
          The AI analysis for this execution failed to complete. You can try generating it again.
        </p>
        <Button
          onClick={retryAnalysis}
          disabled={isRetrying}
          className="w-full"
          variant="outline"
        >
          {isRetrying ? 'Retrying Analysis...' : 'Retry AI Analysis'}
        </Button>
      </CardContent>
    </Card>
  );
}

function renderAITestSummary(result: StandardResult, onAnalysisComplete?: () => void) {
  const aiAnalysis = result.metadata?.ai_analysis;
  const aiAnalysisJobId = result.metadata?.ai_analysis_job_id;
  const aiAnalysisStatus = result.metadata?.ai_analysis_status;
  // Show progress indicator if analysis is in progress
  // Check multiple conditions to determine if analysis is in progress
  const isAnalysisInProgress = (
    aiAnalysisJobId &&
    (
      aiAnalysisStatus === "processing" ||
      (aiAnalysisStatus !== "completed" && (!aiAnalysis || (!aiAnalysis.completion_analysis && !aiAnalysis.summary)))
    )
  );

  // Enhanced logging for debugging
  console.log('🔍 AI Analysis Status Check:', {
    aiAnalysisJobId,
    aiAnalysisStatus,
    hasAnalysis: !!aiAnalysis,
    hasCompletionAnalysis: !!aiAnalysis?.completion_analysis,
    hasSummary: !!aiAnalysis?.summary,
    hasStepAnalyses: !!aiAnalysis?.step_analyses,
    stepAnalysesCount: aiAnalysis?.step_analyses?.length || 0,
    isAnalysisInProgress,
    finalVerdict: aiAnalysis?.completion_analysis?.final_verdict || aiAnalysis?.summary?.final_verdict
  });

  if (isAnalysisInProgress) {
    return (
      <AnalysisProgressIndicator
        jobId={aiAnalysisJobId}
        onAnalysisComplete={(result) => {
          console.log('🎉 Analysis completed, triggering refresh:', result);
          // Trigger a refresh of the execution data
          if (onAnalysisComplete) {
            onAnalysisComplete();
          }
        }}
        onAnalysisError={(error) => {
          console.error('❌ Analysis failed:', error);
        }}
        className="mb-4"
      />
    );
  }

  // Show manual analysis trigger if no analysis exists
  // Check both completion_analysis and summary as fallback
  if (!aiAnalysis || (!aiAnalysis.completion_analysis && !aiAnalysis.summary)) {
    console.log('🔄 No AI analysis found, showing manual trigger');
    // Check if analysis failed
    if (aiAnalysisStatus === "failed" || aiAnalysisStatus === "error") {
      return renderFailedAnalysisCard(result, onAnalysisComplete);
    }
    return renderManualAnalysisTrigger(result, onAnalysisComplete);
  }

  // Use completion_analysis if available, otherwise use summary as fallback
  const completion = aiAnalysis.completion_analysis || aiAnalysis.summary;
  const summary = aiAnalysis.summary;

  console.log('✅ Rendering AI analysis with completion data:', {
    hasCompletion: !!completion,
    finalVerdict: completion?.final_verdict,
    confidenceLevel: completion?.confidence_level,
    hasExecutionQuality: !!completion?.execution_quality,
    hasRecommendations: !!completion?.recommendations
  });

  const getVerdictColor = (verdict: string) => {
    switch (verdict) {
      case 'PASS': return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'FAIL': return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'INCONCLUSIVE': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800';
    }
  };

  // Get the final verdict with fallbacks
  const finalVerdict = completion?.final_verdict || completion?.verdict || summary?.final_verdict || 'UNKNOWN';
  const confidenceLevel = completion?.confidence_level || completion?.confidence || 0.5;

  return (
    <Card className={`${getVerdictColor(finalVerdict)} border-2`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <CardTitle className="text-lg">AI Test Analysis</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={finalVerdict === 'PASS' ? 'default' : finalVerdict === 'FAIL' ? 'destructive' : 'secondary'}>
              {finalVerdict}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {Math.round(confidenceLevel * 100)}% confident
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary */}
        <div>
          <h4 className="font-medium mb-2">Analysis Summary</h4>
          <p className="text-sm">{safeRender(completion.summary) || 'No summary available'}</p>
        </div>

        {/* Detailed Reasoning */}
        {completion.detailed_reasoning && (
          <div>
            <h4 className="font-medium mb-2">Detailed Reasoning</h4>
            <ExpandableText
              text={formatAIAnalysis(completion.detailed_reasoning)}
              maxLength={300}
              className="text-sm"
              contentClassName="whitespace-pre-line text-sm"
            />
          </div>
        )}

        {/* Execution Quality Metrics */}
        {completion.execution_quality && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
            <h4 className="font-semibold mb-3 text-gray-800 dark:text-gray-200 flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
              Quality Metrics
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Stability Score */}
              <div className="text-center p-3 bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {completion.execution_quality.stability_score !== null && completion.execution_quality.stability_score !== undefined && !isNaN(completion.execution_quality.stability_score)
                    ? Math.round(completion.execution_quality.stability_score * 100) + '%'
                    : <span className="text-gray-400 text-lg">N/A</span>}
                </div>
                <div className="text-xs font-medium text-gray-600 dark:text-gray-300 mt-1">Stability</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Execution consistency</div>
              </div>

              {/* Coverage Score */}
              <div className="text-center p-3 bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {completion.execution_quality.coverage_score !== null && completion.execution_quality.coverage_score !== undefined && !isNaN(completion.execution_quality.coverage_score)
                    ? Math.round(completion.execution_quality.coverage_score * 100) + '%'
                    : <span className="text-gray-400 text-lg">N/A</span>}
                </div>
                <div className="text-xs font-medium text-gray-600 dark:text-gray-300 mt-1">Coverage</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Test completeness</div>
              </div>

              {/* Reliability Score */}
              <div className="text-center p-3 bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {completion.execution_quality.reliability_score !== null && completion.execution_quality.reliability_score !== undefined && !isNaN(completion.execution_quality.reliability_score)
                    ? Math.round(completion.execution_quality.reliability_score * 100) + '%'
                    : <span className="text-gray-400 text-lg">N/A</span>}
                </div>
                <div className="text-xs font-medium text-gray-600 dark:text-gray-300 mt-1">Reliability</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">Test repeatability</div>
              </div>

              {/* Issues Count */}
              <div className="text-center p-3 bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {completion.execution_quality.issues_count ?
                    (completion.execution_quality.issues_count.critical || 0) +
                    (completion.execution_quality.issues_count.major || 0) +
                    (completion.execution_quality.issues_count.minor || 0)
                    : (summary?.steps_with_issues || 0)}
                </div>
                <div className="text-xs font-medium text-gray-600 dark:text-gray-300 mt-1">Issues</div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {completion.execution_quality.issues_count && (
                    <>C:{completion.execution_quality.issues_count.critical || 0} M:{completion.execution_quality.issues_count.major || 0} m:{completion.execution_quality.issues_count.minor || 0}</>
                  )}
                </div>
              </div>
            </div>

            {/* Overall Quality Indicator */}
            {completion.execution_quality.stability_score !== null && completion.execution_quality.coverage_score !== null && completion.execution_quality.reliability_score !== null && (
              <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Overall Quality</span>
                  <div className="flex items-center space-x-2">
                    {(() => {
                      const avgScore = (completion.execution_quality.stability_score + completion.execution_quality.coverage_score + completion.execution_quality.reliability_score) / 3;
                      const percentage = Math.round(avgScore * 100);
                      const color = percentage >= 80 ? 'text-green-600 dark:text-green-400' :
                        percentage >= 60 ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-red-600 dark:text-red-400';
                      return (
                        <>
                          <span className={`text-lg font-bold ${color}`}>{percentage}%</span>
                          <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className={`h-full transition-all duration-300 ${percentage >= 80 ? 'bg-green-500' : percentage >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`}
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Acceptance Criteria Analysis */}
        {completion.acceptance_criteria_analysis && (
          <div>
            <h4 className="font-medium mb-2">Acceptance Criteria</h4>
            <div className="space-y-2">
              {completion.acceptance_criteria_analysis.details?.map((criterion: any, idx: number) => (
                <div key={idx} className="flex items-center space-x-2 p-2 bg-white/50 dark:bg-black/20 rounded">
                  {criterion.status === 'met' ? (
                    <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
                  ) : criterion.status === 'partial' ? (
                    <Info className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                  )}
                  <div className="flex-1">
                    <div className="text-sm font-medium">{criterion.criterion}</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">{criterion.evidence}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recommendations */}
        {completion.recommendations && completion.recommendations.length > 0 && (
          <div>
            <h4 className="font-medium mb-2">Recommendations</h4>
            <ul className="text-sm space-y-1">
              {completion.recommendations.map((rec: string, idx: number) => (
                <li key={idx} className="flex items-start space-x-2">
                  <span className="text-blue-600 dark:text-blue-400">•</span>
                  <span>{rec}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Next Actions */}
        {completion.next_actions && completion.next_actions.length > 0 && (
          <div>
            <h4 className="font-medium mb-2">Next Actions</h4>
            <ul className="text-sm space-y-1">
              {completion.next_actions.map((action: string, idx: number) => (
                <li key={idx} className="flex items-start space-x-2">
                  <span className="text-purple-600 dark:text-purple-400">→</span>
                  <span>{action}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}