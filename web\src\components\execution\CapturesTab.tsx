"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import type { TestExecutionHistoryData } from "@/lib/types";
import { Download, Expand, Image as ImageIcon } from "lucide-react";
import Image from 'next/image';
import { useCallback, useState } from "react";
import { getScreenshotUrlFromArtifactPath } from "./RichResultsViewer";

interface CapturesTabProps {
  historyData: TestExecutionHistoryData;
}

export function CapturesTab({ historyData }: CapturesTabProps) {
  const { screenshots = [] } = historyData;
  const [selectedScreenshot, setSelectedScreenshot] = useState<string | null>(null);
  const [imageErrors, setImageErrors] = useState<Record<number, boolean>>({});

  // Helper to check if a string is a Base64 image
  const isBase64Image = (str: string) => {
    if (!str) return false;
    return str.startsWith('data:image') || str.startsWith('data:image/');
  };

  // Helper to check if it's a valid URL (RFC compliant)
  const isValidUrl = (str: string) => {
    if (!str) return false;
    try {
      new URL(str);
      return true;
    } catch {
      return false;
    }
  };

  // Handle image load error to display placeholder
  const handleImageError = useCallback((index: number) => {
    setImageErrors((prev) => ({ ...prev, [index]: true }));
  }, []);


  // Function to download screenshot (handles both base64 and remote URLs)
  const downloadScreenshot = async (screenshot: string, index: number) => {
    try {
      if (isBase64Image(screenshot)) {
        const link = document.createElement('a');
        link.href = screenshot;
        link.download = `screenshot_${index + 1}_${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        return;
      }

      if (isValidUrl(screenshot)) {
        const resp = await fetch(screenshot, { cache: 'no-store', credentials: 'include' });
        if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
        const blob = await resp.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `screenshot_${index + 1}_${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        return;
      }
      console.warn('Cannot download screenshot: unsupported format');
    } catch (err) {
      console.error('Error downloading screenshot:', err);
    }
  };


  return (
    <Card>
      <CardHeader>
        <CardTitle className="glow-text">Screenshots</CardTitle>
        <CardDescription>
          {screenshots && screenshots.length > 0
            ? `${screenshots.length} screenshot${screenshots.length > 1 ? 's' : ''} captured during execution`
            : 'No screenshots were captured during this test execution'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {screenshots && screenshots.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {screenshots.map((screenshot, index) => {
              // Usar la función centralizada para obtener la URL
              const screenshotUrl = getScreenshotUrlFromArtifactPath(screenshot);
              return (
                <div key={index} className="border rounded-lg overflow-hidden shadow-md">
                  <CardHeader className="p-3 flex flex-row items-center justify-between">
                    <CardDescription>Screenshot {index + 1}</CardDescription>
                    <div className="flex gap-2">
                      {(isBase64Image(screenshot) || isValidUrl(screenshot)) && (
                        <>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setSelectedScreenshot(screenshot)}
                              >
                                <Expand className="h-4 w-4" />
                                <span className="sr-only">Ver en tamaño completo</span>
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
                              <DialogHeader>
                                <DialogTitle>Screenshot {index + 1} - Vista completa</DialogTitle>
                              </DialogHeader>
                              <div className="relative w-full">
                                <Image
                                  src={screenshotUrl}
                                  alt={`Screenshot ${index + 1} - Full size`}
                                  width={1200}
                                  height={800}
                                  className="w-full h-auto object-contain rounded-lg"
                                  unoptimized={isBase64Image(screenshot)}
                                />
                              </div>
                              <div className="flex justify-end mt-4">
                                <Button
                                  onClick={() => downloadScreenshot(screenshot, index)}
                                  className="flex items-center gap-2"
                                >
                                  <Download className="h-4 w-4" />
                                  Descargar
                                </Button>
                              </div>
                            </DialogContent>
                          </Dialog>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => downloadScreenshot(screenshot, index)}
                          >
                            <Download className="h-4 w-4" />
                            <span className="sr-only">Descargar</span>
                          </Button>
                        </>
                      )}
                    </div>
                  </CardHeader>
                  <div className="relative bg-muted min-h-[300px] cursor-pointer rounded-lg p-2 flex items-center justify-center">
                    {(!imageErrors[index]) && (isBase64Image(screenshot) || isValidUrl(screenshot)) ? (
                      <Dialog>
                        <DialogTrigger asChild>
                          <div className="w-full relative group">
                            <Image
                              src={screenshotUrl}
                              alt={`Screenshot ${index + 1}`}
                              width={800}
                              height={600}
                              className="w-full h-auto object-contain transition-opacity group-hover:opacity-80 max-h-[800px] rounded shadow-sm"
                              style={{
                                maxHeight: '800px',
                                width: '100%',
                                height: 'auto',
                                objectFit: 'contain',
                                aspectRatio: 'unset'
                              }}
                              unoptimized={isBase64Image(screenshot)}
                              onError={() => handleImageError(index)}
                            />
                            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded">
                              <div className="bg-white/90 p-2 rounded-full">
                                <Expand className="h-6 w-6 text-gray-700" />
                              </div>
                            </div>
                          </div>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
                          <DialogHeader>
                            <DialogTitle>Screenshot {index + 1} - Vista completa</DialogTitle>
                          </DialogHeader>
                          <div className="relative w-full">
                            <Image
                              src={screenshotUrl}
                              alt={`Screenshot ${index + 1} - Full size`}
                              width={1200}
                              height={800}
                              className="w-full h-auto object-contain rounded-lg"
                              unoptimized={isBase64Image(screenshot)}
                            />
                          </div>
                          <div className="flex justify-end mt-4">
                            <Button
                              onClick={() => downloadScreenshot(screenshot, index)}
                              className="flex items-center gap-2"
                            >
                              <Download className="h-4 w-4" />
                              Descargar
                            </Button>
                          </div>
                        </DialogContent>
                      </Dialog>
                    ) : (
                      // Failed to load or unsupported format: show placeholder
                      <div className="flex items-center justify-center h-full text-muted-foreground">
                        <div className="text-center">
                          <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                          <p className="text-sm font-medium">Image path:</p>
                          <p className="text-xs break-all mt-1">{screenshot.substring(0, 50)}{screenshot.length > 50 ? '...' : ''}</p>
                          <p className="text-xs mt-2 opacity-75">Unable to display image</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No screenshots available for this execution.</p>
            <p className="text-sm text-muted-foreground mt-2">Screenshots are captured during automated test runs.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
