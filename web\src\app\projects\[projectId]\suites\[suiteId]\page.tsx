"use client";

import Link from 'next/link';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getSuiteById, getTestCasesBySuiteId, deleteSuite } from '@/lib/api/projects';
import { executeTest, pauseExecution, resumeExecution, stopExecution, getExecutionById } from '@/lib/api/execution';
import { getProjectEnvironments } from '@/lib/api';
import { TestSuite, TestCase, ExecutionResponse as ExecutionResponseV2, ExecutionRequest, ExecutionTypeEnum, ExecutionStatus } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { ClipboardList, TestTube2, PlusCircle, ArrowLeft, Edit, Trash2, Play, AlertCircle, CheckCircle, XCircle, Clock, Repeat } from 'lucide-react';
import { format } from 'date-fns';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import React, { useEffect, useState, useMemo } from 'react';
import { CompactEnvironmentSelector } from '@/components/environments/CompactEnvironmentSelector';

function TestCaseCard({ testCase, projectId, suiteId }: { testCase: TestCase; projectId: string; suiteId: string }) {
  let statusColorClass = "status-neutral";
  let StatusIcon = Clock;

  if (testCase.status === 'Passed') {
    statusColorClass = "status-success";
    StatusIcon = CheckCircle;
  } else if (testCase.status === 'Failed') {
    statusColorClass = "status-failed";
    StatusIcon = XCircle;
  }

  return (
    <Card className="flex flex-col">
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="flex items-center gap-2">
            <TestTube2 className="h-6 w-6 text-primary" />
            {testCase.name}
          </CardTitle>
          <Badge className={`status-badge ${statusColorClass}`}>
            <StatusIcon className="mr-1 h-3 w-3" />
            {testCase.status}
          </Badge>
        </div>
        <CardDescription className="line-clamp-2 pt-1">{testCase.description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="mb-2">
          <span className="text-sm font-semibold">Tags:</span>
          <div className="flex flex-wrap gap-1 mt-1">
            {testCase.tags.length > 0 ? (
              testCase.tags.map((tag, index) => <Badge key={`${testCase.id}-tag-${index}-${tag}`} variant="secondary">{tag}</Badge>)
            ) : (
              <span className="text-xs text-muted-foreground">No tags</span>
            )}
          </div>
        </div>
        <p className="text-xs text-muted-foreground">
          Last updated: {format(new Date(testCase.updated_at), 'PPP')}
        </p>
        {testCase.last_execution && (
          <p className="text-xs text-muted-foreground">
            Last execution: {format(new Date(testCase.last_execution), 'PPP p')}
          </p>
        )}
      </CardContent>
      <CardFooter>
        <Button asChild size="sm" className="w-full">
          <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testCase.id}`}>
            View Test Case
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

function TestCaseListSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
      {[...Array(3)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <Skeleton className="h-6 w-3/5 mb-1" />
              <Skeleton className="h-5 w-20 rounded-full" />
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6 mt-1" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-1/4 mb-2" />
            <div className="flex gap-1 mt-1">
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
            <Skeleton className="h-3 w-1/2 mt-3" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-9 w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}


export default function SuiteDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const [executionTimes, setExecutionTimes] = useState(1);
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [currentExecutionStatus, setCurrentExecutionStatus] = useState<ExecutionStatus | null>(null);
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string | undefined>();
  const [applicationVersion, setApplicationVersion] = useState<string>('');
  const [visibleTestCases, setVisibleTestCases] = React.useState(9); // Show 9 initially (3x3 grid)

  // All hooks must be called in the same order every time
  const { data: suite, isLoading: isLoadingSuite, error: suiteError, isError: isSuiteError } = useQuery({
    queryKey: ['suite', projectId, suiteId],
    queryFn: () => getSuiteById(projectId, suiteId),
    enabled: !!projectId && !!suiteId,
  });

  // Memoize test cases mapping to avoid recalculation on every render
  // Only process test cases when suite data is available
  const testCases = useMemo(() => {
    if (!suite?.test_cases) return [];

    return suite.test_cases.map((tc: any) => ({
      test_id: tc.test_id || tc.id || tc.Id || tc.testId || tc.TestId || '',
      suite_id: tc.suite_id || tc.suiteId || tc.SuiteId || suiteId,
      project_id: tc.project_id || tc.projectId || tc.ProjectId || projectId,
      name: tc.name || tc.Name || '',
      description: tc.description || tc.Description || '',
      instructions: tc.instructions || tc.instrucciones || tc.Instrucciones || '',
      user_story: tc.user_story || tc.historia_de_usuario || tc.historiaDeUsuario || tc.HistoriaDeUsuario || '',
      gherkin: tc.gherkin || tc.Gherkin || '',
      url: tc.url || tc.Url || '',
      tags: tc.tags || tc.Tags || [],
      status: tc.status || tc.Status || 'Not Executed',
      priority: tc.priority || tc.Priority || 'Medium',
      created_at: tc.created_at || tc.createdAt || tc.CreatedAt || new Date().toISOString(),
      updated_at: tc.updated_at || tc.updatedAt || tc.UpdatedAt || new Date().toISOString(),
      history_files: tc.history_files || tc.historyFiles || tc.HistoryFiles || [],
      last_execution: tc.last_execution || tc.lastExecution || tc.LastExecution || null,
    }));
  }, [suite?.test_cases, suiteId, projectId]);

  // Memoize suite statistics to avoid recalculation
  const suiteStats = useMemo(() => {
    if (!testCases || testCases.length === 0) {
      return { totalTests: 0, passedTests: 0, progress: 0 };
    }

    const totalTests = testCases.length;
    const passedTests = testCases.filter((tc: any) => tc.status === 'Passed').length;
    const progress = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
    return { totalTests, passedTests, progress };
  }, [testCases]);

  const isLoadingTestCases = isLoadingSuite;
  const isTestCasesError = isSuiteError;
  const testCasesError = suiteError;

  // Destructure the suite stats
  const { totalTests, passedTests, progress } = suiteStats;

  // All mutations must be defined consistently
  const deleteMutation = useMutation({
    mutationFn: () => deleteSuite(projectId, suiteId),
    onSuccess: () => {
      toast({ title: "Suite Deleted", description: `Suite "${suite?.name}" has been deleted.` });
      queryClient.invalidateQueries({ queryKey: ['suites', projectId] });
      router.push(`/projects/${projectId}`);
    },
    onError: (error) => {
      toast({ title: "Error Deleting Suite", description: error.message, variant: "destructive" });
    },
  });

  const { mutate: executeSuite, isPending: isExecuting } = useMutation({
    mutationFn: () => {
      const request: ExecutionRequest = {
        type: ExecutionTypeEnum.SUITE,
        suite_id: suiteId,
        project_id: projectId,
        parallel: false, // Or make this configurable in the UI
        environment_id: selectedEnvironmentId,
        application_version: applicationVersion || undefined,
      };
      return executeTest(request);
    },
    onMutate: () => {
      setCurrentExecutionStatus(ExecutionStatus.RUNNING);
    },
    onSuccess: (data: ExecutionResponseV2) => {
      setCurrentExecutionId(data.execution_id);
      setCurrentExecutionStatus(data.status as ExecutionStatus);
      
      // Handle immediate completion (legacy synchronous mode)
      if (data.status === ExecutionStatus.SUCCESS || data.status === ExecutionStatus.FAILURE || data.status === ExecutionStatus.ERROR) {
        queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
        queryClient.invalidateQueries({ queryKey: ['suite', projectId, suiteId] });
        toast({
          title: "Suite Execution Complete",
          description: data.message || `Execution finished with status: ${data.status}.`,
        });
        // Clear execution state after a brief delay for immediate completions
        setTimeout(() => {
          setCurrentExecutionId(null);
          setCurrentExecutionStatus(null);
        }, 2000);
      } else {
        // Handle async execution - polling will start via useEffect for RUNNING, PENDING, PAUSED statuses
        toast({
          title: "Suite Execution Started",
          description: `Execution ${data.execution_id} started. Status: ${data.status}`,
        });
      }
    },
    onError: (error) => {
      toast({ title: "Error Executing Suite", description: error.message, variant: "destructive" });
      setCurrentExecutionId(null);
      setCurrentExecutionStatus(null);
    }
  });

  // Reset visible test cases when suite changes
  useEffect(() => {
    setVisibleTestCases(9);
  }, [suiteId]);

  // Polling logic for async suite execution
  useEffect(() => {
    if (!currentExecutionId) {
      return;
    }

    // Only poll if execution is in a non-final state
    const isNonFinalStatus = currentExecutionStatus === ExecutionStatus.RUNNING || 
                            currentExecutionStatus === ExecutionStatus.PENDING || 
                            currentExecutionStatus === ExecutionStatus.PAUSED;
    
    if (!isNonFinalStatus) {
      return;
    }

    let intervalId: NodeJS.Timeout;

    const pollExecutionStatus = async (executionId: string) => {
      try {
        const response = await getExecutionById(executionId);
        console.log("Suite execution polling response:", response);
        
        setCurrentExecutionStatus(response.status as ExecutionStatus);
        
        // Check if execution reached a final state
        const isFinalStatus = response.status === ExecutionStatus.SUCCESS || 
                             response.status === ExecutionStatus.FAILURE || 
                             response.status === ExecutionStatus.ERROR || 
                             response.status === ExecutionStatus.CANCELLED;
        
        if (isFinalStatus) {
          // Execution completed
          if (intervalId) clearInterval(intervalId);
          
          // Invalidate queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
          queryClient.invalidateQueries({ queryKey: ['suite', projectId, suiteId] });
          
          // Show completion toast
          toast({
            title: "Suite Execution Complete",
            description: response.message || `Execution finished with status: ${response.status}.`,
          });
          
          // Clear execution state after a brief delay to ensure UI shows final status
          setTimeout(() => {
            setCurrentExecutionId(null);
            setCurrentExecutionStatus(null);
          }, 1000);
        }
      } catch (err) {
        console.error("Failed to poll suite execution status:", err);
        if (intervalId) clearInterval(intervalId);
        setCurrentExecutionId(null);
        setCurrentExecutionStatus(null);
        toast({
          title: "Error Polling Execution",
          description: "Failed to get execution status.",
          variant: "destructive"
        });
      }
    };

    // Start polling immediately and then every 3-5 seconds based on execution state
    pollExecutionStatus(currentExecutionId);
    const pollInterval = currentExecutionStatus === ExecutionStatus.RUNNING ? 3000 : 5000;
    intervalId = setInterval(() => pollExecutionStatus(currentExecutionId), pollInterval);

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [currentExecutionId, currentExecutionStatus, queryClient, toast, projectId, suiteId]);

  // Auto-select default environment when project loads
  useEffect(() => {
    const fetchAndSelectDefaultEnvironment = async () => {
      if (projectId && !selectedEnvironmentId && suite && !isLoadingSuite) { // Only fetch when suite is loaded and not loading
        try {
          console.log("🔍 Fetching environments for project:", projectId);
          const envs = await getProjectEnvironments(projectId);
          console.log("✅ Environments retrieved:", envs);

          if (envs.length > 0) {
            const defaultEnv = envs.find(env => env.is_default) || envs[0];
            setSelectedEnvironmentId(defaultEnv.env_id);
            console.log("🔧 Suite page: Auto-selected environment:", defaultEnv.name, defaultEnv.env_id);
          } else {
            console.log("⚠️ No environments found for project:", projectId);
          }
        } catch (err) {
          console.error("Error fetching project environments:", err);

          // Enhanced error logging
          if (err instanceof Error) {
            console.error("Error details:", {
              message: err.message,
              name: err.name,
              stack: err.stack
            });
          }

          // Check if it's a fetch error with response
          if (err && typeof err === 'object' && 'status' in err) {
            console.error("HTTP Status:", (err as any).status);
          }
        }
      }
    };

    fetchAndSelectDefaultEnvironment();
  }, [projectId, selectedEnvironmentId, suite, isLoadingSuite]); // Added isLoadingSuite dependency

  // Define utility functions that need to be available in component scope
  const handlePause = async () => {
    if (!currentExecutionId) return;
    try {
      await pauseExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.PAUSED);
      toast({ title: "Execution Paused" });
    } catch (error) {
      toast({ title: "Error Pausing", description: (error as Error).message, variant: "destructive" });
    }
  };

  const handleResume = async () => {
    if (!currentExecutionId) return;
    try {
      await resumeExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.RUNNING);
      toast({ title: "Execution Resumed" });
    } catch (error) {
      toast({ title: "Error Resuming", description: (error as Error).message, variant: "destructive" });
    }
  };

  const handleStop = async () => {
    if (!currentExecutionId) return;
    try {
      await stopExecution(currentExecutionId);
      setCurrentExecutionStatus(ExecutionStatus.CANCELLED);
      toast({ title: "Execution Cancelled" });
    } catch (error) {
      toast({ title: "Error Cancelling", description: (error as Error).message, variant: "destructive" });
    }
  };

  // Polling logic for async suite execution
  useEffect(() => {
    if (!currentExecutionId) {
      return;
    }

    // Only poll if execution is in a non-final state
    const isNonFinalStatus = currentExecutionStatus === ExecutionStatus.RUNNING || 
                            currentExecutionStatus === ExecutionStatus.PENDING || 
                            currentExecutionStatus === ExecutionStatus.PAUSED;
    
    if (!isNonFinalStatus) {
      return;
    }

    let intervalId: NodeJS.Timeout;

    const pollExecutionStatus = async (executionId: string) => {
      try {
        const response = await getExecutionById(executionId);
        console.log("Suite execution polling response:", response);
        
        setCurrentExecutionStatus(response.status as ExecutionStatus);
        
        // Check if execution reached a final state
        const isFinalStatus = response.status === ExecutionStatus.SUCCESS || 
                             response.status === ExecutionStatus.FAILURE || 
                             response.status === ExecutionStatus.ERROR || 
                             response.status === ExecutionStatus.CANCELLED;
        
        if (isFinalStatus) {
          // Execution completed
          if (intervalId) clearInterval(intervalId);
          
          // Invalidate queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
          queryClient.invalidateQueries({ queryKey: ['suite', projectId, suiteId] });
          
          // Show completion toast
          toast({
            title: "Suite Execution Complete",
            description: response.message || `Execution finished with status: ${response.status}.`,
          });
          
          // Clear execution state after a brief delay to ensure UI shows final status
          setTimeout(() => {
            setCurrentExecutionId(null);
            setCurrentExecutionStatus(null);
          }, 1000);
        }
      } catch (err) {
        console.error("Failed to poll suite execution status:", err);
        if (intervalId) clearInterval(intervalId);
        setCurrentExecutionId(null);
        setCurrentExecutionStatus(null);
        toast({
          title: "Error Polling Execution",
          description: "Failed to get execution status.",
          variant: "destructive"
        });
      }
    };

    // Start polling immediately and then every 3-5 seconds based on execution state
    pollExecutionStatus(currentExecutionId);
    const pollInterval = currentExecutionStatus === ExecutionStatus.RUNNING ? 3000 : 5000;
    intervalId = setInterval(() => pollExecutionStatus(currentExecutionId), pollInterval);

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [currentExecutionId, currentExecutionStatus, queryClient, toast, projectId, suiteId]);

  if (isLoadingSuite) {
    return (
      <div>
        <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
        <Skeleton className="h-10 w-1/2 mb-2" /> {/* Suite Name */}
        <Skeleton className="h-5 w-3/4 mb-4" /> {/* Suite Description */}
        <Skeleton className="h-8 w-1/4 mb-6" /> {/* Tags label */}
        <div className="flex flex-wrap gap-2 mb-6">
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
        <Skeleton className="h-5 w-1/3 mb-6" /> {/* Dates */}
        <Skeleton className="h-9 w-40 mb-4" /> {/* Execute Suite button */}
        <div className="flex justify-between items-center mb-6 mt-4">
           <Skeleton className="h-8 w-1/3" /> {/* Test Cases Header */}
           <Skeleton className="h-9 w-36" /> {/* Create Test Case Button */}
        </div>
        <TestCaseListSkeleton />
      </div>
    );
  }

  if (isSuiteError) {
    return (
       <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error fetching suite</AlertTitle>
        <AlertDescription>{suiteError?.message || 'An unknown error occurred.'}</AlertDescription>
      </Alert>
    );
  }

  if (!suite) {
    return <p>Suite not found.</p>;
  }


  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Project
        </Link>
      </Button>

      <div className="flex justify-between items-start mb-4">
        <div>
          <h1 className="page-header mb-1 flex items-center gap-2">
            <ClipboardList className="h-8 w-8 text-primary" />
            {suite.name}
          </h1>
          <p className="text-muted-foreground text-sm mb-3">{suite.description}</p>
          <div className="flex flex-wrap gap-2 mb-2">
            {suite.tags.map((tag, index) => (
              <Badge key={`${suite.suite_id}-header-tag-${index}-${tag}`} variant="outline">{tag}</Badge>
            ))}
          </div>
           <p className="text-xs text-muted-foreground">
            Created: {format(new Date(suite.created_at), 'PPP p')} | Updated: {format(new Date(suite.updated_at), 'PPP p')}
          </p>
        </div>
         <div className="flex flex-col items-end gap-2">
            <div className="flex gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/projects/${projectId}/suites/${suiteId}/edit`}>
                  <Edit className="mr-2 h-4 w-4" /> Edit
                </Link>
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete the suite "{suite.name}" and all its test cases.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => deleteMutation.mutate()} disabled={deleteMutation.isPending}>
                      {deleteMutation.isPending ? "Deleting..." : "Delete Suite"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
             <div className="space-y-3 mt-2">
               {!isLoadingSuite && (
                 <CompactEnvironmentSelector
                   projectId={projectId}
                   selectedEnvId={selectedEnvironmentId}
                   onSelect={setSelectedEnvironmentId}
                   disabled={isExecuting || !!currentExecutionId}
                 />
               )}
              <div className="flex items-center gap-2">
                <div className="grid w-full max-w-[150px] items-center gap-1.5">
                  <Label htmlFor="application-version" className="text-xs">App Version</Label>
                  <Input 
                    type="text"
                    id="application-version"
                    value={applicationVersion}
                    onChange={(e) => setApplicationVersion(e.target.value)}
                    placeholder="e.g., 1.0.1"
                    className="h-8"
                    disabled={isExecuting || !!currentExecutionId}
                  />
                </div>
                <div className="grid w-full max-w-[150px] items-center gap-1.5">
                  <Label htmlFor="execution-times" className="text-xs">Execution Times</Label>
                  <Input 
                    type="number"
                    id="execution-times"
                    value={executionTimes}
                    onChange={(e) => setExecutionTimes(Math.max(1, parseInt(e.target.value, 10) || 1))}
                    min={1}
                    className="h-8 text-center"
                    disabled={isExecuting || !!currentExecutionId}
                  />
                </div>
                {!currentExecutionId ? (
                  <Button 
                    size="sm" 
                    onClick={() => executeSuite()} 
                    disabled={isExecuting || totalTests === 0}
                    className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 self-end"
                  >
                    <Play className="mr-2 h-4 w-4" /> 
                    {isExecuting ? "Starting..." : "Execute Suite"}
                  </Button>
                ) : (
                  <div className="flex flex-col items-end gap-2">
                    <div className="text-xs text-muted-foreground text-center">
                      Execution ID: {currentExecutionId}
                      <br />
                      Status: {currentExecutionStatus || 'Running'}
                    </div>
                    <div className="flex items-center gap-2">
                      {currentExecutionStatus === ExecutionStatus.RUNNING && (
                        <Button variant="outline" size="sm" onClick={handlePause}>Pause</Button>
                      )}
                      {currentExecutionStatus === ExecutionStatus.PAUSED && (
                        <Button variant="outline" size="sm" onClick={handleResume}>Resume</Button>
                      )}
                      <Button variant="destructive" size="sm" onClick={handleStop}>Cancel</Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
      </div>

      {/* Progress indicator for suite execution */}
      {currentExecutionId && (
        <div className="mb-6">
          <Alert>
            <Clock className="h-4 w-4" />
            <AlertTitle>Suite Execution {currentExecutionStatus === ExecutionStatus.PAUSED ? 'Paused' : 'in Progress'}</AlertTitle>
            <AlertDescription>
              {currentExecutionStatus === ExecutionStatus.PAUSED ? 'Execution is paused. ' : 'Executing '}
              {totalTests} test cases. 
              {currentExecutionStatus === ExecutionStatus.PAUSED ? ' Click Resume to continue.' : ' Please wait for completion.'}
              <br />
              <span className="text-xs font-mono">Execution ID: {currentExecutionId}</span>
              <br />
              <span className="text-xs">Status: {currentExecutionStatus}</span>
            </AlertDescription>
          </Alert>
        </div>
      )}

      {totalTests > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Suite Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between text-sm text-muted-foreground mb-1">
              <span>{passedTests} / {totalTests} tests passed</span>
              <span>{progress.toFixed(0)}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </CardContent>
        </Card>
      )}

      <hr className="my-6"/>

      <div className="flex justify-between items-center mb-6">
        <h2 className="section-header mb-0">Test Cases</h2>
        <Button asChild>
          <Link href={`/projects/${projectId}/suites/${suiteId}/tests/create`}>
            <PlusCircle className="mr-2 h-4 w-4" /> Create Test Case
          </Link>
        </Button>
      </div>

      {isLoadingTestCases && <TestCaseListSkeleton />}
      {isTestCasesError && (
         <Alert variant="destructive" className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error fetching test cases</AlertTitle>
            <AlertDescription>{testCasesError?.message || 'An unknown error occurred.'}</AlertDescription>
        </Alert>
      )}
      {!isLoadingTestCases && !isTestCasesError && testCases.length === 0 && (
        <Card className="text-center py-12 mt-6">
          <CardHeader>
            <TestTube2 className="mx-auto h-12 w-12 text-muted-foreground" />
            <CardTitle className="mt-4">No Test Cases Yet</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>This suite doesn't have any test cases. Create one to start testing.</CardDescription>
          </CardContent>
          <CardFooter className="justify-center">
             <Button asChild>
                <Link href={`/projects/${projectId}/suites/${suiteId}/tests/create`}>
                  <PlusCircle className="mr-2 h-4 w-4" /> Create Test Case
                </Link>
              </Button>
          </CardFooter>
        </Card>
      )}
       {!isLoadingTestCases && !isTestCasesError && testCases.length > 0 && (
         <div>
           <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
             {testCases.slice(0, visibleTestCases).map((testCase: any, index: number) => (
               <TestCaseCard key={`testcase-${testCase.id}-${index}`} testCase={testCase} projectId={projectId} suiteId={suiteId} />
             ))}
           </div>
           {testCases.length > visibleTestCases && (
             <div className="flex justify-center mt-6">
               <Button
                 variant="outline"
                 onClick={() => setVisibleTestCases(prev => prev + 9)}
               >
                 Load More Test Cases ({testCases.length - visibleTestCases} remaining)
               </Button>
             </div>
           )}
         </div>
       )}
    </div>
  );
}
