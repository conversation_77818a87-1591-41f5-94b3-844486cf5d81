import { fetchApi } from '../core/fetch';

/**
 * Test history save input interface
 */
export interface SaveTestHistoryInput {
  projectId: string;
  suiteId: string;
  name: string;
  description: string;
  gherkin: string;
  testHistory: any;
}

/**
 * Prompt update data interface
 */
export interface PromptUpdateData {
  content: Record<string, string>;
  metadata?: Record<string, any>;
  commit_message?: string;
}

/**
 * Saves test execution history to a project for future reference and analysis
 * 
 * @param input - The test history data including project context and execution results
 * @returns Promise resolving to save operation result
 * 
 * @example
 * ```typescript
 * await saveTestHistory({
 *   projectId: "proj-123",
 *   suiteId: "suite-456",
 *   name: "Login Flow Test",
 *   description: "Tests user authentication flow",
 *   gherkin: "Given I am on the login page...",
 *   testHistory: { executions: [...], results: [...] }
 * });
 * ```
 */
export async function saveTestHistory(input: SaveTestHistoryInput): Promise<any> {
  // Convert our input to match the API schema
  const apiInput = {
    project_id: input.projectId,
    suite_id: input.suiteId,
    name: input.name,
    description: input.description,
    gherkin: input.gherkin,
    test_history: input.testHistory
  };

  try {
    return await fetchApi<any>('/projects/save-history', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });
  } catch (error) {
    throw new Error(`Failed to save test history: ${(error as Error).message}`);
  }
}

/**
 * Fetches all available AI prompts organized by category
 * 
 * @returns Promise resolving to prompts data structure
 * 
 * @example
 * ```typescript
 * const prompts = await fetchPrompts();
 * console.log(prompts.categories);
 * ```
 */
export async function fetchPrompts(): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/');
  } catch (error) {
    throw new Error(`Failed to fetch prompts: ${(error as Error).message}`);
  }
}

/**
 * Fetches detailed information for a specific AI prompt
 * 
 * @param category - The prompt category (e.g., "test-generation", "code-generation")
 * @param promptId - The specific prompt identifier
 * @returns Promise resolving to prompt details
 * 
 * @example
 * ```typescript
 * const prompt = await fetchPromptDetail("test-generation", "gherkin-scenarios");
 * console.log(prompt.content, prompt.metadata);
 * ```
 */
export async function fetchPromptDetail(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`);
  } catch (error) {
    throw new Error(`Failed to fetch prompt details: ${(error as Error).message}`);
  }
}

/**
 * Updates an AI prompt with new content and metadata
 * 
 * @param category - The prompt category
 * @param promptId - The specific prompt identifier
 * @param data - The updated prompt data including content and metadata
 * @returns Promise resolving to update operation result
 * 
 * @example
 * ```typescript
 * await updatePrompt("test-generation", "gherkin-scenarios", {
 *   content: {
 *     system: "You are a test generation expert...",
 *     user: "Generate Gherkin scenarios for: {user_story}"
 *   },
 *   metadata: { version: "2.0", author: "QAK Team" },
 *   commit_message: "Updated Gherkin generation prompt for better accuracy"
 * });
 * ```
 */
export async function updatePrompt(
  category: string,
  promptId: string,
  data: PromptUpdateData
): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  } catch (error) {
    throw new Error(`Failed to update prompt: ${(error as Error).message}`);
  }
}

/**
 * Creates a new AI prompt
 *
 * @param data - The prompt creation data
 * @returns Promise resolving to created prompt
 */
export async function createPrompt(data: {
  category: string;
  prompt_id: string;
  name: string;
  description: string;
  languages?: string[];
  content: Record<string, string>;
  metadata?: Record<string, any>;
}): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  } catch (error) {
    throw new Error(`Failed to create prompt: ${(error as Error).message}`);
  }
}

/**
 * Deletes an AI prompt
 *
 * @param category - The prompt category
 * @param promptId - The specific prompt identifier
 * @returns Promise resolving to delete operation result
 */
export async function deletePrompt(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}`, {
      method: 'DELETE',
    });
  } catch (error) {
    throw new Error(`Failed to delete prompt: ${(error as Error).message}`);
  }
}

/**
 * Validates an AI prompt configuration
 *
 * @param category - The prompt category
 * @param promptId - The specific prompt identifier
 * @returns Promise resolving to validation result
 */
export async function validatePrompt(category: string, promptId: string): Promise<any> {
  try {
    return await fetchApi<any>(`/prompts/${category}/${promptId}/validate`, {
      method: 'POST',
    });
  } catch (error) {
    throw new Error(`Failed to validate prompt: ${(error as Error).message}`);
  }
}

/**
 * Validates all AI prompts in the system
 *
 * @returns Promise resolving to validation results
 */
export async function validateAllPrompts(): Promise<any> {
  try {
    return await fetchApi<any>('/prompts/validate-all', {
      method: 'POST',
    });
  } catch (error) {
    throw new Error(`Failed to validate all prompts: ${(error as Error).message}`);
  }
}