'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthService, User, LoginCredentials } from '@/lib/auth';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is authenticated on mount
    const initAuth = async () => {
      try {
        console.log('🔐 AuthContext: Initializing authentication...');
        const isAuth = AuthService.isAuthenticated();
        console.log('🔐 AuthContext: Is authenticated?', isAuth);
        
        if (isAuth) {
          const storedUser = AuthService.getUser();
          console.log('🔐 AuthContext: Stored user:', storedUser);
          if (storedUser) {
            setUser(storedUser);
          }
        } else {
          console.log('🔐 AuthContext: Not authenticated, clearing any stale data');
          AuthService.logout();
        }
      } catch (error) {
        console.error('🔐 AuthContext: Auth initialization error:', error);
        AuthService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      console.log('🔐 AuthContext: Starting login process...');
      
      const response = await AuthService.login(credentials);
      console.log('🔐 AuthContext: Login response:', response);
      
      if (response.success && response.data) {
        // Check if email verification is required
        if (response.data.requiresEmailVerification) {
          console.log('🔐 AuthContext: Email verification required, attempting dev verification...');
          
          // Try to auto-verify for development
          const devVerified = await AuthService.devVerifyEmail(credentials.email);
          
          if (devVerified) {
            console.log('🔐 AuthContext: Email auto-verified for development, retrying login...');
            // Retry login after verification
            const retryResponse = await AuthService.login(credentials);
            
            if (retryResponse.success && retryResponse.data && !retryResponse.data.requiresEmailVerification) {
              const mappedUser = AuthService.getUser();
              console.log('🔐 AuthContext: Retry successful, setting user:', mappedUser);
              setUser(mappedUser);
              return { success: true };
            }
          }
          
          return { 
            success: false, 
            error: 'Email verification required. Please check your email and verify your account before logging in.' 
          };
        }
        
        const mappedUser = AuthService.getUser(); // Get the mapped user from AuthService
        console.log('🔐 AuthContext: Setting user:', mappedUser);
        setUser(mappedUser);
        return { success: true };
      } else {
        console.log('🔐 AuthContext: Login failed:', response.errorMessage);
        return { 
          success: false, 
          error: response.errorMessage || 'Login failed' 
        };
      }
    } catch (error) {
      console.error('🔐 AuthContext: Login error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    AuthService.logout();
    setUser(null);
    // Redirect to login page
    window.location.href = '/login';
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user && AuthService.isAuthenticated(),
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}