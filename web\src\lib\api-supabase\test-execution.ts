import { BaseApiHandler } from './base'
import type { Database } from '@/lib/supabase/database.types'

// Types for test execution
export interface TestExecutionRequest {
  type: 'smoke' | 'full' | 'case' | 'suite'
  url?: string
  instructions?: string
  test_id?: string
  suite_id?: string
  project_id?: string
  config_profile?: string
  max_steps?: number
}

export interface TestExecutionResponse {
  execution_id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  job_id?: string
  results?: any
  started_at: string
  completed_at?: string
}

type TestExecution = Database['public']['Tables']['test_executions']['Row']
type TestExecutionInsert = Database['public']['Tables']['test_executions']['Insert']

export class TestExecutionApiHandler extends BaseApiHandler {
  private readonly PYTHON_API_BASE = process.env.NODE_ENV === 'production'
    ? 'http://browser-automation-api:8080'  // Docker internal hostname
    : 'http://localhost:8080'               // Local development

  async executeTest(request: TestExecutionRequest): Promise<TestExecutionResponse> {
    await this.initialize()

    try {
      // 1. Create execution record in database
      const executionRecord: TestExecutionInsert = {
        user_id: this.user.id,
        project_id: request.project_id || null,
        test_type: request.type,
        status: 'pending',
        config: request as any,
        started_at: new Date().toISOString()
      }

      const { data: execution, error: executionError } = await this.supabase
        .from('test_executions')
        .insert([executionRecord])
        .select()
        .single()

      if (executionError) {
        console.error('Failed to create execution record:', executionError)
        throw new Error('Failed to create execution record')
      }

      // 2. Update status to running
      await this.supabase
        .from('test_executions')
        .update({ status: 'running' })
        .eq('execution_id', execution.execution_id)

      // 3. Call Python API asynchronously
      this.executeTestAsync(execution.execution_id, request)
        .catch(error => {
          console.error('Async execution failed:', error)
          // Update execution status to failed
          this.supabase
            .from('test_executions')
            .update({
              status: 'failed',
              completed_at: new Date().toISOString(),
              results: { error: error.message }
            })
            .eq('execution_id', execution.execution_id)
            .then(() => console.log('Updated execution status to failed'))
        })

      return {
        execution_id: execution.execution_id,
        status: 'running',
        started_at: execution.started_at!
      }

    } catch (error) {
      console.error('Test execution error:', error)
      throw new Error(`Failed to execute test: ${(error as Error).message}`)
    }
  }

  private async executeTestAsync(executionId: string, request: TestExecutionRequest) {
    try {
      console.log(`🚀 Starting async execution ${executionId} with Python API`)

      // Map request to Python API format
      const pythonRequest = this.mapToPythonApiFormat(request)

      // Call Python API
      const response = await fetch(`${this.PYTHON_API_BASE}/api/execute-test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pythonRequest)
      })

      if (!response.ok) {
        throw new Error(`Python API returned ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log(`✅ Python API response for ${executionId}:`, result)

      // Update execution with results
      await this.supabase
        .from('test_executions')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          results: result
        })
        .eq('execution_id', executionId)

      console.log(`✅ Execution ${executionId} completed successfully`)

    } catch (error) {
      console.error(`❌ Async execution ${executionId} failed:`, error)

      // Update execution status to failed
      await this.supabase
        .from('test_executions')
        .update({
          status: 'failed',
          completed_at: new Date().toISOString(),
          results: { error: (error as Error).message }
        })
        .eq('execution_id', executionId)
    }
  }

  private mapToPythonApiFormat(request: TestExecutionRequest) {
    // Map Next.js request format to Python API format
    return {
      url: request.url,
      instructions: request.instructions || '',
      test_type: request.type,
      max_steps: request.max_steps || 10,
      config: {
        profile: request.config_profile || 'default'
      }
    }
  }

  async getExecutionStatus(executionId: string): Promise<TestExecutionResponse> {
    await this.initialize()

    const { data: execution, error } = await this.supabase
      .from('test_executions')
      .select('*')
      .eq('execution_id', executionId)
      .eq('user_id', this.user.id)
      .single()

    if (error || !execution) {
      throw new Error('Execution not found')
    }

    return {
      execution_id: execution.execution_id,
      status: execution.status as any,
      results: execution.results,
      started_at: execution.started_at!,
      completed_at: execution.completed_at || undefined
    }
  }

  async getExecutionResults(executionId: string) {
    await this.initialize()

    const { data: execution, error } = await this.supabase
      .from('test_executions')
      .select(`
        *,
        execution_steps (*)
      `)
      .eq('execution_id', executionId)
      .eq('user_id', this.user.id)
      .single()

    if (error || !execution) {
      throw new Error('Execution not found')
    }

    return execution
  }

  async getUserExecutions() {
    await this.initialize()

    const { data: executions, error } = await this.supabase
      .from('test_executions')
      .select(`
        *,
        projects (name)
      `)
      .eq('user_id', this.user.id)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error('Failed to fetch executions')
    }

    return executions || []
  }
}