import { BaseApiHandler } from './base'
import type { Database } from '@/lib/supabase/database.types'

type Project = Database['public']['Tables']['projects']['Row']
type ProjectInsert = Database['public']['Tables']['projects']['Insert']
type ProjectUpdate = Database['public']['Tables']['projects']['Update']

export class ProjectsApiHandler extends BaseApiHandler {
  private readonly projectSelect = `
    *,
    test_suites (
      suite_id,
      name,
      description,
      type,
      tags,
      created_at,
      updated_at,
      execution_times
    ),
    environments!environments_project_id_fkey (
      env_id,
      name,
      description,
      base_url,
      is_default,
      config_overrides,
      headless,
      wait_time,
      timeout,
      viewport_width,
      viewport_height,
      user_agent,
      tags,
      variables,
      created_at,
      updated_at
    )
  `

  async getProjects() {
    console.log('🔍 getProjects called, user:', this.user)

    const { data: projects, error } = await this.supabase
      .from('projects')
      .select('*')
      .eq('user_id', this.user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Projects query error:', error)
      throw new Error(`Failed to fetch projects: ${error.message}`)
    }

    console.log('✅ Projects query successful:', projects)
    return { success: true, count: (projects || []).length, items: projects || [] }
  }

  async getProject(projectId: string) {
    const { data: project, error } = await this.supabase
      .from('projects')
      .select(this.projectSelect)
      .eq('project_id', projectId)
      .eq('user_id', this.user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Project not found')
      }
      throw new Error('Failed to fetch project')
    }

    return { success: true, data: project }
  }

  async createProject(data: { name: string; description: string; tags?: string[] }) {
    const { name, description, tags = [] } = data

    if (!name || !description) {
      throw new Error('Name and description are required')
    }

    const projectData: ProjectInsert = {
      name,
      description,
      tags,
      user_id: this.user.id
    }

    const { data: project, error } = await this.supabase
      .from('projects')
      .insert(projectData)
      .select(this.projectSelect)
      .single()

    if (error) {
      console.error('Supabase error creating project:', error)
      throw new Error(`Failed to create project: ${error.message}`)
    }

    return { success: true, data: project }
  }

  async updateProject(
    projectId: string, 
    data: { name?: string; description?: string; tags?: string[]; default_environment_id?: string; github_config?: any }
  ) {
    if (!(await this.verifyProjectOwnership(projectId))) {
      throw new Error('Project not found')
    }

    const updateData: ProjectUpdate = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.tags !== undefined) updateData.tags = data.tags
    if (data.default_environment_id !== undefined) updateData.default_environment_id = data.default_environment_id
    if (data.github_config !== undefined) updateData.github_config = data.github_config

    const { data: project, error } = await this.supabase
      .from('projects')
      .update(updateData)
      .eq('project_id', projectId)
      .eq('user_id', this.user.id)
      .select(this.projectSelect)
      .single()

    if (error) {
      throw new Error('Failed to update project')
    }

    return { success: true, data: project }
  }

  async deleteProject(projectId: string) {
    if (!(await this.verifyProjectOwnership(projectId))) {
      throw new Error('Project not found')
    }

    const { error } = await this.supabase
      .from('projects')
      .delete()
      .eq('project_id', projectId)
      .eq('user_id', this.user.id)

    if (error) {
      throw new Error('Failed to delete project')
    }

    return { success: true, data: { message: 'Project deleted successfully' } }
  }
}