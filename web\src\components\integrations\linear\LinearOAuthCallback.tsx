"use client";

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircleIcon, XCircleIcon, RefreshCwIcon } from 'lucide-react';
import { toast } from 'sonner';

interface OAuthCallbackProps {
  tenantId: string;
}

export function LinearOAuthCallback({ tenantId }: OAuthCallbackProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [workspace, setWorkspace] = useState<any>(null);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const handleOAuthCallback = async () => {
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');

      if (error) {
        setStatus('error');
        setError(error);
        return;
      }

      if (!code || !state) {
        setStatus('error');
        setError('Missing authorization code or state parameter');
        return;
      }

      try {
        const redirectUri = `${window.location.origin}/integrations/linear/callback`;
        
        const response = await fetch('/api/v1/linear/auth/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'X-Tenant-ID': tenantId
          },
          body: JSON.stringify({
            code,
            state,
            redirectUri,
            tenantId
          })
        });

        if (response.ok) {
          const workspaceData = await response.json();
          setWorkspace(workspaceData);
          setStatus('success');
          toast.success(`Successfully connected to ${workspaceData.workspaceName}`);
        } else {
          const errorData = await response.json();
          setStatus('error');
          setError(errorData.error || 'Failed to connect workspace');
          toast.error('Failed to connect Linear workspace');
        }
      } catch (err) {
        console.error('OAuth callback error:', err);
        setStatus('error');
        setError('An unexpected error occurred');
        toast.error('An unexpected error occurred');
      }
    };

    handleOAuthCallback();
  }, [searchParams, tenantId]);

  const handleReturnToIntegrations = () => {
    router.push('/integrations');
  };

  const handleRetry = () => {
    router.push('/integrations/linear');
  };

  if (status === 'processing') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              <RefreshCwIcon className="w-12 h-12 animate-spin text-primary" />
            </div>
            <CardTitle>Connecting Linear Workspace</CardTitle>
            <CardDescription>
              Please wait while we establish the connection...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              <XCircleIcon className="w-12 h-12 text-destructive" />
            </div>
            <CardTitle>Connection Failed</CardTitle>
            <CardDescription>
              We encountered an error while connecting your Linear workspace
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-destructive/10 rounded-lg">
              <p className="text-sm text-destructive font-medium">Error Details:</p>
              <p className="text-sm text-muted-foreground mt-1">{error}</p>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleRetry} className="flex-1">
                Try Again
              </Button>
              <Button onClick={handleReturnToIntegrations} className="flex-1">
                Return to Integrations
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Success state
  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4">
            <CheckCircleIcon className="w-12 h-12 text-green-500" />
          </div>
          <CardTitle>Workspace Connected Successfully!</CardTitle>
          <CardDescription>
            Your Linear workspace has been connected and is ready for synchronization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {workspace && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="font-medium text-green-900">{workspace.workspaceName}</h4>
              <p className="text-sm text-green-700">{workspace.workspaceUrlKey}.linear.app</p>
              <div className="mt-2 text-xs text-green-600">
                Connected on {new Date(workspace.createdAt).toLocaleDateString()}
              </div>
            </div>
          )}
          
          <div className="space-y-2">
            <h5 className="font-medium text-sm">What happens next?</h5>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Issues and user stories will be synchronized automatically</li>
              <li>• Documents will be indexed for AI-powered search</li>
              <li>• You can configure sync settings anytime</li>
            </ul>
          </div>

          <Button onClick={handleReturnToIntegrations} className="w-full">
            Go to Integrations
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}