"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { X, Plus, FileText } from "lucide-react";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FileUpload } from "@/components/ui/file-upload";

interface TestCase {
  _id?: string;
  name: string;
  description?: string;
  target_url?: string;
  tags?: string[];
  priority?: string;
  type?: string;
  instructions?: string;
  user_story?: string;
}

const simplifiedTestCaseSchema = z.object({
  name: z.string().min(2, { message: "Test case name must be at least 2 characters." }),
  description: z.string().optional(),
  url: z.string().url({ message: "Please enter a valid URL." }).optional().or(z.literal('')),
  tags: z.array(z.string()).optional(),
  // Content - only two fields needed: instructions and user_story (removed gherkin)
  content: z.string().min(10, { message: "Test content must be at least 10 characters." }),
  content_type: z.enum(["instructions", "user_story"]).default("instructions"),
  file_attachments: z.array(z.instanceof(File)).optional(),
});

type SimplifiedTestCaseFormData = z.infer<typeof simplifiedTestCaseSchema>;

interface SimplifiedTestCaseFormProps {
  testCase?: TestCase;
  onSubmit: (data: any) => Promise<void>;
}

export function SimplifiedTestCaseForm({ testCase, onSubmit }: SimplifiedTestCaseFormProps) {
  const [currentTag, setCurrentTag] = useState("");
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  const form = useForm<SimplifiedTestCaseFormData>({
    resolver: zodResolver(simplifiedTestCaseSchema),
    defaultValues: {
      name: testCase?.name || "",
      description: testCase?.description || "",
      url: testCase?.target_url || "",
      tags: testCase?.tags || [],
      content: testCase?.instructions || testCase?.user_story || "",
      content_type: testCase?.instructions ? "instructions" : 
                  testCase?.user_story ? "user_story" : "instructions",
      file_attachments: [],
    },
  });

  const handleAddTag = () => {
    if (currentTag.trim() !== "") {
      const currentTags = form.getValues("tags") || [];
      if (!currentTags.includes(currentTag.trim())) {
        form.setValue("tags", [...currentTags, currentTag.trim()]);
      }
      setCurrentTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter((tag: string) => tag !== tagToRemove));
  };

  const handleFilesChange = (files: File[]) => {
    setAttachedFiles(files);
    form.setValue("file_attachments", files);
  };

  const handleSubmit = async (data: SimplifiedTestCaseFormData) => {
    // Transform simplified data to match API expectations
    const transformedData = {
      name: data.name,
      description: data.description,
      url: data.url,
      tags: data.tags,
      // Set default values for removed fields
      priority: "Medium" as const,
      type: "Functional" as const,
      // Map content to appropriate field based on content_type
      instructions: data.content_type === "instructions" ? data.content : "",
      user_story: data.content_type === "user_story" ? data.content : "",
      ...(attachedFiles.length > 0 && { file_attachments: attachedFiles }),
    };

    await onSubmit(transformedData);
  };

  const contentType = form.watch("content_type");

  const getContentPlaceholder = () => {
    switch (contentType) {
      case "instructions":
        return "1. Navigate to the login page\n2. Enter valid credentials\n3. Click submit\n4. Verify successful login";
      case "user_story":
        return "As a user, I want to log into the application so that I can access my account and personal information.";
      default:
        return "";
    }
  };

  const getContentDescription = () => {
    switch (contentType) {
      case "instructions":
        return "Step-by-step instructions for executing this test manually or with automation.";
      case "user_story":
        return "User story or requirement that this test case validates.";
      default:
        return "";
    }
  };

  const getContentTitle = () => {
    switch (contentType) {
      case "instructions":
        return "Test Instructions";
      case "user_story":
        return "User Story";
      default:
        return "Content";
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Create Test Case</CardTitle>
          <CardDescription>
            Create a simplified test case with essential information only.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              {/* Test Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Test Case Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., User Login Test" {...field} />
                    </FormControl>
                    <FormDescription>
                      A clear, descriptive name for your test case.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Brief description of what this test validates..."
                        className="min-h-[80px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description explaining the purpose of this test.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Target URL */}
              <FormField
                control={form.control}
                name="url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target URL</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="https://example.com" 
                        type="url"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      The URL where this test will be executed.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tags */}
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <Input
                            placeholder="Add a tag..."
                            value={currentTag}
                            onChange={(e) => setCurrentTag(e.target.value)}
                            onKeyPress={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault();
                                handleAddTag();
                              }
                            }}
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            onClick={handleAddTag}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        {field.value && field.value.length > 0 && (
                          <div className="flex flex-wrap gap-2">
                            {field.value.map((tag: string, index: number) => (
                              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                {tag}
                                <X
                                  className="h-3 w-3 cursor-pointer"
                                  onClick={() => handleRemoveTag(tag)}
                                />
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormDescription>
                      Add tags to categorize and organize your tests.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Content Type and Content */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="content_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{getContentTitle()} *</FormLabel>
                      <FormControl>
                        <Tabs value={field.value} onValueChange={field.onChange}>
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="instructions">Instructions</TabsTrigger>
                            <TabsTrigger value="user_story">User Story</TabsTrigger>
                          </TabsList>
                          
                          <TabsContent value="instructions" className="mt-4">
                            <FormField
                              control={form.control}
                              name="content"
                              render={({ field: contentField }) => (
                                <FormItem>
                                  <FormControl>
                                    <Textarea
                                      placeholder={getContentPlaceholder()}
                                      className="min-h-[120px]"
                                      {...contentField}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    {getContentDescription()}
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </TabsContent>
                          
                          <TabsContent value="user_story" className="mt-4">
                            <FormField
                              control={form.control}
                              name="content"
                              render={({ field: contentField }) => (
                                <FormItem>
                                  <FormControl>
                                    <Textarea
                                      placeholder={getContentPlaceholder()}
                                      className="min-h-[120px]"
                                      {...contentField}
                                    />
                                  </FormControl>
                                  <FormDescription>
                                    {getContentDescription()}
                                  </FormDescription>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </TabsContent>
                        </Tabs>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* File Attachments */}
              <FormField
                control={form.control}
                name="file_attachments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>File Attachments</FormLabel>
                    <FormControl>
                      <FileUpload
                        onFilesChange={handleFilesChange}
                        maxFiles={5}
                        maxSize={10 * 1024 * 1024} // 10MB
                        acceptedFileTypes={[".pdf", ".doc", ".docx", ".txt", ".png", ".jpg", ".jpeg"]}
                      />
                    </FormControl>
                    <FormDescription>
                      Upload supporting files for your test case (max 5 files, 10MB each).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full">
                {testCase ? "Update Test Case" : "Create Test Case"}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}