"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, FileText, GripVertical } from "lucide-react";
import { cn } from "@/lib/utils";

interface TestStep {
  id: string;
  text: string;
}

interface TestStepsEditorProps {
  steps: TestStep[];
  onChange: (steps: TestStep[]) => void;
  className?: string;
}

export function TestStepsEditor({ steps, onChange, className }: TestStepsEditorProps) {
  const [newStepText, setNewStepText] = useState("");

  const addStep = () => {
    if (newStepText.trim()) {
      const newStep: TestStep = {
        id: `step_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        text: newStepText.trim()
      };
      onChange([...steps, newStep]);
      setNewStepText("");
    }
  };

  const removeStep = (stepId: string) => {
    onChange(steps.filter(step => step.id !== stepId));
  };

  const updateStep = (stepId: string, newText: string) => {
    onChange(steps.map(step => 
      step.id === stepId ? { ...step, text: newText } : step
    ));
  };

  const moveStep = (fromIndex: number, toIndex: number) => {
    const newSteps = [...steps];
    const [movedStep] = newSteps.splice(fromIndex, 1);
    newSteps.splice(toIndex, 0, movedStep);
    onChange(newSteps);
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Test Steps
          </h3>
          <p className="text-sm text-muted-foreground">
            Define the actions to perform during testing
          </p>
        </div>
        <Button 
          type="button" 
          variant="outline" 
          onClick={addStep}
          disabled={!newStepText.trim()}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Step
        </Button>
      </div>

      {/* Steps List */}
      {steps.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">No steps added yet</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              Click "Add Step" to get started
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {steps.map((step, index) => (
            <Card key={step.id} className="relative">
              <CardContent className="flex items-center gap-3 p-4">
                {/* Step Number */}
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-medium text-sm">
                    {index + 1}
                  </div>
                  <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />
                </div>

                {/* Step Content */}
                <div className="flex-1">
                  <Input
                    value={step.text}
                    onChange={(e) => updateStep(step.id, e.target.value)}
                    placeholder="Describe the action to perform..."
                    className="border-none shadow-none p-0 h-auto font-medium text-base focus-visible:ring-0"
                  />
                </div>

                {/* Remove Button */}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeStep(step.id)}
                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                >
                  Remove
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add New Step Input */}
      <Card className="border-dashed border-primary/20">
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium text-sm">
              {steps.length + 1}
            </div>
            <Input
              placeholder="Type your next step and press Enter or click Add Step..."
              value={newStepText}
              onChange={(e) => setNewStepText(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addStep();
                }
              }}
              className="flex-1"
            />
            <Button 
              type="button" 
              onClick={addStep}
              disabled={!newStepText.trim()}
              size="sm"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Steps Summary */}
      {steps.length > 0 && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Badge variant="outline">{steps.length} step{steps.length !== 1 ? 's' : ''}</Badge>
          <span>•</span>
          <span>Steps will be executed in order</span>
        </div>
      )}
    </div>
  );
}

export default TestStepsEditor;