/**
 * Result Processor
 * Converts raw aery-browser JSON results into human-readable messages for QA engineers
 */

export interface ProcessedResult {
  success: boolean;
  humanMessage: string;
  originalResult: any;
  screenshots?: string[];
  metadata?: any;
}

/**
 * Processes raw result array into human-readable format
 */
export function processTestResult(result: any[]): ProcessedResult {
  if (!Array.isArray(result) || result.length === 0) {
    return {
      success: false,
      humanMessage: "No se recibieron resultados del test",
      originalResult: result
    };
  }

  let success = false;
  let humanMessage = "";
  let screenshots: string[] = [];
  let metadata: any = {};

  // Process each result item
  for (const item of result) {
    // Skip todo.md file operations that should not be displayed as test results
    if (typeof item === 'string' && (
      item.includes('todo.md') || 
      item.includes('Buffered update') || 
      item.includes('Skipped unchanged write')
    )) {
      continue;
    }
    
    if (typeof item === 'string') {
      // Parse if it's a string representation of the result
      const processedItem = parseResultString(item);
      if (processedItem.success !== undefined) {
        success = processedItem.success;
      }
      if (processedItem.message) {
        humanMessage = processedItem.message;
      }
      if (processedItem.screenshots) {
        screenshots.push(...processedItem.screenshots);
      }
      if (processedItem.metadata) {
        metadata = { ...metadata, ...processedItem.metadata };
      }
    } else if (typeof item === 'object' && item !== null) {
      // Skip todo.md file operations in object format
      if (item.extracted_content && (
        item.extracted_content.includes('todo.md') || 
        item.extracted_content.includes('Buffered update') || 
        item.extracted_content.includes('Skipped unchanged write')
      )) {
        continue;
      }
      if (item.long_term_memory && (
        item.long_term_memory.includes('todo.md') || 
        item.long_term_memory.includes('Buffered update') || 
        item.long_term_memory.includes('Skipped unchanged write')
      )) {
        continue;
      }
      
      // Handle object format
      if (item.success !== undefined) {
        success = item.success;
      }
      if (item.extracted_content) {
        humanMessage = item.extracted_content;
      } else if (item.long_term_memory) {
        humanMessage = item.long_term_memory;
      }
      if (item.attachments && Array.isArray(item.attachments)) {
        screenshots.push(...item.attachments);
      }
    }
  }

  // Generate human-readable message if none found
  if (!humanMessage) {
    humanMessage = success 
      ? "✅ Test completado exitosamente" 
      : "❌ Test completado con errores";
  }

  return {
    success,
    humanMessage,
    originalResult: result,
    screenshots: screenshots.length > 0 ? screenshots : undefined,
    metadata: Object.keys(metadata).length > 0 ? metadata : undefined
  };
}

/**
 * Parses a string representation of result data
 */
function parseResultString(resultStr: string): {
  success?: boolean;
  message?: string;
  screenshots?: string[];
  metadata?: any;
} {
  // Handle format: "is_done=True success=True error=None attachments=[] long_term_memory='...' extracted_content='...'"
  const patterns = {
    success: /success=(\w+)/i,
    is_done: /is_done=(\w+)/i,
    error: /error=([^=]*?)(?:\s+\w+=|$)/,
    long_term_memory: /long_term_memory=['"](.*?)['"]/,
    extracted_content: /extracted_content=['"](.*?)['"]/,
    attachments: /attachments=\[(.*?)\]/
  };

  const matches: any = {};
  Object.entries(patterns).forEach(([key, pattern]) => {
    const match = resultStr.match(pattern);
    if (match) {
      matches[key] = match[1];
    }
  });

  // Parse success
  let success: boolean | undefined = undefined;
  if (matches.success) {
    success = matches.success.toLowerCase() === 'true';
  } else if (matches.is_done) {
    success = matches.is_done.toLowerCase() === 'true';
  }

  // Get message
  let message = '';
  if (matches.extracted_content && matches.extracted_content !== 'None') {
    message = matches.extracted_content;
  } else if (matches.long_term_memory && matches.long_term_memory !== 'None') {
    message = matches.long_term_memory;
  }

  // Clean up message
  message = message.replace(/^Task completed:\s*(True|False)\s*-\s*/, '');
  message = message.replace(/^\w+\s*-\s*/, ''); // Remove prefixes like "True - "

  // Parse attachments/screenshots
  let screenshots: string[] = [];
  if (matches.attachments && matches.attachments !== '') {
    try {
      // Simple parsing for now - could be enhanced
      const attachmentMatches = matches.attachments.match(/['"](.*?)['"]/g);
      if (attachmentMatches) {
        screenshots = attachmentMatches.map((match: string) => match.replace(/['"]/g, ''));
      }
    } catch (e) {
      console.debug('Could not parse attachments:', e);
    }
  }

  return { success, message, screenshots, metadata: {} };
}

/**
 * Converts aery-browser action results to human-readable descriptions
 */
export function processActionResult(action: any): string {
  if (!action || typeof action !== 'object') {
    return "Acción desconocida";
  }

  const actionType = action.action_type || action.type || 'unknown';
  
  switch (actionType.toLowerCase()) {
    case 'click':
    case 'click_element':
      return `🖱️ Click en elemento${action.description ? ': ' + action.description : ''}`;
    
    case 'type':
    case 'input_text':
      const text = action.text || action.value || '';
      const truncatedText = text.length > 30 ? text.substring(0, 30) + '...' : text;
      return `⌨️ Escribir texto: "${truncatedText}"`;
    
    case 'navigate':
    case 'go_to_url':
      return `🌐 Navegar a: ${action.url || 'URL'}`;
    
    case 'scroll':
      return `📜 Hacer scroll en la página`;
    
    case 'wait':
      return `⏳ Esperar ${action.duration || 'un momento'}`;
    
    case 'screenshot':
    case 'take_screenshot':
      return `📸 Capturar screenshot`;
    
    case 'done':
      return `✅ Completar test`;
    
    case 'upload_file':
    case 'upload_files':
      return `📁 Subir archivo(s)`;
    
    case 'select':
    case 'select_option':
      return `📋 Seleccionar opción`;
    
    default:
      return `🔧 ${actionType}${action.description ? ': ' + action.description : ''}`;
  }
}

/**
 * Formats step metadata into human-readable information
 */
export function formatStepMetadata(metadata: any): string[] {
  const info: string[] = [];
  
  if (!metadata || typeof metadata !== 'object') {
    return info;
  }

  // Add thinking if available
  if (metadata.thinking) {
    info.push(`💭 Razonamiento: ${metadata.thinking}`);
  }

  // Add URL if available
  if (metadata.state?.url) {
    info.push(`🌐 URL: ${metadata.state.url}`);
  }

  // Add action details
  if (metadata.actions && Array.isArray(metadata.actions)) {
    const actionDescriptions = metadata.actions.map(processActionResult);
    info.push(`🎯 Acciones: ${actionDescriptions.join(', ')}`);
  }

  return info;
} 