import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContextSupabase';
import { PermissionService, UserPermissions, UserRole } from '@/lib/permissions';

/**
 * Hook personalizado para manejar permisos de usuario
 * Proporciona una interfaz fácil de usar para verificar permisos en componentes
 */
export const usePermissions = (): UserPermissions => {
  const { user } = useAuth();

  return useMemo(() => {
    if (!user) {
      // Usuario no autenticado - permisos mínimos
      return {
        isSuperAdmin: false,
        canManageTenants: false,
        canManageGlobalUsers: false,
        canAccessGlobalAdmin: false,
        isTenantAdmin: false,
        canManageOwnTenant: () => false,
        canManageUsersInOwnTenant: () => false,
        isTenantUser: false,
        canExecuteTests: false,
        canCreateTests: false,
        canUseQAKI: false,
        hasPermission: () => false
      };
    }

    // Obtener permisos basados en el rol del usuario
    return PermissionService.getUserPermissions(user.role, user.tenantId);
  }, [user?.role, user?.tenantId]);
};

/**
 * Hook específico para verificar permisos de tenant
 */
export const useTenantPermissions = (tenantId?: string) => {
  const { user } = useAuth();
  const permissions = usePermissions();

  return useMemo(() => {
    if (!user || !tenantId) {
      return {
        canManageThisTenant: false,
        canManageUsersInThisTenant: false,
        isMemberOfThisTenant: false,
        isOwnerOfThisTenant: false,
        isAdminOfThisTenant: false
      };
    }

    return {
      canManageThisTenant: permissions.canManageOwnTenant(tenantId),
      canManageUsersInThisTenant: permissions.canManageUsersInOwnTenant(tenantId),
      isMemberOfThisTenant: user.tenantId === tenantId,
      isOwnerOfThisTenant: user.tenantId === tenantId && user.role === UserRole.USER,
      isAdminOfThisTenant: user.tenantId === tenantId && user.role === UserRole.ADMIN
    };
  }, [user?.role, user?.tenantId, tenantId, permissions.canManageOwnTenant, permissions.canManageUsersInOwnTenant]);
};

/**
 * Hook para verificar permisos específicos
 */
export const useHasPermission = (permission: string, tenantId?: string) => {
  const permissions = usePermissions();

  return useMemo(() => {
    return permissions.hasPermission(permission, tenantId);
  }, [permissions, permission, tenantId]);
};

/**
 * Hook para navegación condicional basada en permisos
 */
export const useNavigationPermissions = () => {
  const permissions = usePermissions();

  return useMemo(() => {
    return {
      // Items de navegación para SuperAdmin
      superAdminItems: permissions.canAccessGlobalAdmin ? [
        { href: '/admin', label: 'Admin Dashboard' },
        { href: '/admin/users', label: 'Users' },
        { href: '/admin/tenants', label: 'Tenants' }
      ] : [],

      // Items de navegación para Admin de tenant
      tenantAdminItems: permissions.isTenantAdmin ? [
        { href: '/tenant/settings', label: 'Tenant Settings' },
        { href: '/tenant/users', label: 'Tenant Users' }
      ] : [],

      // Items de navegación para usuarios regulares
      userItems: permissions.isTenantUser ? [
        { href: '/projects', label: 'Projects' },
        { href: '/qaki', label: 'QAKI Assistant' }
      ] : [],

      // Items comunes para todos los usuarios autenticados
      commonItems: [
        { href: '/', label: 'Dashboard' },
        { href: '/settings', label: 'Settings' }
      ]
    };
  }, [permissions]);
};