import { NextRequest } from 'next/server'
import { TestCasesApiHandler } from '@/lib/api-supabase/test-cases'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string; testCaseId: string }> }
) {
  const handler = new TestCasesApiHandler()
  return handler.handleRequest(async () => {
    const resolvedParams = await params
    return handler.getTestCase(resolvedParams.projectId, resolvedParams.suiteId, resolvedParams.testCaseId)
  })
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string; testCaseId: string }> }
) {
  const handler = new TestCasesApiHandler()
  return handler.handleRequest(async () => {
    const body = await request.json()
    const resolvedParams = await params
    return handler.updateTestCase(resolvedParams.projectId, resolvedParams.suiteId, resolvedParams.testCaseId, body)
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string; testCaseId: string }> }
) {
  const handler = new TestCasesApiHandler()
  return handler.handleRequest(async () => {
    const resolvedParams = await params
    return handler.deleteTestCase(resolvedParams.projectId, resolvedParams.suiteId, resolvedParams.testCaseId)
  })
}