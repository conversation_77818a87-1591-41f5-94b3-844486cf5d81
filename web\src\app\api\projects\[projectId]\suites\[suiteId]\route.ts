import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { SupabaseAuthServerService } from '@/lib/auth-supabase-server'
import type { Database } from '@/lib/supabase/database.types'

type TestSuite = Database['public']['Tables']['test_suites']['Row']
type TestSuiteUpdate = Database['public']['Tables']['test_suites']['Update']

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string }> }
) {
  try {
    const user = await SupabaseAuthServerService.getCurrentUserServer()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { projectId, suiteId } = resolvedParams
    const supabase = await createClient()
    
    // Verify the project belongs to the user and get the suite
    const { data: suite, error } = await supabase
      .from('test_suites')
      .select(`
        *,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Test suite not found' }, { status: 404 })
      }
      console.error('Error fetching test suite:', error)
      return NextResponse.json({ error: 'Failed to fetch test suite' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: suite
    })
  } catch (error) {
    console.error('Error in GET /api/projects/[projectId]/suites/[suiteId]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string }> }
) {
  try {
    const user = await SupabaseAuthServerService.getCurrentUserServer()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { projectId, suiteId } = resolvedParams
    const body = await request.json()
    const { name, description, type, tags, execution_times } = body

    const supabase = await createClient()
    
    // First verify the suite exists and belongs to user's project
    const { data: existingSuite, error: checkError } = await supabase
      .from('test_suites')
      .select(`
        suite_id,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', user.id)
      .single()

    if (checkError || !existingSuite) {
      return NextResponse.json({ error: 'Test suite not found' }, { status: 404 })
    }

    const updateData: TestSuiteUpdate = {}
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (type !== undefined) updateData.type = type
    if (tags !== undefined) updateData.tags = tags
    if (execution_times !== undefined) updateData.execution_times = execution_times

    const { data: suite, error } = await supabase
      .from('test_suites')
      .update(updateData)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .select('*')
      .single()

    if (error) {
      console.error('Error updating test suite:', error)
      return NextResponse.json({ error: 'Failed to update test suite' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: suite
    })
  } catch (error) {
    console.error('Error in PUT /api/projects/[projectId]/suites/[suiteId]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string }> }
) {
  try {
    const user = await SupabaseAuthServerService.getCurrentUserServer()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { projectId, suiteId } = resolvedParams
    const supabase = await createClient()
    
    // First verify the suite exists and belongs to user's project
    const { data: existingSuite, error: checkError } = await supabase
      .from('test_suites')
      .select(`
        suite_id,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', user.id)
      .single()

    if (checkError || !existingSuite) {
      return NextResponse.json({ error: 'Test suite not found' }, { status: 404 })
    }

    const { error } = await supabase
      .from('test_suites')
      .delete()
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)

    if (error) {
      console.error('Error deleting test suite:', error)
      return NextResponse.json({ error: 'Failed to delete test suite' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Test suite deleted successfully'
    })
  } catch (error) {
    console.error('Error in DELETE /api/projects/[projectId]/suites/[suiteId]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}