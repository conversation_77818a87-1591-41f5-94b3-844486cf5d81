import { fetchApi } from '../core/fetch';
import type {
  V2ExecutionResponse,
  TestExecutionHistoryData,
} from '@/lib/types';

/**
 * Execution History & Data Module
 * 
 * Functions for retrieving and managing execution history, MongoDB data,
 * and execution summaries. These functions provide access to historical
 * execution data for analysis and reporting.
 * 
 * @example
 * ```typescript
 * // Get execution history for a test case
 * const executions = await getTestMongoExecutions("project-id", "suite-id", "test-id", {
 *   limit: 10,
 *   includeAiAnalysis: true
 * });
 * 
 * // Get execution summaries for performance
 * const summaries = await getTestExecutionSummaries("project-id", "suite-id", "test-id");
 * 
 * // Get detailed execution history
 * const history = await getTestExecutionHistory("project-id", "suite-id", "test-id", "execution-id");
 * ```
 */

/**
 * Get executions from MongoDB for a specific test case
 * 
 * Retrieves execution history with flexible filtering options.
 * Supports pagination, environment filtering, and AI analysis inclusion.
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier  
 * @param testId - Unique test case identifier
 * @param environmentId - Optional environment filter
 * @param applicationVersion - Optional application version filter
 * @param limit - Maximum number of executions to return
 * @param includeAiAnalysis - Include AI analysis data in results
 * @param summaryOnly - Return only summary data for performance
 * @returns Promise<V2ExecutionResponse[]> Array of execution records
 */
export const getTestMongoExecutions = async (
  projectId: string,
  suiteId: string,
  testId: string,
  environmentId?: string,
  applicationVersion?: string,
  limit?: number,
  includeAiAnalysis?: boolean,
  summaryOnly?: boolean
): Promise<V2ExecutionResponse[]> => {
  // Construct query parameters
  const params = new URLSearchParams();
  if (environmentId) {
    params.append('environment_id', environmentId);
  }
  if (applicationVersion) {
    params.append('application_version', applicationVersion);
  }
  if (limit !== undefined) {
    params.append('limit', limit.toString());
  }
  if (includeAiAnalysis !== undefined) {
    params.append('include_ai_analysis', includeAiAnalysis.toString());
  }
  if (summaryOnly !== undefined) {
    params.append('summary_only', summaryOnly.toString());
  }
  const queryString = params.toString();

  // The backend returns an object { executions: [...], total_count, ... }
  const resp = await fetchApi<{
    executions: V2ExecutionResponse[];
    total_count: number;
    total_available: number;
    limit: number;
    has_more: boolean;
    summary_only: boolean;
    include_ai_analysis: boolean;
    message?: string;
  }>(
    `/v2/tests/projects/${encodeURIComponent(projectId)}/suites/${encodeURIComponent(suiteId)}/tests/${encodeURIComponent(testId)}/executions${queryString ? `?${queryString}` : ''}`
  );
  return resp.executions ?? [];
};

/**
 * Get execution summaries (optimized endpoint)
 * 
 * Retrieves lightweight execution summaries for performance-critical scenarios.
 * Much faster than full execution data when you only need basic information.
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param testId - Unique test case identifier
 * @param limit - Maximum number of summaries to return
 * @returns Promise<any[]> Array of execution summaries
 */
export const getTestExecutionSummaries = async (
  projectId: string,
  suiteId: string,
  testId: string,
  limit?: number
): Promise<any[]> => {
  // Construct query parameters
  const params = new URLSearchParams();
  if (limit !== undefined) {
    params.append('limit', limit.toString());
  }
  const queryString = params.toString();

  // The backend returns an object { summaries: [...], total_available, ... }
  const resp = await fetchApi<{
    summaries: any[];
    total_available: number;
    limit: number;
    has_more: boolean;
    message?: string;
  }>(
    `/v2/tests/projects/${encodeURIComponent(projectId)}/suites/${encodeURIComponent(suiteId)}/tests/${encodeURIComponent(testId)}/executions/summary${queryString ? `?${queryString}` : ''}`
  );
  return resp.summaries ?? [];
};

/**
 * Get detailed execution history with processed data
 * 
 * Retrieves comprehensive execution history including screenshots,
 * step-by-step details, and processed metadata. This is the most
 * detailed view of an execution.
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param testId - Unique test case identifier
 * @param executionId - Unique execution identifier
 * @returns Promise<TestExecutionHistoryData> Detailed execution history
 */
export async function getTestExecutionHistory(
  projectId: string,
  suiteId: string,
  testId: string,
  executionId: string
): Promise<TestExecutionHistoryData> {
  const response = await fetchApi<TestExecutionHistoryData>(
    `/history/${encodeURIComponent(projectId)}/${encodeURIComponent(suiteId)}/${encodeURIComponent(testId)}/${encodeURIComponent(executionId)}`
  );
  return response;
}