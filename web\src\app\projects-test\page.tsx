"use client";

import { useQuery } from '@tanstack/react-query';
import { getProjects } from '@/lib/api/projects';

export default function ProjectsTestPage() {
  const { data: projects, isLoading, error, isError } = useQuery({
    queryKey: ['projects-test'],
    queryFn: getProjects,
  });

  const projectsList = projects || [];

  console.log('Projects test page rendered');
  console.log('isLoading:', isLoading);
  console.log('isError:', isError);
  console.log('error:', error);
  console.log('projects:', projects);
  console.log('projectsList length:', projectsList.length);

  return (
    <div style={{ padding: '20px' }}>
      <h1>Projects Test Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
        <p><strong>Error:</strong> {isError ? 'Yes' : 'No'}</p>
        <p><strong>Projects count:</strong> {projectsList.length}</p>
      </div>

      {isLoading && <p>Loading projects...</p>}
      
      {isError && (
        <div style={{ color: 'red' }}>
          <p>Error loading projects:</p>
          <pre>{error?.message || 'Unknown error'}</pre>
        </div>
      )}
      
      {!isLoading && !isError && projectsList.length === 0 && (
        <p>No projects found</p>
      )}
      
      {!isLoading && !isError && projectsList.length > 0 && (
        <div>
          <h2>Projects Found: {projectsList.length}</h2>
          {projectsList.slice(0, 3).map((project, index) => (
            <div key={project.project_id || index} style={{ 
              border: '1px solid #ccc', 
              margin: '10px 0', 
              padding: '10px' 
            }}>
              <h3>{project.name}</h3>
              <p><strong>ID:</strong> {project.project_id}</p>
              <p><strong>Description:</strong> {project.description}</p>
              <p><strong>Tags:</strong> {project.tags?.join(', ') || 'None'}</p>
              <p><strong>Last Updated:</strong> {project.updated_at}</p>
            </div>
          ))}
          {projectsList.length > 3 && (
            <p>... and {projectsList.length - 3} more projects</p>
          )}
        </div>
      )}
    </div>
  );
}
