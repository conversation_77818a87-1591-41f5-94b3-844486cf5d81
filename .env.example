# Database
POSTGRES_PASSWORD=your-super-secret-and-long-postgres-password

# JWT
JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long

# Supabase Keys (Cloud Instance)
SUPABASE_URL=https://qekegvxuykuuokipfple.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFla2Vndnh1eWt1dW9raXBmcGxlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTc5Nzc2NDksImV4cCI6MjA3MzU1MzY0OX0.7sFgaSFtsiwdU4uKWjkXKVNlykK62A7itHGnx81kM-I
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFla2Vndnh1eWt1dW9raXBmcGxlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Nzk3NzY0OSwiZXhwIjoyMDczNTUzNjQ5fQ.WgHMszFAfr79TYIacsxADhXTOMIqqiGIkmR-FbKNias

# URLs
API_EXTERNAL_URL=http://localhost:8000
SITE_URL=http://localhost:3000
ADDITIONAL_REDIRECT_URLS=

# Auth Settings
DISABLE_SIGNUP=false
JWT_EXPIRY=3600

# Google OAuth (Required for Google Sign-in)
ENABLE_GOOGLE_OAUTH=true
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Steel Browser
STEEL_DOMAIN=localhost:3001
STEEL_CDP_DOMAIN=localhost:9223

# AI Configuration (at least one required)
GEMINI_API_KEY=your-gemini-api-key-here
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
AGENT_MAX_STEPS=8

# PostgREST
PGRST_DB_SCHEMAS=public

# Image Proxy
IMGPROXY_ENABLE_WEBP_DETECTION=true