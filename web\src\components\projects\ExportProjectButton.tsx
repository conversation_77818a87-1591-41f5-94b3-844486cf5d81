import { Button } from "@/components/ui/button";
import { exportProject } from "@/lib/api";

interface Props {
  projectId: string;
}

export function ExportProjectButton({ projectId }: Props) {
  const handleClick = async () => {
    try {
      const data = await exportProject(projectId);
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `project_${projectId}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error(err);
      alert("Unexpected error while exporting");
    }
  };

  return (
    <Button size="sm" variant="secondary" onClick={handleClick} className="ml-2">
      Export
    </Button>
  );
} 