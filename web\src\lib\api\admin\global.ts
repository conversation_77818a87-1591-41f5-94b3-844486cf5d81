/**
 * Global Administration Functions
 * 
 * Functions for managing global users, tenants, and system-wide statistics.
 * These functions require SuperAdmin privileges and operate across all tenants.
 * 
 * @module GlobalAdmin
 */

import { fetchApi } from '../core/fetch';

/**
 * Get global user statistics across all tenants
 * @returns Promise<any> Global user statistics
 * @example
 * ```typescript
 * const stats = await getGlobalUserStatistics();
 * console.log(`Total users: ${stats.totalUsers}`);
 * ```
 */
export const getGlobalUserStatistics = async () => {
  return fetchApi('/global/users/statistics');
};

/**
 * Get global tenant summary
 * @returns Promise<any> Tenant summary data
 */
export const getGlobalTenantSummary = async () => {
  return fetchApi('/global/tenants/summary');
};

/**
 * Get global users with pagination and filtering
 * @param page - Page number (default: 1)
 * @param pageSize - Items per page (default: 10)
 * @param search - Search term (optional)
 * @param role - Filter by role (optional)
 * @param status - Filter by status (optional)
 * @returns Promise<any> Paginated user list
 */
export const getGlobalUsers = async (page = 1, pageSize = 10, search?: string, role?: string, status?: string) => {
  const params = new URLSearchParams({
    page: page.toString(),
    pageSize: pageSize.toString()
  });
  
  if (search) params.append('search', search);
  if (role) params.append('role', role);
  if (status) params.append('status', status);
  
  return fetchApi(`/global/users?${params.toString()}`);
};

/**
 * Create a new global user
 * @param userData - User data for creation
 * @returns Promise<any> Created user data
 */
export const createGlobalUser = async (userData: any) => {
  return fetchApi('/Auth/register', {
    method: 'POST',
    body: JSON.stringify(userData)
  });
};

/**
 * Update a global user
 * @param userId - User ID to update
 * @param userData - Updated user data
 * @returns Promise<any> Updated user data
 */
export const updateGlobalUser = async (userId: string, userData: any) => {
  // Try different formats for the role
  const roleMapping: { [key: string]: number } = {
    'User': 0,
    'Admin': 1,
    'SuperAdmin': 2
  };

  // Wrap the data in the expected format
  const updateDto = {
    username: userData.username,
    email: userData.email,
    role: roleMapping[userData.role] !== undefined ? roleMapping[userData.role] : userData.role,
    isActive: userData.isActive
  };

  console.log('Sending update data:', updateDto);

  try {
    return await fetchApi(`/global/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(updateDto)
    });
  } catch (error) {
    // If the first attempt fails, try without the role mapping
    console.log('First attempt failed, trying without role mapping...');
    const simpleUpdateDto = {
      username: userData.username,
      email: userData.email,
      role: userData.role,
      isActive: userData.isActive
    };

    return fetchApi(`/global/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(simpleUpdateDto)
    });
  }
};

/**
 * Delete a global user
 * @param userId - User ID to delete
 * @returns Promise<any> Deletion result
 */
export const deleteGlobalUser = async (userId: string) => {
  return fetchApi(`/global/users/${userId}`, {
    method: 'DELETE'
  });
};

/**
 * Toggle global user activation status
 * @param userId - User ID to toggle
 * @param isActive - Current active status
 * @param reason - Reason for the change (optional)
 * @returns Promise<any> Toggle result
 */
export const toggleGlobalUserActivation = async (userId: string, isActive: boolean, reason?: string) => {
  return fetchApi(`/global/users/${userId}/activation`, {
    method: 'PATCH',
    body: JSON.stringify({
      isActive: !isActive,
      reason: reason || (isActive ? 'Admin deactivation' : 'Admin activation')
    })
  });
};

/**
 * Get global tenants with pagination and filtering
 * @param page - Page number (default: 1)
 * @param pageSize - Items per page (default: 10)
 * @param search - Search term (optional)
 * @param status - Filter by status (optional)
 * @returns Promise<any> Paginated tenant list
 */
export const getGlobalTenants = async (page = 1, pageSize = 10, search?: string, status?: string) => {
  const params = new URLSearchParams({
    Page: page.toString(),
    PageSize: pageSize.toString()
  });
  
  if (search) params.append('SearchTerm', search);
  if (status) params.append('Status', status);
  
  return fetchApi(`/Tenant?${params.toString()}`);
};

/**
 * Create a new global tenant
 * @param tenantData - Tenant data for creation
 * @returns Promise<any> Created tenant data
 */
export const createGlobalTenant = async (tenantData: any) => {
  return fetchApi('/Tenant', {
    method: 'POST',
    body: JSON.stringify(tenantData)
  });
};

/**
 * Update a global tenant
 * @param tenantId - Tenant ID to update
 * @param tenantData - Updated tenant data
 * @returns Promise<any> Updated tenant data
 */
export const updateGlobalTenant = async (tenantId: string, tenantData: any) => {
  return fetchApi(`/Tenant/${tenantId}`, {
    method: 'PUT',
    body: JSON.stringify(tenantData)
  });
};

/**
 * Delete a global tenant
 * @param tenantId - Tenant ID to delete
 * @returns Promise<any> Deletion result
 */
export const deleteGlobalTenant = async (tenantId: string) => {
  return fetchApi(`/Tenant/${tenantId}`, {
    method: 'DELETE'
  });
};

/**
 * Toggle global tenant status
 * @param tenantId - Tenant ID to toggle
 * @param isActive - Current active status
 * @param reason - Reason for the change (optional)
 * @returns Promise<any> Toggle result
 */
export const toggleGlobalTenantStatus = async (tenantId: string, isActive: boolean, reason?: string) => {
  return fetchApi(`/Tenant/${tenantId}/status`, {
    method: 'PATCH',
    body: JSON.stringify({
      isActive: !isActive,
      reason: reason || (isActive ? 'Admin deactivation' : 'Admin activation')
    })
  });
};

/**
 * Get emergency access token using bootstrap key
 * @param bootstrapKey - Bootstrap key for emergency access
 * @returns Promise<any> Access token
 */
export const getEmergencyAccessToken = async (bootstrapKey: string) => {
  return fetchApi('/bootstrap/emergency-access', {
    method: 'POST',
    body: JSON.stringify({ bootstrapKey })
  });
};