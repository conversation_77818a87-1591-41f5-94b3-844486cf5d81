'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContextSupabase'
import { Loader2 } from 'lucide-react'

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const { user, loading } = useAuth()

   useEffect(() => {
     if (!loading) {
       // Check if user is SuperAdmin or Admin for LLM pages
       if (!user || (user.role !== 'SuperAdmin' && user.role !== 'Admin')) {
         router.push('/login')
         return
       }
     }
   }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

   if (!user || (user.role !== 'SuperAdmin' && user.role !== 'Admin')) {
     return (
       <div className="flex items-center justify-center min-h-screen">
         <div className="text-center">
           <h2 className="text-2xl font-bold text-destructive mb-2">Acceso Denegado</h2>
           <p className="text-muted-foreground">No tienes permisos para acceder a esta sección.</p>
           <p className="text-sm text-muted-foreground mt-2">Rol actual: {user?.role || 'No definido'}</p>
           <p className="text-sm text-muted-foreground">Se requiere: SuperAdmin o Admin</p>
         </div>
       </div>
     )
   }

  // Return children without wrapper - let ClientLayout handle the sidebar and styling
  return <>{children}</>
}