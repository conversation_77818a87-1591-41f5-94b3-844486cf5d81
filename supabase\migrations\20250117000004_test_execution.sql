-- Migration: Test Execution Tables
-- Description: Creates tables for tracking test executions and their steps
-- Dependencies: 20250117000002_core_tables.sql

-- Test executions table
CREATE TABLE IF NOT EXISTS test_executions (
  execution_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES projects(project_id) ON DELETE SET NULL,
  test_type TEXT NOT NULL CHECK (test_type IN ('smoke', 'full', 'case', 'suite')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
  config JSONB,
  results JSONB,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Execution steps table (for detailed step-by-step tracking)
CREATE TABLE IF NOT EXISTS execution_steps (
  step_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  execution_id UUID REFERENCES test_executions(execution_id) ON DELETE CASCADE,
  step_number INTEGER NOT NULL,
  action TEXT,
  target TEXT,
  value TEXT,
  screenshot_url TEXT,
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_test_executions_user_id ON test_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_test_executions_project_id ON test_executions(project_id);
CREATE INDEX IF NOT EXISTS idx_test_executions_status ON test_executions(status);
CREATE INDEX IF NOT EXISTS idx_test_executions_created_at ON test_executions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_execution_steps_execution_id ON execution_steps(execution_id);
CREATE INDEX IF NOT EXISTS idx_execution_steps_step_number ON execution_steps(execution_id, step_number);

-- Create trigger for test_executions updated_at
CREATE TRIGGER update_test_executions_updated_at
    BEFORE UPDATE ON test_executions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE test_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE execution_steps ENABLE ROW LEVEL SECURITY;

-- RLS Policies for test_executions
-- Users can only see their own executions
CREATE POLICY "Users can view their own executions" ON test_executions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own executions" ON test_executions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own executions" ON test_executions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own executions" ON test_executions
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for execution_steps
-- Users can view steps for their executions
CREATE POLICY "Users can view their execution steps" ON execution_steps
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM test_executions
            WHERE test_executions.execution_id = execution_steps.execution_id
            AND test_executions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert execution steps for their executions" ON execution_steps
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM test_executions
            WHERE test_executions.execution_id = execution_steps.execution_id
            AND test_executions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update execution steps for their executions" ON execution_steps
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM test_executions
            WHERE test_executions.execution_id = execution_steps.execution_id
            AND test_executions.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete execution steps for their executions" ON execution_steps
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM test_executions
            WHERE test_executions.execution_id = execution_steps.execution_id
            AND test_executions.user_id = auth.uid()
        )
    );

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON test_executions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON execution_steps TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;