"""
LLM Providers Module
Handles different LLM providers (OpenAI, Gemini) for browser automation
"""
from abc import ABC, abstractmethod
import os
from typing import Optional
import google.generativeai as genai
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    @abstractmethod
    async def chat_completion(self, message: str) -> str:
        """Get chat completion from the LLM"""
        pass

class GeminiProvider(LLMProvider):
    """Google's Gemini AI provider"""
    
    def __init__(self):
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not set")
        
        genai.configure(api_key=api_key)
        model_name = os.getenv("GEMINI_MODEL", "gemini-2.5-flash")
        self.model = genai.GenerativeModel(model_name)
        self.chat = self.model.start_chat(history=[])

    async def chat_completion(self, message: str) -> str:
        try:
            response = self.chat.send_message(message)
            return response.text
        except Exception as e:
            raise Exception(f"Error with Gemini chat completion: {str(e)}")

class OpenAIProvider(LLMProvider):
    """OpenAI's GPT provider"""
    
    def __init__(self):
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY not set")
        
        self.client = openai.AsyncOpenAI(api_key=api_key)
        self.model = os.getenv("OPENAI_MODEL", "gpt-4-turbo-preview")

    async def chat_completion(self, message: str) -> str:
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": message}],
                temperature=0.7,
                max_tokens=1000
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"Error with OpenAI chat completion: {str(e)}")

def get_llm_provider() -> LLMProvider:
    """Factory function to get the configured LLM provider"""
    default_provider = os.getenv("DEFAULT_LLM_PROVIDER", "openai").lower()
    
    if default_provider == "openai" and os.getenv("OPENAI_API_KEY"):
        return OpenAIProvider()
    elif default_provider == "gemini" and os.getenv("GEMINI_API_KEY"):
        return GeminiProvider()
    elif os.getenv("OPENAI_API_KEY"):
        return OpenAIProvider()
    elif os.getenv("GEMINI_API_KEY"):
        return GeminiProvider()
    else:
        raise ValueError("No valid LLM provider configured. Set either OPENAI_API_KEY or GEMINI_API_KEY")