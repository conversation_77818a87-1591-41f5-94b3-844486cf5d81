"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getSuiteById, updateSuite } from "@/lib/api";
import type { TestSuiteUpdateInput } from "@/lib/types";
import { SuiteForm } from "@/components/forms/SuiteForm";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export default function EditSuitePage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;

  const { data: suite, isLoading, error, isError } = useQuery({
    queryKey: ['suite', projectId, suiteId],
    queryFn: () => getSuiteById(projectId, suiteId),
    enabled: !!projectId && !!suiteId,
  });

  const mutation = useMutation({
    mutationFn: (updatedSuite: TestSuiteUpdateInput) => updateSuite(projectId, suiteId, updatedSuite),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['suite', projectId, suiteId] });
      queryClient.invalidateQueries({ queryKey: ['suites', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] });
      toast({
        title: "Suite Updated",
        description: `Suite "${data.name}" has been successfully updated.`,
      });
      router.push(`/projects/${projectId}/suites/${suiteId}`);
    },
    onError: (error) => {
      toast({
        title: "Error Updating Suite",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: TestSuiteUpdateInput) => {
    await mutation.mutateAsync(data);
  };

  if (isLoading) {
    return (
      <div>
        <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
        <Skeleton className="h-10 w-1/2 mb-6" /> {/* Page header */}
        <div className="space-y-6">
          <Skeleton className="h-32 w-full" /> {/* Form skeleton */}
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-16 w-full" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href={`/projects/${projectId}/suites/${suiteId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Suite
          </Link>
        </Button>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error loading suite</AlertTitle>
          <AlertDescription>{error?.message || 'An unknown error occurred.'}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!suite) {
    return (
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href={`/projects/${projectId}/suites/${suiteId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Suite
          </Link>
        </Button>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Suite not found</AlertTitle>
          <AlertDescription>The requested suite could not be found.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suite
        </Link>
      </Button>
      <h1 className="page-header">Edit Suite</h1>
      <SuiteForm 
        suite={suite}
        onSubmit={handleSubmit} 
        isSubmitting={mutation.isPending}
        submitButtonText="Update Suite"
      />
    </div>
  );
}
