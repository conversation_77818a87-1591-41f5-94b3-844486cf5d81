import { fetchApi } from '../core/fetch';
import type {
  ExecutionRequest,
  ExecutionResponse as ExecutionResponseV2,
  A<PERSON>Health,
  TestExecutionHistoryData,
} from '@/lib/types';

/**
 * Execution API V1 Module (Legacy)
 * 
 * Legacy test execution APIs that are being phased out in favor of V2.
 * These functions are maintained for backward compatibility but should
 * be migrated to V2 APIs when possible.
 * 
 * @deprecated Most functions here are legacy and should use V2 APIs instead
 * 
 * @example
 * ```typescript
 * // Legacy execution (use V2 instead)
 * const result = await executeTest(request);
 * 
 * // Health check (still used)
 * const health = await getApiHealth();
 * ```
 */

/**
 * Legacy test execution endpoint (V1)
 * 
 * @deprecated Use executeTestV2 from execution/v2.ts instead
 * 
 * This function uses the older execution API that has limited functionality
 * compared to the V2 APIs. Prefer using V2 APIs for new implementations.
 * 
 * @param request - Legacy execution request format
 * @returns Promise<ExecutionResponseV2> Execution result
 * @throws Error if execution fails
 */
export const executeTest = async (request: ExecutionRequest): Promise<ExecutionResponseV2> => {
  // The C# backend wraps the response in a BaseResponseDto structure
  const response = await fetchApi<{success: boolean, message: string, data: ExecutionResponseV2}>('/v2/tests/execute', {
    method: 'POST',
    body: JSON.stringify(request),
  });

  // Extract the actual execution response from the wrapper
  return response.data;
};

/**
 * Get API health status
 * 
 * This is still actively used and not deprecated.
 * Provides system health information including service status and connectivity.
 * 
 * @returns Promise<ApiHealth> Current API health status
 * @throws Error if health check fails
 */
export const getApiHealth = (): Promise<ApiHealth> => fetchApi<ApiHealth>('/health');

/**
 * Get detailed test execution history
 * 
 * Retrieves processed execution history with screenshots and metadata.
 * This function processes history files and extracts useful information.
 * 
 * @param historyPath - Path to the execution history file
 * @returns Promise<TestExecutionHistoryData> Processed execution history
 * @throws Error if history retrieval fails
 */
export const getTestExecutionHistoryDetails = async (historyPath: string): Promise<TestExecutionHistoryData> => {
  // Use the new backend endpoint that processes the history and extracts screenshots
  const response = await fetchApi<TestExecutionHistoryData>(`/history/${encodeURIComponent(historyPath)}`);
  return response;
};