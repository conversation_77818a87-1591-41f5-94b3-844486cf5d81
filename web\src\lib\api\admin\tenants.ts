/**
 * Tenant Management Functions
 * 
 * Functions for managing tenant-specific operations, users, and settings.
 * These functions operate within the context of a specific tenant.
 * 
 * @module TenantAdmin
 */

import { fetchApi } from '../core/fetch';
import { TenantService } from '../../tenant';

// ============================================================================
// TENANT-SCOPED OPERATIONS
// ============================================================================

/**
 * Get users belonging to the current user's tenant
 * @param tenantId - Optional tenant ID (uses current tenant if not provided)
 * @returns Promise<any> List of tenant users
 * @example
 * ```typescript
 * const users = await getTenantUsers();
 * console.log(`Tenant has ${users.length} users`);
 * ```
 */
export const getTenantUsers = async (tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/users`, {
    headers: {
      'X-Tenant-ID': effectiveTenantId
    }
  });
};

/**
 * Add a user to the current user's tenant
 * @param userId - User ID to add
 * @param role - Role to assign (default: 'Member')
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Addition result
 */
export const addUserToTenant = async (userId: string, role: string = 'Member', tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/users/${userId}`, {
    method: 'POST',
    headers: {
      'X-Tenant-ID': effectiveTenantId
    },
    body: JSON.stringify({ role })
  });
};

/**
 * Remove a user from the current user's tenant
 * @param userId - User ID to remove
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Removal result
 */
export const removeUserFromTenant = async (userId: string, tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/users/${userId}`, {
    method: 'DELETE',
    headers: {
      'X-Tenant-ID': effectiveTenantId
    }
  });
};

/**
 * Update user role within the current user's tenant
 * @param userId - User ID to update
 * @param newRole - New role to assign
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Update result
 */
export const updateUserRoleInTenant = async (userId: string, newRole: string, tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/users/${userId}/role`, {
    method: 'PUT',
    headers: {
      'X-Tenant-ID': effectiveTenantId
    },
    body: JSON.stringify({ newRole })
  });
};

/**
 * Get tenant statistics for the current user's tenant
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Tenant statistics
 */
export const getTenantStatistics = async (tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/statistics`, {
    headers: {
      'X-Tenant-ID': effectiveTenantId
    }
  });
};

/**
 * Update tenant settings (only for tenant admins)
 * @param settings - Settings to update
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Update result
 */
export const updateTenantSettings = async (settings: any, tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/settings`, {
    method: 'PUT',
    headers: {
      'X-Tenant-ID': effectiveTenantId
    },
    body: JSON.stringify(settings)
  });
};

/**
 * Get tenant invitations (only for tenant admins)
 * @param tenantId - Optional tenant ID
 * @param filters - Optional filters for invitations
 * @returns Promise<any> List of invitations
 */
export const getTenantInvitations = async (tenantId?: string, filters?: any) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  const queryParams = filters ? new URLSearchParams(filters).toString() : '';
  const url = `/tenant/${effectiveTenantId}/invitations${queryParams ? `?${queryParams}` : ''}`;

  return fetchApi(url, {
    headers: {
      'X-Tenant-ID': effectiveTenantId
    }
  });
};

/**
 * Create tenant invitation (only for tenant admins)
 * @param invitationData - Invitation data
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Created invitation
 */
export const createTenantInvitation = async (invitationData: any, tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/invitations`, {
    method: 'POST',
    headers: {
      'X-Tenant-ID': effectiveTenantId
    },
    body: JSON.stringify(invitationData)
  });
};

/**
 * Validate tenant access for current user
 * @param tenantId - Tenant ID to validate
 * @returns Promise<any> Validation result
 */
export const validateTenantAccess = async (tenantId: string) => {
  return fetchApi(`/tenant/${tenantId}/validate`, {
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
};

/**
 * Check if tenant can add more users
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Availability check result
 */
export const canTenantAddUser = async (tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/can-add-user`, {
    headers: {
      'X-Tenant-ID': effectiveTenantId
    }
  });
};

/**
 * Check if tenant can add more projects
 * @param tenantId - Optional tenant ID
 * @returns Promise<any> Availability check result
 */
export const canTenantAddProject = async (tenantId?: string) => {
  const effectiveTenantId = tenantId || TenantService.getCurrentTenantId();
  if (!effectiveTenantId) {
    throw new Error('No tenant ID available');
  }

  return fetchApi(`/tenant/${effectiveTenantId}/can-add-project`, {
    headers: {
      'X-Tenant-ID': effectiveTenantId
    }
  });
};