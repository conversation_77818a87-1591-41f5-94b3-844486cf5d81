# Browser Automation Setup

Esta guía te ayudará a configurar y usar el sistema de automatización de navegador integrado con Next.js.

## Requisitos Previos

1. **Python 3.11+** instalado en tu sistema
2. **Docker** para ejecutar Steel Browser
3. **Node.js 18+** para la aplicación Next.js

## Configuración Paso a Paso

### 1. Configurar Steel Browser (Docker)

Steel Browser proporciona la infraestructura de navegador que usa el sistema de automatización.

```bash
# Navegar al directorio browser
cd /Users/<USER>/Proyectos/persoqak/browser

# Levantar Steel Browser con Docker
docker-compose up -d
```

Esto iniciará:
- **API de Steel Browser** en `http://localhost:3000`
- **UI de Steel Browser** en `http://localhost:5173`
- **CDP (Chrome DevTools Protocol)** en `ws://localhost:9223`

### 2. Configurar el Servicio Python

El servicio Python maneja la automatización del navegador usando browser-use.

```bash
# Navegar al directorio browser
cd /Users/<USER>/Proyectos/persoqak/browser

# Instalar dependencias Python
pip install -e .

# Configurar variables de entorno
cp .env.example .env
# Editar .env y agregar tu GEMINI_API_KEY
```

**Archivo .env requerido:**
```env
GEMINI_API_KEY=tu_clave_de_gemini_aqui
GEMINI_MODEL=gemini-2.5-flash
AGENT_MAX_STEPS=8
```

### 3. Iniciar el Servicio Python

```bash
# Desde el directorio browser
python start.py
```

Esto iniciará:
- **API Python** en `http://localhost:8080`
- **WebSocket** en `ws://localhost:8080/ws`
- **UI Web** en `http://localhost:8080` (opcional)

### 4. Verificar la Configuración

1. **Steel Browser**: Visita `http://localhost:5173` - deberías ver la UI de Steel Browser
2. **Servicio Python**: Visita `http://localhost:8080` - deberías ver la UI del recorder
3. **Next.js**: Visita `http://localhost:9001/browser-automation` - deberías ver el panel de control

## Uso del Sistema

### Desde la UI de Next.js

1. **Navegar a Browser Automation**:
   ```
   http://localhost:9001/browser-automation
   ```

2. **Crear una Sesión**:
   - Haz clic en "Start Session" para crear una sesión de navegador
   - El estado debería cambiar a "Session Active"

3. **Iniciar Grabación**:
   - Haz clic en "Start Recording" para comenzar a grabar interacciones
   - Todas las acciones del navegador se grabarán automáticamente

4. **Interactuar con el Agente AI**:
   - Usa el chat para enviar instrucciones al agente
   - El agente ejecutará las acciones automáticamente
   - Verás los pasos en tiempo real

### Ejecutar Test Cases

1. **Desde un Test Case**:
   ```
   /projects/{projectId}/suites/{suiteId}/tests/{testId}/execute-browser
   ```

2. **Ejecución Automática**:
   - El sistema creará automáticamente una sesión
   - Enviará las instrucciones del test case al agente
   - Grabará todos los pasos de ejecución
   - Actualizará el estado del test case

## API Endpoints

### Browser Automation APIs

- `POST /api/browser-automation/session` - Crear sesión de navegador
- `DELETE /api/browser-automation/session` - Detener sesión
- `POST /api/browser-automation/recording?action=start` - Iniciar grabación
- `POST /api/browser-automation/recording?action=stop` - Detener grabación
- `GET /api/browser-automation/steps` - Obtener pasos grabados
- `GET /api/browser-automation/chat` - Obtener mensajes del chat
- `POST /api/browser-automation/chat` - Enviar mensaje al agente
- `POST /api/browser-automation/execute-test` - Ejecutar test case

### Ejemplo de Uso con API

```javascript
// Crear sesión
const session = await fetch('/api/browser-automation/session', {
  method: 'POST'
})

// Ejecutar test case
const execution = await fetch('/api/browser-automation/execute-test', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    instructions: "Click on the login button and enter credentials",
    user_story: "As a user, I want to log into the system",
    url: "https://example.com/login"
  })
})
```

## Arquitectura del Sistema

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js UI   │    │  Python Service │    │  Steel Browser  │
│  (Port 9001)   │◄──►│  (Port 8080)    │◄──►│  (Port 3000)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐             ┌────▼────┐             ┌────▼────┐
    │ React   │             │browser- │             │ Chrome  │
    │Components│             │use +    │             │ Browser │
    │         │             │ Gemini  │             │ Instance│
    └─────────┘             └─────────┘             └─────────┘
```

## Componentes Principales

### 1. BrowserAutomationPanel (React)
- Panel de control principal
- Manejo de sesiones y grabación
- Chat con el agente AI
- Visualización de pasos en tiempo real

### 2. BrowserAutomationApiHandler (Next.js)
- Proxy entre Next.js y el servicio Python
- Manejo de errores y autenticación
- Integración con Supabase

### 3. UIServer (Python)
- Servidor FastAPI con WebSocket
- Integración con browser-use
- Agente AI con Gemini
- Grabación de interacciones CDP

### 4. Steel Browser (Docker)
- Navegador Chrome controlado remotamente
- API REST y WebSocket
- Chrome DevTools Protocol (CDP)

## Solución de Problemas

### Error: "Browser automation service is not running"
- Verificar que el servicio Python esté ejecutándose en puerto 8080
- Verificar que Steel Browser esté ejecutándose en puerto 3000

### Error: "WebSocket disconnected"
- Reiniciar el servicio Python
- Verificar que no haya conflictos de puerto

### Error: "GEMINI_API_KEY not set"
- Configurar la clave API de Gemini en el archivo .env
- Reiniciar el servicio Python

### El navegador no responde
- Reiniciar Steel Browser: `docker-compose restart`
- Verificar que Docker esté ejecutándose correctamente

## Desarrollo y Extensión

### Agregar Nuevas Funcionalidades

1. **Nuevos Tipos de Pasos**:
   - Modificar `RecordingStep` interface en `browser-automation.ts`
   - Actualizar el handler de CDP en el servicio Python

2. **Nuevos Comandos del Agente**:
   - Extender el prompt del agente en el servicio Python
   - Agregar nuevos tipos de mensaje en el chat

3. **Integración con Test Cases**:
   - Modificar `TestCasesApiHandler` para incluir resultados de automatización
   - Actualizar la UI de test cases para mostrar pasos grabados

### Estructura de Archivos

```
web/
├── src/
│   ├── app/
│   │   ├── api/browser-automation/     # API routes
│   │   ├── browser-automation/         # UI pages
│   │   └── projects/.../execute-browser/ # Test execution
│   ├── components/browser-automation/  # React components
│   └── lib/api-supabase/              # API handlers
└── BROWSER_AUTOMATION_SETUP.md       # Esta documentación

browser/
├── browser_use_recorder/
│   ├── server.py                      # Servidor Python principal
│   └── ui/                           # Templates y assets
├── docker-compose.yml                # Steel Browser setup
├── start.py                          # Launcher
└── pyproject.toml                    # Dependencias Python
```

## Próximos Pasos

1. **Configurar Supabase**: Crear proyecto y aplicar migraciones
2. **Configurar Variables de Entorno**: Actualizar .env con credenciales reales
3. **Probar Integración**: Ejecutar un test case completo
4. **Personalizar Agente**: Ajustar prompts según necesidades específicas
5. **Monitoreo**: Implementar logging y métricas de ejecución