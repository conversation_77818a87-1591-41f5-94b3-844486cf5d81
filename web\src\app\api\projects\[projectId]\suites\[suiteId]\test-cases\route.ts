import { NextRequest } from 'next/server'
import { TestCasesApiHandler } from '@/lib/api-supabase/test-cases'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string }> }
) {
  const handler = new TestCasesApiHandler()
  return handler.handleRequest(async () => {
    const resolvedParams = await params
    return handler.getTestCases(resolvedParams.projectId, resolvedParams.suiteId)
  })
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string; suiteId: string }> }
) {
  const handler = new TestCasesApiHandler()
  return handler.handleRequest(async () => {
    const body = await request.json()
    const resolvedParams = await params
    return handler.createTestCase(resolvedParams.projectId, resolvedParams.suiteId, body)
  })
}