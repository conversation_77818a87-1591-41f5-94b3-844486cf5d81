'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Plus, Search, Edit, Trash2, DollarSign, Clock } from 'lucide-react'
import { toast } from 'sonner'
import { getLLMModels, createLLMModel, updateLLMModel, deleteLLMModel, getLLMProviders } from '@/lib/api'

interface LLMModel {
  id: string
  providerId: string
  modelId: string
  displayName: string
  capabilities: string[]
  inputCostPer1K: number
  outputCostPer1K: number
  maxTokens: number
  defaultParams: Record<string, any>
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface LLMProvider {
  id: string
  name: string
  displayName: string
  isActive: boolean
}

export default function LLMModelsPage() {
  const [models, setModels] = useState<LLMModel[]>([])
  const [providers, setProviders] = useState<LLMProvider[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedProvider, setSelectedProvider] = useState<string>('all')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingModel, setEditingModel] = useState<LLMModel | null>(null)
  const [newModel, setNewModel] = useState({
    providerId: '',
    modelId: '',
    displayName: '',
    capabilities: [] as string[],
    inputCostPer1K: 0,
    outputCostPer1K: 0,
    maxTokens: 0,
    defaultParams: {},
    isActive: true
  })

  // Datos de ejemplo
  const sampleModels: LLMModel[] = [
    {
      id: '1',
      providerId: 'openai',
      modelId: 'gpt-4',
      displayName: 'GPT-4',
      capabilities: ['text-generation', 'conversation'],
      inputCostPer1K: 0.03,
      outputCostPer1K: 0.06,
      maxTokens: 8192,
      defaultParams: { temperature: 0.7, top_p: 1 },
      isActive: true,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      providerId: 'anthropic',
      modelId: 'claude-3-sonnet',
      displayName: 'Claude 3 Sonnet',
      capabilities: ['text-generation', 'conversation', 'analysis'],
      inputCostPer1K: 0.015,
      outputCostPer1K: 0.075,
      maxTokens: 200000,
      defaultParams: { temperature: 0.3, max_tokens: 4000 },
      isActive: true,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '3',
      providerId: 'google',
      modelId: 'gemini-pro',
      displayName: 'Gemini Pro',
      capabilities: ['text-generation', 'conversation', 'multimodal'],
      inputCostPer1K: 0.0005,
      outputCostPer1K: 0.0015,
      maxTokens: 32768,
      defaultParams: { temperature: 0.4, top_k: 40 },
      isActive: false,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    }
  ]

  const sampleProviders: LLMProvider[] = [
    { id: 'openai', name: 'openai', displayName: 'OpenAI', isActive: true },
    { id: 'anthropic', name: 'anthropic', displayName: 'Anthropic', isActive: true },
    { id: 'google', name: 'google', displayName: 'Google', isActive: true },
    { id: 'openrouter', name: 'openrouter', displayName: 'OpenRouter', isActive: true }
  ]

  const fetchModels = async () => {
    try {
      const response = await getLLMModels()
      // Handle different response formats
      let modelsData: LLMModel[] = []
      const responseAny = response as any

      if (Array.isArray(responseAny)) {
        modelsData = responseAny as LLMModel[]
      } else if (responseAny && typeof responseAny === 'object') {
        if (responseAny.data && Array.isArray(responseAny.data)) {
          modelsData = responseAny.data as LLMModel[]
        } else if (responseAny.items && Array.isArray(responseAny.items)) {
          modelsData = responseAny.items as LLMModel[]
        } else if ('id' in responseAny) {
          modelsData = [responseAny as LLMModel]
        }
      }

      // Ensure all models have valid capabilities arrays
      modelsData = modelsData.map(model => ({
        ...model,
        capabilities: Array.isArray(model.capabilities) ? model.capabilities : []
      }))

      setModels(modelsData)
    } catch (error) {
      console.error('Error fetching models:', error)
      // Usar datos de ejemplo si el endpoint no está disponible
      setModels(sampleModels)
      toast.error('Error al cargar modelos, usando datos de ejemplo')
    }
  }

  const fetchProviders = async () => {
    try {
      const response = await getLLMProviders()
      // Handle different response formats
      let providersData: LLMProvider[] = []
      if (Array.isArray(response)) {
        providersData = response as LLMProvider[]
      } else if (response && typeof response === 'object' && !Array.isArray(response)) {
        const responseObj = response as Record<string, any>
        if (responseObj.data && Array.isArray(responseObj.data)) {
          providersData = responseObj.data as LLMProvider[]
        } else if (responseObj.items && Array.isArray(responseObj.items)) {
          providersData = responseObj.items as LLMProvider[]
        } else if ('id' in responseObj) {
          providersData = [responseObj as LLMProvider]
        }
      }

      // Ensure providersData is always an array
      if (!Array.isArray(providersData)) {
        providersData = []
      }

      setProviders(providersData)
    } catch (error) {
      console.error('Error fetching providers:', error)
      setProviders(sampleProviders)
      toast.error('Error al cargar proveedores, usando datos de ejemplo')
    }
  }

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([fetchModels(), fetchProviders()])
      setLoading(false)
    }
    loadData()
  }, [])

  const filteredModels = Array.isArray(models) ? models.filter(model => {
    const matchesSearch = model.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          model.modelId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesProvider = selectedProvider === 'all' || model.providerId === selectedProvider
    return matchesSearch && matchesProvider
  }) : []

  const handleToggleActive = async (modelId: string, isActive: boolean) => {
    try {
      await updateLLMModel(modelId, { isActive })
      setModels(prev => prev.map(model =>
        model.id === modelId ? { ...model, isActive } : model
      ))
      toast.success(`Modelo ${isActive ? 'activado' : 'desactivado'} correctamente`)
    } catch (error) {
      console.error('Error toggling model status:', error)
      toast.error('Error al actualizar el modelo')
    }
  }

  const handleDeleteModel = async (modelId: string) => {
    try {
      await deleteLLMModel(modelId)
      setModels(prev => prev.filter(model => model.id !== modelId))
      toast.success('Modelo eliminado correctamente')
    } catch (error) {
      console.error('Error deleting model:', error)
      toast.error('Error al eliminar el modelo')
    }
  }

  const handleCreateModel = async (modelData: any) => {
    try {
      await createLLMModel(modelData)
      toast.success('Modelo creado correctamente')
      setShowCreateDialog(false)
      fetchModels()
    } catch (error) {
      console.error('Error creating model:', error)
      toast.error('Error al crear el modelo')
    }
  }

  const handleUpdateModel = async (modelId: string, modelData: any) => {
    try {
      await updateLLMModel(modelId, modelData)
      toast.success('Modelo actualizado correctamente')
      setEditingModel(null)
      fetchModels()
    } catch (error) {
      console.error('Error updating model:', error)
      toast.error('Error al actualizar el modelo')
    }
  }

  const getCapabilityColor = (capability: string) => {
    const colors: Record<string, string> = {
      'text-generation': 'bg-primary/10 text-primary border-primary/20',
      'conversation': 'bg-accent/10 text-accent border-accent/20',
      'analysis': 'bg-secondary/20 text-secondary-foreground border-secondary/30',
      'multimodal': 'bg-warning/10 text-warning border-warning/20',
      'code-generation': 'bg-muted/30 text-muted-foreground border-muted/40'
    }
    return colors[capability] || 'bg-muted/30 text-muted-foreground border-muted/40'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary border-t-transparent"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Modelos LLM</h1>
          <p className="text-muted-foreground">
            Gestiona los modelos de lenguaje disponibles en el sistema
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nuevo Modelo
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
             <DialogHeader>
               <DialogTitle>{editingModel ? 'Editar Modelo' : 'Crear Nuevo Modelo'}</DialogTitle>
               <DialogDescription>
                 {editingModel ? 'Modifica la configuración del modelo LLM' : 'Configura un nuevo modelo LLM para el sistema'}
               </DialogDescription>
             </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="provider">Proveedor</Label>
                   <Select
                     value={newModel.providerId}
                     onValueChange={(value) => setNewModel({ ...newModel, providerId: value })}
                   >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar proveedor" />
                      </SelectTrigger>
                       <SelectContent>
                         {Array.isArray(providers) && providers.map(provider => (
                           <SelectItem key={provider.id} value={provider.id}>
                             {provider.displayName}
                           </SelectItem>
                         ))}
                       </SelectContent>
                   </Select>
                 </div>
                 <div className="space-y-2">
                   <Label htmlFor="modelId">ID del Modelo</Label>
                   <Input
                     id="modelId"
                     placeholder="gpt-4, claude-3-sonnet, etc."
                     value={newModel.modelId}
                     onChange={(e) => setNewModel({ ...newModel, modelId: e.target.value })}
                   />
                 </div>
               </div>
               <div className="space-y-2">
                 <Label htmlFor="displayName">Nombre para Mostrar</Label>
                 <Input
                   id="displayName"
                   placeholder="GPT-4, Claude 3 Sonnet, etc."
                   value={newModel.displayName}
                   onChange={(e) => setNewModel({ ...newModel, displayName: e.target.value })}
                 />
               </div>
               <div className="space-y-2">
                 <Label>Capacidades</Label>
                 <div className="grid grid-cols-2 gap-2">
                   {['text-generation', 'conversation', 'analysis', 'multimodal', 'code-generation', 'image-generation'].map((capability) => (
                     <div key={capability} className="flex items-center space-x-2">
                       <input
                         type="checkbox"
                         id={`capability-${capability}`}
                         checked={newModel.capabilities.includes(capability)}
                         onChange={(e) => {
                           const updatedCapabilities = e.target.checked
                             ? [...newModel.capabilities, capability]
                             : newModel.capabilities.filter(c => c !== capability);
                           setNewModel({ ...newModel, capabilities: updatedCapabilities });
                         }}
                         className="rounded"
                       />
                       <Label htmlFor={`capability-${capability}`} className="text-sm capitalize">
                         {capability.replace('-', ' ')}
                       </Label>
                     </div>
                   ))}
                 </div>
               </div>
               <div className="grid grid-cols-3 gap-4">
                 <div className="space-y-2">
                   <Label htmlFor="inputCost">Costo Input (por 1K tokens)</Label>
                   <Input
                     id="inputCost"
                     type="number"
                     step="0.001"
                     placeholder="0.03"
                     value={newModel.inputCostPer1K}
                     onChange={(e) => setNewModel({ ...newModel, inputCostPer1K: parseFloat(e.target.value) || 0 })}
                   />
                 </div>
                 <div className="space-y-2">
                   <Label htmlFor="outputCost">Costo Output (por 1K tokens)</Label>
                   <Input
                     id="outputCost"
                     type="number"
                     step="0.001"
                     placeholder="0.06"
                     value={newModel.outputCostPer1K}
                     onChange={(e) => setNewModel({ ...newModel, outputCostPer1K: parseFloat(e.target.value) || 0 })}
                   />
                 </div>
                 <div className="space-y-2">
                   <Label htmlFor="maxTokens">Máximo Tokens</Label>
                   <Input
                     id="maxTokens"
                     type="number"
                     placeholder="8192"
                     value={newModel.maxTokens}
                     onChange={(e) => setNewModel({ ...newModel, maxTokens: parseInt(e.target.value) || 0 })}
                   />
                 </div>
               </div>
               <div className="space-y-2">
                 <Label htmlFor="defaultParams">Parámetros por Defecto (JSON)</Label>
                 <Textarea
                   id="defaultParams"
                   placeholder='{"temperature": 0.7, "top_p": 1}'
                   className="font-mono text-sm"
                   value={JSON.stringify(newModel.defaultParams, null, 2)}
                   onChange={(e) => {
                     try {
                       const parsed = JSON.parse(e.target.value || '{}');
                       setNewModel({ ...newModel, defaultParams: parsed });
                     } catch (error) {
                       // Keep current value if JSON is invalid
                     }
                   }}
                 />
               </div>
               <div className="flex items-center space-x-2">
                 <Switch
                   id="isActive"
                   checked={newModel.isActive}
                   onCheckedChange={(checked) => setNewModel({ ...newModel, isActive: checked })}
                 />
                 <Label htmlFor="isActive">Modelo activo</Label>
               </div>
            </div>
             <div className="flex justify-end space-x-2">
               <Button variant="outline" onClick={() => {
                 setShowCreateDialog(false)
                 setEditingModel(null)
                 setNewModel({
                   providerId: '',
                   modelId: '',
                   displayName: '',
                   capabilities: [],
                   inputCostPer1K: 0,
                   outputCostPer1K: 0,
                   maxTokens: 0,
                   defaultParams: {},
                   isActive: true
                 })
               }}>
                 Cancelar
               </Button>
                <Button onClick={() => {
                 // Validación completa del formulario
                 if (!newModel.providerId) {
                   toast.error('Por favor selecciona un proveedor')
                   return
                 }
                 if (!newModel.modelId.trim()) {
                   toast.error('Por favor ingresa el ID del modelo')
                   return
                 }
                 if (!newModel.displayName.trim()) {
                   toast.error('Por favor ingresa el nombre para mostrar')
                   return
                 }
                 if (newModel.inputCostPer1K < 0) {
                   toast.error('El costo de input debe ser positivo')
                   return
                 }
                 if (newModel.outputCostPer1K < 0) {
                   toast.error('El costo de output debe ser positivo')
                   return
                 }
                 if (newModel.maxTokens <= 0) {
                   toast.error('El máximo de tokens debe ser mayor a 0')
                   return
                 }

                 // Validar JSON de parámetros por defecto
                 try {
                   JSON.parse(JSON.stringify(newModel.defaultParams));
                 } catch (error) {
                   toast.error('Los parámetros por defecto deben ser un JSON válido')
                   return
                 }

                 if (editingModel) {
                   // Update existing model
                   handleUpdateModel(editingModel.id, newModel)
                 } else {
                   // Create new model
                   handleCreateModel(newModel)
                 }

                 // Reset form
                 setNewModel({
                   providerId: '',
                   modelId: '',
                   displayName: '',
                   capabilities: [],
                   inputCostPer1K: 0,
                   outputCostPer1K: 0,
                   maxTokens: 0,
                   defaultParams: {},
                   isActive: true
                 })
                 setEditingModel(null)
                 setShowCreateDialog(false)
               }}>
                 {editingModel ? 'Guardar Cambios' : 'Crear Modelo'}
               </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Buscar modelos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedProvider} onValueChange={setSelectedProvider}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                 <SelectItem value="all">Todos los proveedores</SelectItem>
                 {Array.isArray(providers) && providers.map(provider => (
                   <SelectItem key={provider.id} value={provider.id}>
                     {provider.displayName}
                   </SelectItem>
                 ))}
               </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabla de Modelos */}
      <Card>
         <CardHeader>
           <CardTitle>Modelos Disponibles</CardTitle>
           <CardDescription>
             {Array.isArray(filteredModels) ? filteredModels.length : 0} modelo(s) encontrado(s)
           </CardDescription>
         </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Modelo</TableHead>
                <TableHead>Proveedor</TableHead>
                <TableHead>Capacidades</TableHead>
                <TableHead>Costos</TableHead>
                <TableHead>Tokens Máx.</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Acciones</TableHead>
              </TableRow>
            </TableHeader>
             <TableBody>
               {Array.isArray(filteredModels) && filteredModels.map((model) => {
                 const provider = Array.isArray(providers) ? providers.find(p => p.id === model.providerId) : null
                 return (
                   <TableRow key={model.id}>
                     <TableCell>
                       <div>
                         <div className="font-medium">{model.displayName}</div>
                         <div className="text-sm text-muted-foreground">{model.modelId}</div>
                       </div>
                     </TableCell>
                     <TableCell>
                       <Badge variant="outline">{provider?.displayName || model.providerId}</Badge>
                     </TableCell>
                     <TableCell>
                       <div className="flex flex-wrap gap-1">
                         {Array.isArray(model.capabilities) && model.capabilities.map((capability) => (
                           <Badge
                             key={capability}
                             variant="secondary"
                             className={`text-xs ${getCapabilityColor(capability)}`}
                           >
                             {capability}
                           </Badge>
                         ))}
                       </div>
                     </TableCell>
                     <TableCell>
                       <div className="text-sm">
                         <div className="flex items-center gap-1">
                           <DollarSign className="h-3 w-3" />
                           <span>In: ${model.inputCostPer1K}</span>
                         </div>
                         <div className="flex items-center gap-1">
                           <DollarSign className="h-3 w-3" />
                           <span>Out: ${model.outputCostPer1K}</span>
                         </div>
                       </div>
                     </TableCell>
                     <TableCell>
                       <div className="flex items-center gap-1">
                         <Clock className="h-3 w-3" />
                         <span className="text-sm">{model.maxTokens.toLocaleString()}</span>
                       </div>
                     </TableCell>
                     <TableCell>
                       <div className="flex items-center space-x-2">
                         <Switch
                           checked={model.isActive}
                           onCheckedChange={(checked) => handleToggleActive(model.id, checked)}
                         />
                         <span className="text-sm">
                           {model.isActive ? 'Activo' : 'Inactivo'}
                         </span>
                       </div>
                     </TableCell>
                     <TableCell>
                       <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setEditingModel(model)
                              // Pre-fill the form with model data for editing
                              setNewModel({
                                providerId: model.providerId,
                                modelId: model.modelId,
                                displayName: model.displayName,
                                capabilities: Array.isArray(model.capabilities) ? model.capabilities : [],
                                inputCostPer1K: model.inputCostPer1K,
                                outputCostPer1K: model.outputCostPer1K,
                                maxTokens: model.maxTokens,
                                defaultParams: model.defaultParams,
                                isActive: model.isActive
                              })
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                         <Button
                           variant="ghost"
                           size="sm"
                           onClick={() => handleDeleteModel(model.id)}
                           className="text-destructive hover:text-destructive/80"
                         >
                           <Trash2 className="h-4 w-4" />
                         </Button>
                       </div>
                     </TableCell>
                   </TableRow>
                 )
               })}
             </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}