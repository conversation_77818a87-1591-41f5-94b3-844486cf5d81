"use client";

import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, FileSearch, Sparkles, ChevronRight, Bot } from 'lucide-react';
import { ProjectSelector } from '@/components/qaki/ProjectSelector';
import { WorkflowSelection } from '@/components/qaki/WorkflowSelection';
import { KnowledgeBaseWorkflow } from '@/components/qaki/KnowledgeBaseWorkflow';
import { UserStoryEnhancement } from '@/components/qaki/UserStoryEnhancement';
import { TestCaseGeneration } from '@/components/qaki/TestCaseGeneration';

export type WorkflowType = 'knowledge-base' | 'user-story' | 'test-cases' | null;

interface SelectedProject {
  id: string;
  name: string;
  description?: string;
}

export default function QAKIPage() {
  const [selectedProject, setSelectedProject] = useState<SelectedProject | null>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowType>(null);

  const handleProjectSelect = (project: SelectedProject) => {
    setSelectedProject(project);
    setSelectedWorkflow(null); // Reset workflow when project changes
  };

  const handleWorkflowSelect = (workflow: WorkflowType) => {
    setSelectedWorkflow(workflow);
  };

  const handleReset = () => {
    setSelectedProject(null);
    setSelectedWorkflow(null);
  };

  const renderWorkflowComponent = () => {
    if (!selectedProject || !selectedWorkflow) return null;

    switch (selectedWorkflow) {
      case 'knowledge-base':
        return <KnowledgeBaseWorkflow project={selectedProject} onBack={() => setSelectedWorkflow(null)} />;
      case 'user-story':
        return <UserStoryEnhancement project={selectedProject} onBack={() => setSelectedWorkflow(null)} />;
      case 'test-cases':
        return <TestCaseGeneration project={selectedProject} onBack={() => setSelectedWorkflow(null)} />;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-3 mb-8">
        <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg text-white">
          <Bot size={28} />
        </div>
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            QAKI Assistant
          </h1>
          <p className="text-muted-foreground">
            AI-powered testing assistant para gestión de Knowledge Base y generación de casos de prueba
          </p>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center gap-4 mb-8 p-4 bg-muted/30 rounded-lg">
        <div className="flex items-center gap-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            selectedProject ? 'bg-green-500 text-white' : 'bg-primary text-primary-foreground'
          }`}>
            1
          </div>
          <span className="font-medium">Seleccionar Proyecto</span>
          {selectedProject && (
            <Badge variant="secondary" className="ml-2">{selectedProject.name}</Badge>
          )}
        </div>

        <ChevronRight className="text-muted-foreground" size={16} />

        <div className="flex items-center gap-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            selectedWorkflow ? 'bg-green-500 text-white' : selectedProject ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            2
          </div>
          <span className={`font-medium ${selectedProject ? '' : 'text-muted-foreground'}`}>
            Elegir Flujo de Trabajo
          </span>
          {selectedWorkflow && (
            <Badge variant="secondary" className="ml-2">
              {selectedWorkflow === 'knowledge-base' && 'Knowledge Base'}
              {selectedWorkflow === 'user-story' && 'Mejora de Historias'}
              {selectedWorkflow === 'test-cases' && 'Generación de Tests'}
            </Badge>
          )}
        </div>

        <ChevronRight className="text-muted-foreground" size={16} />

        <div className="flex items-center gap-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            selectedProject && selectedWorkflow ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            3
          </div>
          <span className={`font-medium ${selectedProject && selectedWorkflow ? '' : 'text-muted-foreground'}`}>
            Ejecutar
          </span>
        </div>

        {(selectedProject || selectedWorkflow) && (
          <Button variant="ghost" size="sm" onClick={handleReset} className="ml-auto">
            Reiniciar
          </Button>
        )}
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {!selectedProject && (
          <ProjectSelector onProjectSelect={handleProjectSelect} />
        )}

        {selectedProject && !selectedWorkflow && (
          <WorkflowSelection onWorkflowSelect={handleWorkflowSelect} />
        )}

        {renderWorkflowComponent()}
      </div>


    </div>
  );
}