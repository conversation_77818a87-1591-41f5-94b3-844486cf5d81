"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Zap, 
  Plus, 
  X, 
  Sparkles, 
  FileText, 
  List,
  Globe,
  Tag,
  ChevronDown,
  ChevronRight,
  User
} from "lucide-react";
import { TestCaseCreateInput } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";
import { TestStepsEditor } from "./TestStepsEditor";

interface TestStep {
  id: string;
  text: string;
}

interface QuickTestCreatorProps {
  onSubmit: (data: TestCaseCreateInput) => Promise<void>;
  isSubmitting: boolean;
}

export function QuickTestCreator({ onSubmit, isSubmitting }: QuickTestCreatorProps) {
  const [name, setName] = useState("");
  const [url, setUrl] = useState("");
  const [content, setContent] = useState("");
  const [contentMode, setContentMode] = useState<"guided" | "freeform">("guided");
  const [testSteps, setTestSteps] = useState<TestStep[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  
  // Optional user story - hidden by default
  const [showUserStory, setShowUserStory] = useState(false);
  const [userStory, setUserStory] = useState("");
  
  const { toast } = useToast();

  // Bidirectional sync functions
  const syncGuidedToFreeText = () => {
    if (testSteps.length > 0) {
      const formattedText = testSteps.map((step, index) => `${index + 1}. ${step.text}`).join('\n');
      setContent(formattedText);
    }
  };

  const syncFreeTextToGuided = () => {
    if (content.trim()) {
      // Parse text into steps - handle both numbered and unnumbered lines
      const lines = content.trim().split('\n').filter(line => line.trim());
      const parsedSteps: TestStep[] = lines.map((line, index) => {
        // Remove existing numbering if present (e.g., "1. " or "1) " or "Step 1:")
        const cleanText = line.replace(/^\s*(\d+[\.\)]\s*|Step\s*\d+:\s*)/i, '').trim();
        return {
          id: `step-${Date.now()}-${index}`,
          text: cleanText || `Step ${index + 1}`
        };
      });
      setTestSteps(parsedSteps);
    }
  };

  const handleContentModeChange = (newMode: "guided" | "freeform") => {
    if (newMode === contentMode) return;
    
    if (newMode === "freeform") {
      // Switching from guided to free text
      syncGuidedToFreeText();
    } else {
      // Switching from free text to guided
      syncFreeTextToGuided();
    }
    
    setContentMode(newMode);
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      toast({
        title: "Name required",
        description: "Please enter a test case name",
        variant: "destructive"
      });
      return;
    }
    
    // Generate content based on mode
    let finalContent = "";
    if (contentMode === "guided") {
      if (testSteps.length === 0) {
        toast({
          title: "Steps required", 
          description: "Please add at least one test step",
          variant: "destructive"
        });
        return;
      }
      finalContent = testSteps.map((step, index) => `${index + 1}. ${step.text}`).join('\n');
    } else {
      if (!content.trim()) {
        toast({
          title: "Content required", 
          description: "Please enter test instructions or content",
          variant: "destructive"
        });
        return;
      }
      finalContent = content.trim();
    }

    const testData: TestCaseCreateInput = {
      name: name.trim(),
      description: `Quick test: ${name.trim()}`,
      instructions: finalContent,
      user_story: userStory.trim() || "",
      gherkin: "",
      url: url.trim() || "",
      tags: tags.length > 0 ? tags : ["quick-test"],
      priority: "Medium",
      type: "Functional",
    };

    try {
      await onSubmit(testData);
      // Reset form on success
      setName("");
      setUrl("");
      setContent("");
      setTestSteps([]);
      setUserStory("");
      setShowUserStory(false);
      setTags([]);
    } catch (error) {
      // Error handling is done by parent component
    }
  };

  const quickTemplates = [
    {
      name: "Login Test",
      steps: [
        { id: "1", text: "Navigate to login page" },
        { id: "2", text: "Enter valid credentials" },
        { id: "3", text: "Click login button" },
        { id: "4", text: "Verify successful login" }
      ],
      tags: ["login", "authentication"]
    },
    {
      name: "Navigation Test", 
      steps: [
        { id: "1", text: "Navigate to main page" },
        { id: "2", text: "Click on menu items" },
        { id: "3", text: "Verify pages load correctly" },
        { id: "4", text: "Check navigation consistency" }
      ],
      tags: ["navigation", "ui"]
    },
    {
      name: "Form Submission",
      steps: [
        { id: "1", text: "Fill out form with valid data" },
        { id: "2", text: "Submit form" },
        { id: "3", text: "Verify success message" },
        { id: "4", text: "Check data was saved" }
      ],
      tags: ["form", "data-entry"]
    }
  ];

  const useTemplate = (template: typeof quickTemplates[0]) => {
    setName(template.name);
    setTestSteps(template.steps);
    setTags(template.tags);
    setContentMode("guided");
  };

  return (
    <div className="space-y-6">
      {/* Quick Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Quick Templates
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Start with a template or create from scratch
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {quickTemplates.map((template, index) => (
              <Button
                key={index}
                variant="outline"
                className="h-auto p-4 text-left flex flex-col items-start"
                onClick={() => useTemplate(template)}
              >
                <div className="font-medium">{template.name}</div>
                <div className="text-xs text-muted-foreground mt-1">
                  {template.tags.join(", ")}
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Creator Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Quick Test Creator
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Create a test case in seconds - just name it and write the steps
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Test Name */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">Test Name</label>
              </div>
              <Input
                placeholder="e.g., User Login Test"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="text-base"
              />
            </div>

            {/* Optional URL */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">Starting URL (Optional)</label>
              </div>
              <Input
                placeholder="https://app.example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                type="url"
              />
            </div>

            {/* Optional User Story - Collapsible */}
            <div className="space-y-2">
              <button
                type="button"
                onClick={() => setShowUserStory(!showUserStory)}
                className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors group"
              >
                {showUserStory ? (
                  <ChevronDown className="h-4 w-4 group-hover:text-blue-600" />
                ) : (
                  <ChevronRight className="h-4 w-4 group-hover:text-blue-600" />
                )}
                <User className="h-4 w-4 group-hover:text-blue-600" />
                <span className="group-hover:text-blue-600">User Story</span>
                <span className="text-xs text-muted-foreground">(Optional)</span>
                {userStory.trim() && !showUserStory && (
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                    <span className="text-xs text-blue-600 font-medium">Added</span>
                  </div>
                )}
              </button>
              
              {showUserStory && (
                <div className="space-y-2 pl-6 border-l-2 border-muted">
                  <Textarea
                    placeholder="As a [user type], I want to [action] so that [benefit/goal]...&#10;&#10;Example:&#10;As a customer, I want to log into my account so that I can view my order history and track shipments."
                    value={userStory}
                    onChange={(e) => setUserStory(e.target.value)}
                    rows={4}
                    className="text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    💡 Describe the user goal and context that this test validates
                  </p>
                </div>
              )}
            </div>

            {/* Test Instructions */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">Test Content</label>
              </div>
              
              <Tabs value={contentMode} onValueChange={(value) => handleContentModeChange(value as "guided" | "freeform")}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="guided" className="flex items-center gap-2">
                    <List className="h-4 w-4" />
                    Guided Steps
                  </TabsTrigger>
                  <TabsTrigger value="freeform" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Free Text
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="guided" className="mt-4">
                  <TestStepsEditor 
                    steps={testSteps}
                    onChange={setTestSteps}
                  />
                  {testSteps.length > 0 && (
                    <p className="text-xs text-muted-foreground mt-3">
                      💡 Tip: Switch to "Free Text" to see your steps as formatted text
                    </p>
                  )}
                </TabsContent>
                
                <TabsContent value="freeform" className="mt-4">
                  <Textarea
                    placeholder="1. Navigate to login page&#10;2. Enter valid credentials&#10;3. Click login button&#10;4. Verify successful login&#10;&#10;You can write steps in any format - they'll be converted to guided steps when you switch tabs!"
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    rows={8}
                    className="text-sm"
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    💡 Tip: Switch to "Guided Steps" to see your text converted to interactive step cards
                  </p>
                </TabsContent>
              </Tabs>
            </div>

            {/* Tags */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <label className="text-sm font-medium">Tags (Optional)</label>
              </div>
              <div className="flex gap-2">
                <Input
                  placeholder="Add a tag"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag();
                    }
                  }}
                />
                <Button type="button" variant="outline" onClick={addTag}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 text-muted-foreground hover:text-destructive"
                        onClick={() => removeTag(tag)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Submit */}
            <Button 
              type="submit" 
              disabled={isSubmitting || !name.trim() || (contentMode === "guided" ? testSteps.length === 0 : !content.trim())}
              size="lg"
              className="w-full"
            >
              {isSubmitting ? (
                <>Creating Test Case...</>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Create Test Case
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export default QuickTestCreator;