services:
  api:
    image: ghcr.io/steel-dev/steel-browser-api:latest
    ports:
      - "3000:3000"
      - "9223:9223"
    environment:
      - DOMAIN=${DOMAIN:-localhost:3000}
      - CDP_DOMAIN=${CDP_DOMAIN:-localhost:9223}
    volumes:
      - ./.cache:/app/.cache
    networks:
      - steel-network

  ui:
    image: ghcr.io/steel-dev/steel-browser-ui:latest
    ports:
      - "5173:80"
    environment:
      - API_URL=${API_URL:-http://api:3000}
    depends_on:
      - api
    networks:
      - steel-network

networks:
  steel-network:
    name: steel-network
    driver: bridge
