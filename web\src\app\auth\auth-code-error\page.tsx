import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { AlertTriangle } from 'lucide-react'

export default function AuthCodeError() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-background">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2 text-destructive">
            <AlertTriangle className="h-6 w-6" />
            Error de Autenticación
          </CardTitle>
          <CardDescription>
            Hubo un problema al procesar tu solicitud de autenticación
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-sm text-muted-foreground">
            El enlace de verificación puede haber expirado o ya haber sido utilizado.
          </p>
          <Button asChild>
            <Link href="/login">
              Volver al Login
            </Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}