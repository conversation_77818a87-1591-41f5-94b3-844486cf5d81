-- Seed data for default superadmin user
-- This file creates a default superadmin user for the application

-- Insert default superadmin user into auth.users
-- Note: In production, this should be done through Supabase Auth API for proper password hashing
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role,
  aud,
  confirmation_token,
  email_change_token_new,
  recovery_token
) VALUES (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '00000000-0000-0000-0000-000000000000'::uuid,
  '<EMAIL>',
  crypt('caca12345', gen_salt('bf')), -- Password: caca12345
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"username": "superadmin", "full_name": "Super Administrator"}',
  false,
  'authenticated',
  'authenticated',
  '',
  '',
  ''
) ON CONFLICT (email) DO NOTHING;

-- Insert corresponding profile for the superadmin user
INSERT INTO public.profiles (
  id,
  email,
  username,
  first_name,
  last_name,
  full_name,
  role,
  email_verified,
  tenant_id,
  created_at,
  updated_at
) VALUES (
  '00000000-0000-0000-0000-000000000001'::uuid,
  '<EMAIL>',
  'superadmin',
  'Super',
  'Administrator',
  'Super Administrator',
  'SuperAdmin',
  true,
  null,
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  role = 'SuperAdmin',
  email_verified = true,
  updated_at = NOW();

-- Ensure the superadmin user has all necessary permissions
-- This is handled by RLS policies, but we can add any additional setup here if needed

-- Optional: Create a default project for the superadmin (uncomment if needed)
-- INSERT INTO public.projects (
--   project_id,
--   name,
--   description,
--   tags,
--   user_id,
--   created_at,
--   updated_at
-- ) VALUES (
--   uuid_generate_v4(),
--   'Default Project',
--   'Default project for superadmin user',
--   ARRAY['default', 'admin'],
--   '00000000-0000-0000-0000-000000000001'::uuid,
--   NOW(),
--   NOW()
-- ) ON CONFLICT DO NOTHING;