'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RefreshCw, Activity, Server, Database, Zap } from 'lucide-react'
import { toast } from 'sonner'
import { getApiHealth } from '@/lib/api'

interface HealthStatus {
  status: string
  timestamp: string
  services: {
    database: { status: string; responseTime?: number }
    redis: { status: string; responseTime?: number }
    workers: { status: string; activeWorkers?: number }
  }
}

interface SystemMetrics {
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  activeConnections: number
}

export default function MonitoringPage() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null)
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchHealthStatus = async () => {
    try {
      const data = await getApiHealth()
      setHealthStatus(data as any)
    } catch (error) {
      console.error('Error fetching health status:', error)
      // Datos de ejemplo en caso de error
      setHealthStatus({
        status: 'degraded',
        timestamp: new Date().toISOString(),
        services: {
          database: { status: 'healthy', responseTime: 45 },
          redis: { status: 'degraded', responseTime: 120 },
          workers: { status: 'healthy', activeWorkers: 2 }
        }
      })
    }
  }

  const fetchSystemMetrics = async () => {
    try {
      // Datos de ejemplo - en producción esto vendría de un endpoint real
      setSystemMetrics({
        cpuUsage: Math.floor(Math.random() * 100),
        memoryUsage: Math.floor(Math.random() * 100),
        diskUsage: Math.floor(Math.random() * 100),
        activeConnections: Math.floor(Math.random() * 50) + 10
      })
    } catch (error) {
      console.error('Error fetching system metrics:', error)
    }
  }

  const refreshData = async () => {
    setRefreshing(true)
    await Promise.all([fetchHealthStatus(), fetchSystemMetrics()])
    setRefreshing(false)
    toast.success('Datos actualizados')
  }

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([fetchHealthStatus(), fetchSystemMetrics()])
      setLoading(false)
    }
    
    loadData()
    
    // Auto-refresh cada 30 segundos
    const interval = setInterval(() => {
      fetchHealthStatus()
      fetchSystemMetrics()
    }, 30000)
    
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-accent'
      case 'degraded': return 'bg-warning'
      case 'unhealthy': return 'bg-destructive'
      default: return 'bg-muted-foreground'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy': return 'Saludable'
      case 'degraded': return 'Degradado'
      case 'unhealthy': return 'No saludable'
      default: return 'Desconocido'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Monitoreo del Sistema</h1>
          <p className="text-muted-foreground">
            Estado en tiempo real de los servicios y métricas del sistema
          </p>
        </div>
        <Button onClick={refreshData} disabled={refreshing} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Actualizar
        </Button>
      </div>

      {/* Estado General */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Estado General del Sistema
          </CardTitle>
          <CardDescription>
            Última actualización: {healthStatus ? new Date(healthStatus.timestamp).toLocaleString() : 'N/A'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(healthStatus?.status || 'unknown')}`}></div>
            <span className="font-medium">{getStatusText(healthStatus?.status || 'unknown')}</span>
          </div>
        </CardContent>
      </Card>

      {/* Servicios */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Base de Datos</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(healthStatus?.services.database.status || 'unknown')}`}></div>
              <span className="text-sm">{getStatusText(healthStatus?.services.database.status || 'unknown')}</span>
            </div>
            {healthStatus?.services.database.responseTime && (
              <p className="text-xs text-muted-foreground">
                Tiempo de respuesta: {healthStatus.services.database.responseTime}ms
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Redis</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(healthStatus?.services.redis.status || 'unknown')}`}></div>
              <span className="text-sm">{getStatusText(healthStatus?.services.redis.status || 'unknown')}</span>
            </div>
            {healthStatus?.services.redis.responseTime && (
              <p className="text-xs text-muted-foreground">
                Tiempo de respuesta: {healthStatus.services.redis.responseTime}ms
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Workers</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 mb-2">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(healthStatus?.services.workers.status || 'unknown')}`}></div>
              <span className="text-sm">{getStatusText(healthStatus?.services.workers.status || 'unknown')}</span>
            </div>
            {healthStatus?.services.workers.activeWorkers && (
              <p className="text-xs text-muted-foreground">
                Workers activos: {healthStatus.services.workers.activeWorkers}
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Métricas del Sistema */}
      <Card>
        <CardHeader>
          <CardTitle>Métricas del Sistema</CardTitle>
          <CardDescription>Uso de recursos en tiempo real</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">CPU</span>
                <span className="text-sm text-muted-foreground">{systemMetrics?.cpuUsage || 0}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${systemMetrics?.cpuUsage || 0}%` }}
                ></div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Memoria</span>
                <span className="text-sm text-muted-foreground">{systemMetrics?.memoryUsage || 0}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-accent h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${systemMetrics?.memoryUsage || 0}%` }}
                ></div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Disco</span>
                <span className="text-sm text-muted-foreground">{systemMetrics?.diskUsage || 0}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="bg-warning h-2 rounded-full transition-all duration-300" 
                  style={{ width: `${systemMetrics?.diskUsage || 0}%` }}
                ></div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Conexiones</span>
                <span className="text-sm text-muted-foreground">{systemMetrics?.activeConnections || 0}</span>
              </div>
              <div className="flex items-center">
                <Badge variant="outline">{systemMetrics?.activeConnections || 0} activas</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}