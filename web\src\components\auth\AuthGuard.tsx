'use client';

import { useAuth } from '@/contexts/AuthContextSupabase';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function AuthGuard({ children, fallback }: AuthGuardProps) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  useEffect(() => {
    console.log('AuthGuard: loading =', loading, 'user =', !!user);

    // Wait a bit before checking auth to avoid race conditions
    if (!loading) {
      const timer = setTimeout(() => {
        setHasCheckedAuth(true);
        if (!user) {
          console.log('AuthGuard: Redirecting to login - user not authenticated');
          router.push('/login');
        }
      }, 1000); // 1 second delay

      return () => clearTimeout(timer);
    }
  }, [user, loading, router]);

  // Show loading state while checking authentication
  if (loading || !hasCheckedAuth) {
    return fallback || <div className="flex items-center justify-center min-h-screen">Cargando...</div>;
  }

  // Show content only if user is authenticated
  if (!user) {
    return fallback || <div className="flex items-center justify-center min-h-screen">Redirigiendo...</div>;
  }

  return <>{children}</>;
}