import { API_BASE_URL } from '@/lib/config';
import { AuthService } from '@/lib/auth';
import { TenantService } from '@/lib/tenant';
import { getProjectById } from '@/lib/api/projects';
import { getProjectEnvironments } from '@/lib/api/projects/environments';

// Re-exports from modular API files
export * from './api/scripts';
export * from './api/codegen';
export * from './api/admin/tenants';

// Specialized function for external API calls (bypasses CORS proxy)
async function fetchExternalApi<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  });

  if (!response.ok) {
    let errorData;
    let errorMessage = `External API error! status: ${response.status} for URL: ${url}`;
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        errorData = await response.json();
        errorMessage = errorData?.error || errorData?.message || errorMessage;
      } else {
        const errorText = await response.text();
        errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;
      }
    } catch (e) {
      // If parsing fails, stick with the basic error
      errorMessage = `${response.statusText || `External API error! status: ${response.status} for URL: ${url}`}`;
    }
    throw new Error(errorMessage);
  }

  // Handle cases where response is OK but content might be empty
  if (response.status === 204) {
    return {} as T;
  }

  return response.json();
}

// External API functions - funciones migradas a /api/integration/

// Linear API function (goes through our proxy)
export const callLinearApi = async (endpoint: string, options?: RequestInit): Promise<any> => {
  return fetchApi(`/v1/linear${endpoint}`, options);
};

// ==========================================
// ANALYTICS API - MIGRATED TO utils/analytics.ts
// ==========================================
// All analytics functions have been moved to utils/analytics.ts
// Import from '@/lib/api/utils' or '@/lib/api' instead
import type {
  ApiHealth,
  ApiResponse,
  CodegenExecutionInfo,
  CodegenExecutionListResponse,
  CodegenExecutionRequest,
  CodegenExecutionResponse,
  CodegenHealthResponse,
  CodegenHistoryResponse,
  CodegenHistorySessionDetailResponse,
  CodegenSessionInfo,
  CodegenSessionListResponse,
  CodegenStatsResponse,
  CodegenTestCaseRequest,
  ComputedVariable,
  EnhanceUserStoryInput, EnhanceUserStoryOutput,
  Environment, EnvironmentCreateInput, EnvironmentUpdateInput,
  ExecuteSmokeTestOutput,
  ExecutionRequest,
  ExecutionResponse as ExecutionResponseV2,
  GenerateCodeInput, GenerateCodeOutput,
  GenerateGherkinInput, GenerateGherkinOutput,
  GenerateManualTestCasesInput, GenerateManualTestCasesOutput,
  PlaywrightCodegenRequest,
  Project, ProjectCreateInput, ProjectUpdateInput,
  ProjectVariable, ProjectVariableCreateInput, ProjectVariableUpdateInput,
  StandardResult,
  SuiteVariable, SuiteVariableCreateInput, SuiteVariableUpdateInput,
  SummarizeTestResultsInput, SummarizeTestResultsOutput,
  TestCase, TestCaseCreateInput,
  TestCaseStatusUpdateInput,
  TestCaseUpdateInput,
  TestExecutionHistoryData,
  TestSuite, TestSuiteCreateInput, TestSuiteUpdateInput,
  V2ExecutionResponse,
  ExecutionStatusResponse
} from '@/lib/types';


async function fetchApi<T>(url: string, options?: RequestInit): Promise<T> {
  // Check if this is an auth endpoint to avoid circular dependency
  const isAuthEndpoint = url.includes('/auth/');
  
  // Skip auth headers only for specific testing endpoints if needed
  const isTestingEndpoint = false; // Removed projects exclusion to fix JWT authentication
  
  // Determine the full URL - handle /api prefix correctly
  let fullUrl: string;
  if (API_BASE_URL) {
    // If API_BASE_URL is provided and url doesn't start with /api, add it
    if (url.startsWith('/api/')) {
      fullUrl = `${API_BASE_URL}${url}`; // URL already has /api prefix
    } else {
      fullUrl = `${API_BASE_URL}/api${url}`; // Add /api prefix
    }
  } else {
    // Local development - always ensure /api prefix
    fullUrl = url.startsWith('/api/') ? url : `/api${url}`;
  }
  
  const response = await fetch(fullUrl, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': 'true', // 🔧 Agregar header para evitar bloqueo de ngrok
      ...(isAuthEndpoint || isTestingEndpoint ? {} : AuthService.getAuthHeaders()), // Skip auth headers for testing endpoints
      ...(options?.headers),
    },
  });

  // Check for 401 unauthorized and redirect to login - but skip for testing endpoints
  if (response.status === 401 && !isAuthEndpoint && !isTestingEndpoint) {
    AuthService.logout();
    window.location.href = '/login';
    throw new Error('Authentication required');
  }

  if (!response.ok) {
    let errorData;
    let errorMessage = `HTTP error! status: ${response.status} for URL: ${url}`;
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        errorData = await response.json();
        errorMessage = errorData?.error || errorData?.details || errorMessage;
      } else {
        const errorText = await response.text();
        // If HTML error page, include a snippet.
        if (errorText.toLowerCase().includes("<!doctype html")) {
          errorMessage = `${errorMessage}. Server returned an HTML error page. Snippet: ${errorText.substring(0, 200)}...`;
        } else {
          errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;
        }
      }
    } catch (e) {
      // If parsing as JSON or text fails, stick with the basic error from statusText or status code.
      errorMessage = `${response.statusText || `HTTP error! status: ${response.status} for URL: ${url}`}. Could not parse error response.`;
    }
    throw new Error(errorMessage);
  }
  // Handle cases where response is OK but content might be empty for 204 No Content
  if (response.status === 204) {
    return {} as T; // Or handle as appropriate for your application
  }
  return response.json();
}

// Project Endpoints - funciones migradas a /api/projects/





// Test Suite Endpoints - funciones migradas a /api/projects/suites
// Helper function to map frontend TestCase format to backend format
const mapTestCaseToBackendFormat = (frontendData: TestCaseCreateInput | TestCaseUpdateInput) => {
  const { user_story, instructions, ...rest } = frontendData;
  return {
    ...rest,
    historiaDeUsuario: user_story,  // Map frontend user_story to backend historiaDeUsuario
    instructions: instructions             // Keep instructions as-is (same in both frontend and backend)
  };
};

// Test Case Endpoints - funciones migradas a /api/projects/testCases

// ==========================================
// LEGACY EXECUTION API - MIGRATED TO execution/v1.ts AND execution/history.ts 
// ==========================================
// Legacy execution functions have been moved to appropriate modules:
// - executeTest, getApiHealth, getTestExecutionHistoryDetails -> execution/v1.ts
// - getTestExecutionHistory -> execution/history.ts
// Import from '@/lib/api/execution' or '@/lib/api' instead


// AI Tool Endpoints - funciones migradas a /api/ai/



// ==========================================
// CONFIGURATION API - MIGRATED TO utils/config.ts
// ==========================================
// All configuration functions have been moved to utils/config.ts
// Import from '@/lib/api/utils' or '@/lib/api' instead

// ==========================================
// PROJECT IMPORT/EXPORT - MIGRATED TO projects/projects.ts
// ==========================================
// Project import/export functions have been moved to projects/projects.ts
// Import from '@/lib/api/projects' or '@/lib/api' instead

// ==========================================
// KNOWLEDGE BASE API - MIGRATED TO integration/knowledgeBase.ts
// ==========================================
// All knowledge base functions have been moved to integration/knowledgeBase.ts
// Import from '@/lib/api/integration' or '@/lib/api' instead

// ==========================================
// Linear Integration API Functions
// ==========================================
// LINEAR INTEGRATION - MIGRATED TO integration/linear.ts
// ==========================================
// All Linear integration functions have been moved to integration/linear.ts
// Import from '@/lib/api/integration' or '@/lib/api' instead



// ==========================================
// ANALYTICS API - MIGRATED TO utils/analytics.ts
// ==========================================
// All analytics functions have been moved to utils/analytics.ts
// Import from '@/lib/api/utils' or '@/lib/api' instead

// Analytics Response Type (kept for compatibility)
export type AnalyticsResponse = {
  totalTests: number;
  testsRun: number;
  passRate: number; // 0-1
  avgDuration: number; // seconds
  dailyExecutions: Array<{
    date: string;
    passed: number;
    failed: number;
    total: number;
  }>;
  suiteStats: Array<{
    suiteId: string;
    suiteName: string;
    // Lifetime stats from summary
    lifetimeTests: number;
    lifetimeRuns: number;
    lifetimePassed: number;
    lifetimeFailed: number;
    lifetimeSuccessRate: number; // 0-1
    lastExecutionAt: string | null;
    // Date-range specific stats
    runsInDateRange: number;
    passedInDateRange: number;
  }>;
  testDetails?: Array<{
    testId: string;
    testName: string;
    suiteName: string;
    suiteId: string;
    projectId: string;
    status: string;
    lastExecution: string;
    duration?: number; // seconds - new field from v2
  }>;
  // New fields from analytics-v2 endpoint
  dataSource?: string; // Indicates data source ("ExecutionRepository")
  dateRange?: {
    start: string;
    end: string;
  };
};

// ==========================================

// Test History

// Test History

// ==========================================
// V2 EXECUTION API - MIGRATED TO execution/v2.ts
// ==========================================
// All V2 execution functions have been moved to execution/v2.ts
// Import from '@/lib/api/execution' or '@/lib/api' instead

// ==========================================
// COMPUTED VARIABLES - MIGRATED TO projects/variables.ts
// ==========================================
// getComputedVariables function should be moved to projects/variables.ts

// ==========================================
// COMPUTED VARIABLES - MIGRATED TO projects/variables.ts
// ==========================================
// getComputedVariables function has been moved to projects/variables.ts
// Import from '@/lib/api/projects' or '@/lib/api' instead

// ============================================================================
// MODULAR API RE-EXPORTS
// ============================================================================
// All functions have been organized into domain-specific modules
// These re-exports provide backward compatibility and centralized access

// Core API functionality
export * from './api/core/fetch';

// Project management
export * from './api/projects';

// Test execution 
export * from './api/execution';

// AI-powered features
export * from './api/ai';

// External integrations  
export * from './api/integration';

// Administration and configuration
export * from './api/admin';

// Utilities and configuration
export * from './api/utils';




