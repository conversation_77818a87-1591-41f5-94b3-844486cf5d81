import { API_BASE_URL } from '@/lib/config';
import { AuthService } from '@/lib/auth';

/**
 * Core API Fetch Module
 * 
 * Provides centralized HTTP request handling with:
 * - Automatic authentication header injection
 * - Error handling and response parsing
 * - Support for external APIs (bypassing CORS proxy)
 * - Consistent URL building for local and production environments
 */

/**
 * Specialized function for external API calls (bypasses CORS proxy)
 * 
 * Use this function when calling third-party APIs that don't go through our backend.
 * Examples: OpenAI API, external webhooks, etc.
 * 
 * @param url - Full URL to the external API
 * @param options - Standard fetch options
 * @returns Promise with parsed JSON response
 * 
 * @example
 * ```typescript
 * const response = await fetchExternalApi<OpenAIResponse>('https://api.openai.com/v1/chat/completions', {
 *   method: 'POST',
 *   headers: { 'Authorization': 'Bearer your-key' },
 *   body: JSON.stringify({ model: 'gpt-3.5-turbo', messages })
 * });
 * ```
 */
export async function fetchExternalApi<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options?.headers,
    },
  });

  if (!response.ok) {
    let errorData;
    let errorMessage = `External API error! status: ${response.status} for URL: ${url}`;
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        errorData = await response.json();
        errorMessage = errorData?.error || errorData?.message || errorMessage;
      } else {
        const errorText = await response.text();
        errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;
      }
    } catch (e) {
      // If parsing fails, stick with the basic error
      errorMessage = `${response.statusText || `External API error! status: ${response.status} for URL: ${url}`}`;
    }
    throw new Error(errorMessage);
  }

  // Handle cases where response is OK but content might be empty
  if (response.status === 204) {
    return {} as T;
  }

  return response.json();
}

/**
 * Main API fetch function for internal backend calls
 * 
 * This is the primary function for communicating with our QAK backend.
 * It handles:
 * - Automatic authentication (JWT tokens)
 * - URL building (local development vs production)
 * - Error handling and 401 redirects
 * - Response parsing with proper error messages
 * 
 * @param url - API endpoint (e.g., '/projects' or '/v2/tests/execute')
 * @param options - Standard fetch options
 * @returns Promise with parsed JSON response
 * 
 * @example
 * ```typescript
 * // GET request
 * const projects = await fetchApi<Project[]>('/projects');
 * 
 * // POST request with body
 * const newProject = await fetchApi<Project>('/projects', {
 *   method: 'POST',
 *   body: JSON.stringify({ name: 'My Project' })
 * });
 * ```
 */
export async function fetchApi<T>(url: string, options?: RequestInit): Promise<T> {
  // Check if this is an auth endpoint to avoid circular dependency
  const isAuthEndpoint = url.includes('/auth/');
  
  // Skip auth headers only for specific testing endpoints if needed
  const isTestingEndpoint = false; // Removed projects exclusion to fix JWT authentication
  
  // Determine the full URL - handle /api prefix correctly
  let fullUrl: string;
  if (API_BASE_URL) {
    // If API_BASE_URL is provided and url doesn't start with /api, add it
    if (url.startsWith('/api/')) {
      fullUrl = `${API_BASE_URL}${url}`; // URL already has /api prefix
    } else {
      fullUrl = `${API_BASE_URL}/api${url}`; // Add /api prefix
    }
  } else {
    // Local development - always ensure /api prefix
    fullUrl = url.startsWith('/api/') ? url : `/api${url}`;
  }
  
  const response = await fetch(fullUrl, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': 'true', // 🔧 Agregar header para evitar bloqueo de ngrok
      ...(isAuthEndpoint || isTestingEndpoint ? {} : AuthService.getAuthHeaders()), // Skip auth headers for testing endpoints
      ...(options?.headers),
    },
  });

  // Check for 401 unauthorized and redirect to login - but skip for testing endpoints
  if (response.status === 401 && !isAuthEndpoint && !isTestingEndpoint) {
    AuthService.logout();
    window.location.href = '/login';
    throw new Error('Authentication required');
  }

  if (!response.ok) {
    let errorData;
    let errorMessage = `HTTP error! status: ${response.status} for URL: ${url}`;
    try {
      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        errorData = await response.json();
        errorMessage = errorData?.error || errorData?.details || errorMessage;
      } else {
        const errorText = await response.text();
        // If HTML error page, include a snippet.
        if (errorText.toLowerCase().includes("<!doctype html")) {
          errorMessage = `${errorMessage}. Server returned an HTML error page. Snippet: ${errorText.substring(0, 200)}...`;
        } else {
          errorMessage = `${errorMessage}. Response: ${errorText.substring(0, 500)}${errorText.length > 500 ? '...' : ''}`;
        }
      }
    } catch (e) {
      // If parsing as JSON or text fails, stick with the basic error from statusText or status code.
      errorMessage = `${response.statusText || `HTTP error! status: ${response.status} for URL: ${url}`}. Could not parse error response.`;
    }
    throw new Error(errorMessage);
  }
  
  // Handle cases where response is OK but content might be empty for 204 No Content
  if (response.status === 204) {
    return {} as T; // Or handle as appropriate for your application
  }
  
  return response.json();
}

/**
 * Helper function for file upload requests
 * 
 * Use this for multipart/form-data requests (file uploads).
 * It automatically handles authentication headers but skips Content-Type
 * to let the browser set the correct boundary for FormData.
 * 
 * @param url - API endpoint
 * @param formData - FormData object with files and other data
 * @returns Promise with parsed JSON response
 * 
 * @example
 * ```typescript
 * const formData = new FormData();
 * formData.append('file', file);
 * formData.append('tenant_id', tenantId);
 * 
 * const result = await fetchApiWithFormData('/knowledge-base/documents/upload', formData);
 * ```
 */
export async function fetchApiWithFormData<T>(url: string, formData: FormData): Promise<T> {
  const fullUrl = url.startsWith('/api/') ? url : `/api${url}`;
  
  const response = await fetch(fullUrl, {
    method: 'POST',
    body: formData,
    headers: {
      'ngrok-skip-browser-warning': 'true',
      ...AuthService.getAuthHeaders(),
      // Don't set Content-Type for FormData - let browser handle it
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Upload failed: ${response.status} ${errorText}`);
  }

  return response.json();
}