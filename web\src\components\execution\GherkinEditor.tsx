"use client";

import { useState, useEffect, useRef } from "react";
import { Gherkin<PERSON>ighlighter } from "./GherkinHighlighter";
import { Button } from "@/components/ui/button";
import { Eye, Edit3 } from "lucide-react";

interface GherkinEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  readOnly?: boolean;
}

export function GherkinEditor({ 
  value, 
  onChange, 
  placeholder = "Feature: User Login...", 
  className = "",
  readOnly = false 
}: GherkinEditorProps) {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-adjust textarea height based on content
  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const minHeight = 200; // Minimum height in pixels
      const maxHeight = 600; // Maximum height in pixels
      const newHeight = Math.max(minHeight, Math.min(maxHeight, scrollHeight));
      textarea.style.height = `${newHeight}px`;
    }
  };

  useEffect(() => {
    adjustHeight();
  }, [value]);

  useEffect(() => {
    // Auto-switch to preview mode when content is generated (not empty and user hasn't manually edited)
    if (value && value.trim().length > 50 && !isPreviewMode) {
      setIsPreviewMode(true);
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const toggleMode = () => {
    setIsPreviewMode(!isPreviewMode);
  };

  return (
    <div className={`relative border rounded-md bg-background ${className}`}>
      {/* Mode Toggle Button */}
      <div className="absolute top-2 right-2 z-10">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleMode}
          className="text-xs opacity-70 hover:opacity-100"
          disabled={readOnly}
        >
          {isPreviewMode ? (
            <>
              <Edit3 className="w-3 h-3 mr-1" />
              Edit
            </>
          ) : (
            <>
              <Eye className="w-3 h-3 mr-1" />
              Preview
            </>
          )}
        </Button>
      </div>

      {isPreviewMode && value ? (
        /* Preview Mode with Syntax Highlighting */
        <div className="min-h-[200px] max-h-[600px] overflow-y-auto p-3 font-mono text-sm bg-muted/30">
          <GherkinHighlighter gherkinCode={value} />
        </div>
      ) : (
        /* Edit Mode */
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleInputChange}
          placeholder={placeholder}
          readOnly={readOnly}
          className="w-full p-3 bg-transparent border-none resize-none focus:outline-none font-mono text-sm min-h-[200px] max-h-[600px]"
          style={{
            lineHeight: '1.5',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace'
          }}
        />
      )}
      
      {/* Character count and mode indicator */}
      <div className="flex justify-between items-center px-3 py-1 bg-muted/50 border-t text-xs text-muted-foreground">
        <span>{value.length} characters</span>
        <span>{isPreviewMode ? "Preview Mode" : "Edit Mode"}</span>
      </div>
    </div>
  );
}
