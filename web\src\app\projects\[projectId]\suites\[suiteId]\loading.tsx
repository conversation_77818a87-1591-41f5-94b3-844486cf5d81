"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";

export default function SuiteDetailLoading() {
  return (
    <div>
      <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
      
      <div className="flex justify-between items-start mb-4">
        <div>
          <Skeleton className="h-10 w-64 mb-1" /> {/* Suite Name */}
          <Skeleton className="h-5 w-80 mb-3" /> {/* Suite Description */}
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
          <Skeleton className="h-4 w-72" /> {/* Dates */}
        </div>
        <div className="flex flex-col items-end gap-2">
            <div className="flex gap-2">
                <Skeleton className="h-9 w-24" /> {/* Edit button */}
                <Skeleton className="h-9 w-28" /> {/* Delete button */}
            </div>
            <Skeleton className="h-9 w-36" /> {/* Execute Suite button */}
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
            <Skeleton className="h-6 w-32" /> {/* Suite Progress Title */}
        </CardHeader>
        <CardContent>
            <div className="flex justify-between text-sm text-muted-foreground mb-1">
                <Skeleton className="h-4 w-36" />
                <Skeleton className="h-4 w-12" />
            </div>
            <Skeleton className="h-4 w-full" /> {/* Progress Bar */}
        </CardContent>
      </Card>
      
      <hr className="my-6"/>

      <div className="flex justify-between items-center mb-6">
         <Skeleton className="h-8 w-48" /> {/* Test Cases Header */}
         <Skeleton className="h-9 w-40" /> {/* Create Test Case Button */}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <Skeleton className="h-6 w-3/5 mb-1" />
                <Skeleton className="h-5 w-20 rounded-full" />
              </div>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6 mt-1" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-1/4 mb-2" />
              <div className="flex gap-1 mt-1">
                <Skeleton className="h-5 w-12 rounded-full" />
              </div>
              <Skeleton className="h-3 w-1/2 mt-3" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-9 w-full" />
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
