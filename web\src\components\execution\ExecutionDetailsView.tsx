"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ResultsTab } from "./ResultsTab";
import { DetailsTab } from "./DetailsTab";
import { ElementsTab } from "./ElementsTab";
import { UrlsTab } from "./UrlsTab";
import { CapturesTab } from "./CapturesTab";
import { MetadataTab } from "./MetadataTab";
import { TestCasesTab } from "./TestCasesTab";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

interface ExecutionDetailsViewProps {
  historyData: TestExecutionHistoryData;
  testCaseId: string; // Original TestCase ID from URL or parent
}

export function ExecutionDetailsView({ historyData, testCaseId }: ExecutionDetailsViewProps) {
  return (
    <div className="space-y-3 sm:space-y-4">
      <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-foreground break-words">Execution Details</h3>
      <Tabs defaultValue="results" className="w-full">
        <ScrollArea className="w-full whitespace-nowrap rounded-md border">
          <TabsList className="bg-card p-1 sm:p-2 h-auto flex-nowrap">
            <TabsTrigger value="results" className="text-xs sm:text-sm px-2 sm:px-4 py-1.5 sm:py-2 whitespace-nowrap">Results</TabsTrigger>
            <TabsTrigger value="details" className="text-xs sm:text-sm px-2 sm:px-4 py-1.5 sm:py-2 whitespace-nowrap">Details</TabsTrigger>
            <TabsTrigger value="elements" className="text-xs sm:text-sm px-2 sm:px-4 py-1.5 sm:py-2 whitespace-nowrap">Elements</TabsTrigger>
            <TabsTrigger value="urls" className="text-xs sm:text-sm px-2 sm:px-4 py-1.5 sm:py-2 whitespace-nowrap">URLs</TabsTrigger>
            <TabsTrigger value="captures" className="text-xs sm:text-sm px-2 sm:px-4 py-1.5 sm:py-2 whitespace-nowrap">Captures</TabsTrigger>
            <TabsTrigger value="testcases" className="text-xs sm:text-sm px-2 sm:px-4 py-1.5 sm:py-2 whitespace-nowrap">Test Cases</TabsTrigger>
            <TabsTrigger value="metadata" className="text-xs sm:text-sm px-2 sm:px-4 py-1.5 sm:py-2 whitespace-nowrap">Metadata</TabsTrigger>
          </TabsList>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        <div className="mt-3 sm:mt-4 p-0.5 sm:p-1 rounded-lg bg-card border">
            <TabsContent value="results" className="mt-0 p-3 sm:p-4 fade-in">
              <ResultsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="details" className="mt-0 p-3 sm:p-4 fade-in">
              <DetailsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="elements" className="mt-0 p-3 sm:p-4 fade-in">
              <ElementsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="urls" className="mt-0 p-3 sm:p-4 fade-in">
              <UrlsTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="captures" className="mt-0 p-3 sm:p-4 fade-in">
              <CapturesTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="testcases" className="mt-0 p-3 sm:p-4 fade-in">
              <TestCasesTab historyData={historyData} />
            </TabsContent>
            <TabsContent value="metadata" className="mt-0 p-3 sm:p-4 fade-in">
              <MetadataTab historyData={historyData} testIdFromUrl={testCaseId} />
            </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
