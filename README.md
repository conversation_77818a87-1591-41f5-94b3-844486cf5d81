# QAK - Quality Assurance Kit

Sistema completo de automatización de pruebas con IA, construido con Next.js, Supabase y browser automation.

## 🚀 Características

- **Autenticación Completa**: Login con email/password y Google OAuth
- **Gestión de Proyectos**: Organización de test suites y test cases
- **Browser Automation**: Ejecución automática de pruebas con IA (OpenAI y Gemini)
- **Arquitectura Dockerizada**: Despliegue completo con Docker Compose
- **Base de Datos Supabase**: PostgreSQL con Row Level Security
- **UI Moderna**: Interfaz construida con Next.js y Tailwind CSS

## 🏗️ Arquitectura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js Web   │    │ Supabase Cloud  │    │ Browser Auto API│
│   (Port 3000)   │◄──►│  (Hosted SaaS)  │    │   (Port 8080)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐             ┌────▼────┐             ┌────▼────┐
    │ React   │             │PostgreSQL│             │ Steel   │
    │Components│             │Auth/REST │             │ Browser │
    │Tailwind │             │Realtime  │             │ Chrome  │
    └─────────┘             └─────────┘             └─────────┘
```

## 🛠️ Configuración Rápida

### 1. Clonar el Repositorio

```bash
git clone <repository-url>
cd persoqak
```

### 2. Configurar Variables de Entorno

```bash
cp .env.example .env
```

Editar `.env` con tus credenciales:

```env
# Supabase Cloud (requerido)
SUPABASE_URL=https://qekegvxuykuuokipfple.supabase.co
ANON_KEY=tu_anon_key_de_supabase
SERVICE_ROLE_KEY=tu_service_role_key_de_supabase

# Configuración de IA (requerido al menos uno)
OPENAI_API_KEY=tu_clave_de_openai_aqui
GEMINI_API_KEY=tu_clave_de_gemini_aqui

# Proveedor de IA por defecto (openai o gemini)
DEFAULT_LLM_PROVIDER=openai

# Opcional: Google OAuth
GOOGLE_CLIENT_ID=tu_google_client_id
GOOGLE_CLIENT_SECRET=tu_google_client_secret
```

### 3. Levantar Todo el Stack

```bash
docker-compose up -d
```

### 4. Acceder a la Aplicación

- **Aplicación Web**: http://localhost:3000
- **Browser Automation**: http://localhost:8080
- **Steel Browser UI**: http://localhost:5173 (opcional)

## 📋 Servicios Incluidos

### Core Services
- **web-app**: Aplicación Next.js principal
- **browser-automation-api**: API Python para automatización
- **steel-browser**: Navegador Chrome controlado remotamente

### Supabase Cloud
- **Base de datos PostgreSQL**: Hospedada en Supabase
- **Autenticación**: Servicio de auth integrado
- **API REST**: Generada automáticamente
- **Realtime**: WebSocket para tiempo real
- **Storage**: Almacenamiento de archivos

## 🔧 Desarrollo Local

### Configuración Inicial

1. **Configurar Supabase Cloud**:
   - Crear cuenta en [Supabase](https://supabase.com)
   - Crear nuevo proyecto
   - Obtener URL del proyecto y claves API
   - Configurar variables de entorno en `.env`

2. **Levantar servicios locales**:
```bash
docker-compose up -d
```

3. **Desarrollo de la aplicación web**:
```bash
cd web
npm run dev
```

### Comandos Útiles

```bash
# Ver logs de todos los servicios
docker-compose logs -f

# Reiniciar un servicio específico
docker-compose restart web-app

# Parar todos los servicios
docker-compose down
```

### Requisitos
- Docker y Docker Compose
- Node.js 18+ (para desarrollo sin Docker)
- Python 3.11+ (para desarrollo sin Docker)

### Desarrollo con Docker (Recomendado)

```bash
# Levantar todos los servicios
docker-compose up -d

# Ver logs de un servicio específico
docker-compose logs -f web-app

# Reconstruir un servicio
docker-compose build web-app
docker-compose up -d web-app

# Parar todos los servicios
docker-compose down
```

### Desarrollo Sin Docker

```bash
# Terminal 1: Configurar variables de entorno
cp .env.example .env
# Editar .env con credenciales de Supabase Cloud

# Terminal 2: Steel Browser
cd browser
docker-compose up -d

# Terminal 3: Python API
cd browser
pip install -e .
python api_server.py

# Terminal 4: Next.js
cd web
npm install
npm run dev
```

## 🎯 Uso del Sistema

### 1. Crear Cuenta
- Visita http://localhost:3000
- Regístrate con email o usa "Continuar con Google"

### 2. Crear Proyecto
- Crea un nuevo proyecto de pruebas
- Configura entornos y variables

### 3. Crear Test Cases
- Agrega test suites y test cases
- Define instrucciones y user stories

### 4. Ejecutar con IA
- Ve a cualquier test case
- Haz clic en "Execute with Browser"
- El agente IA ejecutará las pruebas automáticamente

## 🔐 Configuración de Google OAuth

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la API de Google+
4. Crea credenciales OAuth 2.0:
   - **Authorized JavaScript origins**: `http://localhost:3000`
   - **Authorized redirect URIs**: `https://qekegvxuykuuokipfple.supabase.co/auth/v1/callback`
5. Copia Client ID y Client Secret al archivo `.env`

## 🧪 Configuración de IA

### OpenAI (Recomendado)
1. Ve a [OpenAI Platform](https://platform.openai.com/api-keys)
2. Crea una nueva API key
3. Copia la clave al archivo `.env` como `OPENAI_API_KEY`

### Gemini AI (Alternativo)
1. Ve a [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Crea una nueva API key
3. Copia la clave al archivo `.env` como `GEMINI_API_KEY`

Puedes configurar ambos proveedores y cambiar entre ellos usando `DEFAULT_LLM_PROVIDER`

## 📊 Base de Datos

### Esquema Principal
- **profiles**: Perfiles de usuario
- **projects**: Proyectos de pruebas
- **test_suites**: Conjuntos de pruebas
- **test_cases**: Casos de prueba individuales
- **environments**: Entornos de ejecución
- **project_variables**: Variables por proyecto

### Migraciones
Las migraciones se ejecutan automáticamente al iniciar la base de datos.

## 🐛 Solución de Problemas

### Error: "Browser automation service is not running"
```bash
# Verificar que el servicio esté ejecutándose
docker-compose ps browser-automation-api

# Ver logs del servicio
docker-compose logs browser-automation-api

# Reiniciar el servicio
docker-compose restart browser-automation-api
```

### Error: "No LLM provider available"
```bash
# Verificar variables de entorno
docker-compose exec browser-automation-api env | grep -E "OPENAI|GEMINI"

# Asegurarse de que al menos un proveedor esté configurado en .env
# Actualizar .env y reiniciar
docker-compose down
docker-compose up -d
```

### Error de conexión a base de datos
```bash
# Verificar configuración de Supabase Cloud
# Revisar variables de entorno en .env:
# - SUPABASE_URL
# - ANON_KEY  
# - SERVICE_ROLE_KEY

# Verificar conectividad
curl -H "apikey: $ANON_KEY" $SUPABASE_URL/rest/v1/
```

## 🚀 Despliegue en Producción

### Variables de Entorno Importantes
```env
# Cambiar en producción
POSTGRES_PASSWORD=tu-password-super-seguro
JWT_SECRET=tu-jwt-secret-de-al-menos-32-caracteres

# URLs de producción
API_EXTERNAL_URL=https://tu-dominio.com
SITE_URL=https://tu-dominio.com

# Google OAuth con URLs de producción
GOOGLE_CLIENT_ID=tu_client_id_de_produccion
GOOGLE_CLIENT_SECRET=tu_client_secret_de_produccion
```

### Consideraciones de Seguridad
- Cambiar todas las contraseñas por defecto
- Usar HTTPS en producción
- Configurar CORS apropiadamente
- Usar secretos seguros para JWT

## 📝 Estructura del Proyecto

```
persoqak/
├── web/                          # Aplicación Next.js
│   ├── src/
│   │   ├── app/                  # App Router de Next.js
│   │   ├── components/           # Componentes React
│   │   ├── lib/                  # Utilidades y APIs
│   │   └── contexts/             # Contextos de React
│   ├── supabase/
│   │   ├── migrations/           # Migraciones SQL
│   │   └── kong.yml              # Configuración Gateway
│   └── Dockerfile                # Docker para Next.js
├── browser/                      # API de Browser Automation
│   ├── api_server.py             # Servidor FastAPI simplificado
│   ├── browser_use_recorder/     # Módulos originales
│   └── Dockerfile                # Docker para Python API
├── docker-compose.yml            # Orquestación completa
├── .env.example                  # Variables de entorno ejemplo
└── README.md                     # Esta documentación
```

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 🆘 Soporte

Si tienes problemas:

1. Revisa la sección de solución de problemas
2. Verifica los logs: `docker-compose logs <servicio>`
3. Abre un issue en GitHub con detalles del error

---

**¡Disfruta automatizando tus pruebas con IA! 🤖✨**