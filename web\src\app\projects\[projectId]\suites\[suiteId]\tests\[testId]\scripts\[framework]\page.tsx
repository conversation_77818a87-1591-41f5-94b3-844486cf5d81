"use client";

import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import ScriptEditor from '@/components/script-editor/ScriptEditor';

export default function ScriptEditorPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const testId = params.testId as string;
  const framework = params.framework as string;
  const executionId = searchParams.get('executionId') as string;

  const handleSave = () => {
    // Script saved successfully, could redirect or show success message
  };

  const handleClose = () => {
    router.back();
  };

  if (!executionId) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Test Case
          </Link>
        </Button>
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-2">No Execution ID Found</h2>
          <p className="text-muted-foreground mb-4">
            Unable to load script editor without a valid execution ID.
          </p>
          <Button asChild>
            <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
              Return to Test Case
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Link 
          href={`/projects/${projectId}`}
          className="hover:text-foreground"
        >
          {projectId}
        </Link>
        <span>/</span>
        <Link 
          href={`/projects/${projectId}/suites/${suiteId}`}
          className="hover:text-foreground"
        >
          {suiteId}
        </Link>
        <span>/</span>
        <Link 
          href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}
          className="hover:text-foreground"
        >
          {testId}
        </Link>
        <span>/</span>
        <span className="text-foreground font-medium">Script Editor ({framework})</span>
      </div>

      {/* Back Button */}
      <Button variant="outline" size="sm" asChild>
        <Link href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Test Case
        </Link>
      </Button>

      {/* Script Editor */}
      <ScriptEditor
        executionId={executionId}
        framework={framework}
        onSave={handleSave}
        onClose={handleClose}
      />
    </div>
  );
}