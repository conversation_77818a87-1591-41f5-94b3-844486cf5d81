import { NextRequest, NextResponse } from 'next/server'
import { TestExecutionApiHandler } from '@/lib/api-supabase/test-execution'

export async function GET(request: NextRequest) {
  const handler = new TestExecutionApiHandler()
  return handler.handleRequest(async () => {
    const executions = await handler.getUserExecutions()
    return NextResponse.json({
      success: true,
      count: executions.length,
      items: executions
    })
  })
}