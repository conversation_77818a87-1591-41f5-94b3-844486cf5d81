"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { API_BASE_URL } from "@/lib/config";
import { AlertTriangle, CheckCircle, XCircle, Globe, Settings } from "lucide-react";

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

export default function CORSDebugPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isTestingRunning, setIsTestingRunning] = useState(false);

  const runDiagnostics = async () => {
    setIsTestingRunning(true);
    const results: TestResult[] = [];

    // Test 1: Check configuration
    results.push({
      name: "Configuration Check",
      status: "success",
      message: `API Base URL: ${API_BASE_URL || 'Using Next.js proxy'}`,
      details: {
        apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
        usingProxy: !API_BASE_URL,
        currentOrigin: typeof window !== 'undefined' ? window.location.origin : 'unknown'
      }
    });

    // Test 2: Basic connectivity
    try {
      const healthUrl = API_BASE_URL ? `${API_BASE_URL}/api/health` : '/api/health';
      const response = await fetch(healthUrl, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        results.push({
          name: "API Connectivity",
          status: "success", 
          message: "API is accessible",
          details: data
        });
      } else {
        results.push({
          name: "API Connectivity",
          status: "error",
          message: `HTTP ${response.status}: ${response.statusText}`,
          details: { status: response.status, statusText: response.statusText }
        });
      }
    } catch (error: any) {
      if (error.message.includes('Failed to fetch')) {
        results.push({
          name: "API Connectivity",
          status: "error",
          message: "CORS or network error - cannot reach API",
          details: { error: error.message, possibleCause: "CORS policy blocking request" }
        });
      } else {
        results.push({
          name: "API Connectivity",
          status: "error",
          message: error.message,
          details: { error: error.message }
        });
      }
    }

    // Test 3: CORS headers check
    try {
      const testUrl = API_BASE_URL ? `${API_BASE_URL}/api/health` : '/api/health';
      const response = await fetch(testUrl, {
        method: 'OPTIONS',
        mode: 'cors',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type,Authorization',
        }
      });

      const corsHeaders = {
        'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
        'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
        'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
      };

      const hasValidCors = corsHeaders['Access-Control-Allow-Origin'] !== null;

      results.push({
        name: "CORS Headers",
        status: hasValidCors ? "success" : "warning",
        message: hasValidCors ? "CORS headers present" : "CORS headers missing or not returned",
        details: corsHeaders
      });
    } catch (error: any) {
      results.push({
        name: "CORS Headers",
        status: "error",
        message: "Cannot test CORS headers",
        details: { error: error.message }
      });
    }

    // Test 4: Auth endpoint test
    try {
      const authUrl = API_BASE_URL ? `${API_BASE_URL}/api/Auth/login` : '/api/api/Auth/login';
      const response = await fetch(authUrl, {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: 'test', password: 'test' })
      });

      results.push({
        name: "Auth Endpoint Test",
        status: response.status === 401 ? "success" : "warning",
        message: response.status === 401 ? "Auth endpoint reachable (expected 401)" : `Unexpected response: ${response.status}`,
        details: { status: response.status, statusText: response.statusText }
      });
    } catch (error: any) {
      results.push({
        name: "Auth Endpoint Test",
        status: "error",
        message: "Cannot reach auth endpoint",
        details: { error: error.message }
      });
    }

    setTestResults(results);
    setIsTestingRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      default:
        return <Settings className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'default',
      error: 'destructive', 
      warning: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Globe className="h-6 w-6" />
        <h1 className="text-2xl font-bold">CORS Diagnostics</h1>
      </div>

      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          This page helps diagnose CORS (Cross-Origin Resource Sharing) issues when connecting to the QAK API.
          Run the diagnostics below to identify and resolve connectivity problems.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>Current Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div><strong>API Base URL:</strong> {API_BASE_URL || 'Using Next.js proxy'}</div>
          <div><strong>Environment Variable:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL || 'Not set'}</div>
          <div><strong>Current Origin:</strong> {typeof window !== 'undefined' ? window.location.origin : 'Unknown'}</div>
          <div><strong>Using Proxy:</strong> {!API_BASE_URL ? 'Yes' : 'No'}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Diagnostics</CardTitle>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={runDiagnostics} 
            disabled={isTestingRunning}
            className="mb-4"
          >
            {isTestingRunning ? 'Running Tests...' : 'Run CORS Diagnostics'}
          </Button>

          {testResults.length > 0 && (
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <Card key={index}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(result.status)}
                        <CardTitle className="text-base">{result.name}</CardTitle>
                      </div>
                      {getStatusBadge(result.status)}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
                    {result.details && (
                      <details className="text-xs">
                        <summary className="cursor-pointer text-muted-foreground">View Details</summary>
                        <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Troubleshooting Guide</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">CORS Error Solutions:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Make sure you're using HTTPS (https://api.qak.app) not HTTP</li>
              <li>Verify the API server allows requests from localhost:9001</li>
              <li>Check if there are any redirects (HTTP → HTTPS) breaking the preflight request</li>
              <li>Try using the Next.js proxy by clearing NEXT_PUBLIC_API_BASE_URL</li>
              <li>Contact the API administrator to add your origin to the CORS allowlist</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold mb-2">Environment Variables:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li><code>NEXT_PUBLIC_API_BASE_URL=https://api.qak.app</code> - Direct API calls</li>
              <li><code>NEXT_PUBLIC_API_BASE_URL=</code> - Use Next.js proxy (bypasses CORS)</li>
              <li><code>NEXT_PUBLIC_DEBUG_CORS=true</code> - Enable CORS debugging logs</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}