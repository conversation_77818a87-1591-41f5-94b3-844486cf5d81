import { NextRequest } from 'next/server'
import { TestExecutionApiHandler } from '@/lib/api-supabase/test-execution'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ executionId: string }> }
) {
  const handler = new TestExecutionApiHandler()
  const { executionId } = await params
  return handler.handleRequest(async () => {
    return handler.getExecutionResults(executionId)
  })
}