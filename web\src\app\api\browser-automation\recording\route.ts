import { NextRequest, NextResponse } from 'next/server'
import { BrowserAutomationApiHandler } from '@/lib/api-supabase/browser-automation'

export async function POST(request: NextRequest) {
  const handler = new BrowserAutomationApiHandler()
  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action')

  if (action === 'start') {
    return handler.handleRequest(() => handler.handleStartRecording())
  } else if (action === 'stop') {
    return handler.handleRequest(() => handler.handleStopRecording())
  } else {
    return NextResponse.json({ error: 'Invalid action. Use ?action=start or ?action=stop' }, { status: 400 })
  }
}