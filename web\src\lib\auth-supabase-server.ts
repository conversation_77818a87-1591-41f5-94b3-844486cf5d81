import { createClient as createServer<PERSON>lient } from '@/lib/supabase/server'
import type { Database } from '@/lib/supabase/database.types'

export interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  role: string;
  tenantId?: string;
  emailVerified: boolean;
}

type Profile = Database['public']['Tables']['profiles']['Row']

export class SupabaseAuthServerService {
  static async getCurrentUserServer(): Promise<User | null> {
    try {
      const supabase = await createServerClient()
      
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        return null
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError || !profile) {
        return null
      }

      return {
        id: profile.id,
        email: profile.email,
        username: profile.username,
        name: profile.full_name,
        role: profile.role,
        tenantId: profile.tenant_id || undefined,
        emailVerified: profile.email_verified,
      }
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }
}