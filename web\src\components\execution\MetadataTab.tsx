"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Clock, 
  Hash, 
  FileText, 
  Activity, 
  Mouse, 
  Globe, 
  Camera,
  PlayCircle,
  StopCircle,
  Info,
  BarChart3,
  Zap
} from "lucide-react";

interface MetadataTabProps {
  historyData: TestExecutionHistoryData;
  testIdFromUrl?: string; // The actual test case ID from URL params
}

interface MetadataItemProps {
  label: string;
  value: string | number | null | undefined;
  icon?: React.ReactNode;
  variant?: "default" | "success" | "warning" | "info";
  description?: string;
}

function MetadataItem({ label, value, icon, variant = "default", description }: MetadataItemProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case "success":
        return "bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800";
      case "warning":
        return "bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800";
      case "info":
        return "bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800";
      default:
        return "bg-muted/50 border-border";
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case "success":
        return "text-green-600 dark:text-green-400";
      case "warning":
        return "text-yellow-600 dark:text-yellow-400";
      case "info":
        return "text-blue-600 dark:text-blue-400";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <Card className={`transition-all hover:shadow-md ${getVariantStyles()}`}>
      <CardHeader className="p-4">
        <div className="flex items-center space-x-2">
          {icon && <div className={`${getIconColor()}`}>{icon}</div>}
          <CardDescription className="font-medium">{label}</CardDescription>
        </div>
        <CardTitle className="text-lg truncate flex items-center space-x-2" title={String(value ?? 'N/A')}>
          <span>{value ?? 'N/A'}</span>
          {typeof value === 'number' && value > 0 && (
            <Badge variant="secondary" className="text-xs">
              {value}
            </Badge>
          )}
        </CardTitle>
        {description && (
          <CardDescription className="text-xs text-muted-foreground mt-1">
            {description}
          </CardDescription>
        )}
      </CardHeader>
    </Card>
  );
}

function MetadataSection({ title, children, icon }: { title: string; children: React.ReactNode; icon?: React.ReactNode }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        {icon}
        <h3 className="text-lg font-semibold dark:text-white">{title}</h3>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {children}
      </div>
    </div>
  );
}

export function MetadataTab({ historyData, testIdFromUrl }: MetadataTabProps) {
  const { metadata, actions, elements, urls, screenshots, test_id, execution_id, history_path } = historyData;

  // Calculate some additional metrics
  const totalSteps = metadata.total_steps || 0;
  const executedActions = actions?.length || 0;
  const successRate = totalSteps > 0 ? Math.round((executedActions / totalSteps) * 100) : 0;
  
  // Check if execution was recent (within last hour)
  const isRecent = metadata.start_time ? 
    new Date().getTime() - new Date(metadata.start_time).getTime() < 3600000 : false;

  return (
    <div className="space-y-8">
      {/* Execution Overview */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <CardTitle className="text-blue-900 dark:text-blue-100">Execution Overview</CardTitle>
          </div>
          <CardDescription className="dark:text-blue-200">
            Key information about this test execution
            {isRecent && <Badge variant="outline" className="ml-2 text-xs">Recent</Badge>}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetadataItem 
              label="Test Case ID (Execution)" 
              value={test_id} 
              icon={<Hash className="h-4 w-4" />}
              variant="info"
              description="ID from execution data"
            />
            <MetadataItem 
              label="Test Case ID (URL)" 
              value={testIdFromUrl} 
              icon={<Hash className="h-4 w-4" />}
              variant="info"
              description="ID from URL parameters"
            />
            <MetadataItem 
              label="Execution ID" 
              value={execution_id} 
              icon={<Zap className="h-4 w-4" />}
              variant="default"
              description="Unique execution identifier"
            />
            <MetadataItem 
              label="History File" 
              value={history_path ? "Available" : "N/A"} 
              icon={<FileText className="h-4 w-4" />}
              variant={history_path ? "success" : "warning"}
              description="Execution history file path"
            />
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Performance Metrics */}
      <MetadataSection 
        title="Performance Metrics" 
        icon={<BarChart3 className="h-5 w-5 text-purple-600 dark:text-purple-400" />}
      >
        <MetadataItem 
          label="Total Steps Planned" 
          value={totalSteps} 
          icon={<Activity className="h-4 w-4" />}
          variant="info"
          description="Steps defined in the test"
        />
        <MetadataItem 
          label="Actions Executed" 
          value={executedActions} 
          icon={<Mouse className="h-4 w-4" />}
          variant={executedActions > 0 ? "success" : "warning"}
          description="Browser actions performed"
        />
        <MetadataItem 
          label="Completion Rate" 
          value={`${successRate}%`} 
          icon={<BarChart3 className="h-4 w-4" />}
          variant={successRate >= 80 ? "success" : successRate >= 50 ? "warning" : "default"}
          description="Percentage of planned steps completed"
        />
      </MetadataSection>

      <Separator />

      {/* Interaction Data */}
      <MetadataSection 
        title="Interaction Data" 
        icon={<Activity className="h-5 w-5 text-green-600 dark:text-green-400" />}
      >
        <MetadataItem 
          label="Elements Interacted" 
          value={elements?.length || 0} 
          icon={<Mouse className="h-4 w-4" />}
          variant={elements?.length ? "success" : "default"}
          description="DOM elements that were interacted with"
        />
        <MetadataItem 
          label="URLs Visited" 
          value={urls?.length || 0} 
          icon={<Globe className="h-4 w-4" />}
          variant={urls?.length ? "success" : "default"}
          description="Different pages navigated to"
        />
        <MetadataItem 
          label="Screenshots Captured" 
          value={screenshots?.length || 0} 
          icon={<Camera className="h-4 w-4" />}
          variant={screenshots?.length ? "success" : "default"}
          description="Visual captures taken during execution"
        />
      </MetadataSection>

      <Separator />

      {/* Timing Information */}
      <MetadataSection 
        title="Timing Information" 
        icon={<Clock className="h-5 w-5 text-orange-600 dark:text-orange-400" />}
      >
        <MetadataItem 
          label="Start Time" 
          value={metadata.start_time ? new Date(metadata.start_time).toLocaleString() : 'N/A'} 
          icon={<PlayCircle className="h-4 w-4" />}
          variant="info"
          description="When the test execution began"
        />
        <MetadataItem 
          label="End Time" 
          value={metadata.end_time ? new Date(metadata.end_time).toLocaleString() : 'N/A'} 
          icon={<StopCircle className="h-4 w-4" />}
          variant="info"
          description="When the test execution finished"
        />
        <MetadataItem 
          label="Duration" 
          value={
            metadata.start_time && metadata.end_time 
              ? `${Math.round((new Date(metadata.end_time).getTime() - new Date(metadata.start_time).getTime()) / 1000)}s`
              : 'N/A'
          } 
          icon={<Clock className="h-4 w-4" />}
          variant={metadata.start_time && metadata.end_time ? "success" : "default"}
          description="Total execution time"
        />
      </MetadataSection>

      {/* Quick Stats Summary */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800">
        <CardHeader>
          <CardTitle className="text-purple-900 dark:text-purple-100 flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Execution Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div className="space-y-1">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{executedActions}</div>
              <div className="text-xs text-purple-700 dark:text-purple-300">Actions</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{urls?.length || 0}</div>
              <div className="text-xs text-blue-700 dark:text-blue-300">Pages</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{screenshots?.length || 0}</div>
              <div className="text-xs text-green-700 dark:text-green-300">Screenshots</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{successRate}%</div>
              <div className="text-xs text-orange-700 dark:text-orange-300">Completion</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
