"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { 
  Play, 
  Save, 
  Download, 
  Plus, 
  Trash2, 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>own,
  <PERSON>tings,
  Code2,
  <PERSON><PERSON><PERSON><PERSON>,
  Type,
  Navigation,
  Timer,
  CheckSquare,
  Camera,
  Edit3,
  Target
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  getExecutionById, 
  getExecutionScript, 
  generateScript as generateScriptAPI, 
  saveExecutionScript, 
  generateCodeFromScript 
} from '@/lib/api';

// Types matching the backend models
interface ElementSelector {
  description: string;
  method: string;
  value: string;
  backup_selectors?: Array<{method: string; value: string}>;
}

interface Validation {
  id: string;
  type: string;
  description: string;
  expected_value?: string;
  element_selector?: ElementSelector;
  enabled: boolean;
}

interface EditableStep {
  id: string;
  step_number: number;
  action_type: string;
  description: string;
  element_selector?: ElementSelector;
  input_data?: string;
  url?: string;
  wait_time?: number;
  validations: Validation[];
  notes?: string;
  enabled: boolean;
  screenshot_after: boolean;
  original_step_number?: number;
  success_in_original: boolean;
}

interface GeneratedScript {
  script_id: string;
  execution_id: string;
  framework: string;
  generated_at: string;
  script_name: string;
  description: string;
  script_summary?: string;  // AI-generated human-readable summary
  target_url?: string;
  editable_steps: EditableStep[];
  include_screenshots: boolean;
  include_waits: boolean;
  include_validations: boolean;
  total_steps: number;
  enabled_steps: number;
  validations_count: number;
  raw_script_content: string;
}

interface ScriptEditorProps {
  executionId: string;
  framework: string;
  onSave?: (script: GeneratedScript) => void;
  onClose?: () => void;
}

const actionTypeIcons = {
  navigate: Navigation,
  click: MousePointer,
  type_text: Type,
  select_option: CheckSquare,
  wait: Timer,
  verify: CheckSquare,
  scroll: ArrowDown,
  hover: MousePointer,
  take_screenshot: Camera,
  custom: Settings
};

const actionTypeLabels = {
  navigate: "Navigate to URL",
  click: "Click Element",
  type_text: "Type Text",
  select_option: "Select Option", 
  wait: "Wait",
  verify: "Verify/Assert",
  scroll: "Scroll",
  hover: "Hover",
  take_screenshot: "Take Screenshot",
  custom: "Custom Action"
};

const validationTypes = [
  { value: "text_contains", label: "Text contains" },
  { value: "text_equals", label: "Text equals" },
  { value: "element_visible", label: "Element is visible" },
  { value: "element_enabled", label: "Element is enabled" },
  { value: "url_contains", label: "URL contains" },
  { value: "value_equals", label: "Input value equals" }
];

const selectorMethods = [
  { value: "testid", label: "Test ID (Recommended)" },
  { value: "label", label: "Aria Label" },
  { value: "text", label: "Text Content" },
  { value: "css", label: "CSS Selector" },
  { value: "xpath", label: "XPath" }
];

export default function ScriptEditor({ executionId, framework, onSave, onClose }: ScriptEditorProps) {
  const [script, setScript] = useState<GeneratedScript | null>(null);
  const [executionData, setExecutionData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [showCode, setShowCode] = useState(false);
  const [editingStep, setEditingStep] = useState<string | null>(null);
  const [hasExistingScript, setHasExistingScript] = useState(false);
  const [generationOptions, setGenerationOptions] = useState({
    include_assertions: true,
    include_waits: true,
    include_screenshots: false,
    add_error_handling: true,
    use_best_locators: true,
    add_comments: true,
    page_object_pattern: false
  });
  const { toast } = useToast();

  useEffect(() => {
    loadExecutionData();
  }, [executionId, framework]);

  const normalizeScript = (scriptData: any): GeneratedScript => {
    console.log('🔧 Raw scriptData structure:', scriptData);
    console.log('🔧 scriptData.editable_steps:', scriptData.editable_steps);
    console.log('🔧 scriptData.generated_script:', scriptData.generated_script);
    console.log('🔧 scriptData.generated_script?.editable_steps:', scriptData.generated_script?.editable_steps);
    
    // Try to get target URL from script data first, then from execution data
    const targetUrl = scriptData.target_url || 
                     executionData?.target_url || 
                     executionData?.url || 
                     (executionData?.actions && executionData.actions[0]?.url) ||
                     '';

    // Get editable_steps from the correct location
    const editableSteps = scriptData.editable_steps || 
                         scriptData.generated_script?.editable_steps || 
                         [];
    
    console.log('🔧 Final editable_steps:', editableSteps);
    console.log('🔧 Final editable_steps length:', editableSteps.length);

    return {
      ...scriptData,
      editable_steps: editableSteps,
      total_steps: scriptData.total_steps || scriptData.generated_script?.total_steps || 0,
      enabled_steps: scriptData.enabled_steps || scriptData.generated_script?.enabled_steps || 0,
      validations_count: scriptData.validations_count || scriptData.generated_script?.validations_count || 0,
      include_screenshots: scriptData.include_screenshots || false,
      include_waits: scriptData.include_waits || true,
      include_validations: scriptData.include_validations || true,
      script_name: scriptData.script_name || scriptData.generated_script?.script_name || 'Generated Test Script',
      description: scriptData.description || scriptData.generated_script?.description || '',
      script_summary: scriptData.script_summary || scriptData.generated_script?.script_summary || '',
      raw_script_content: scriptData.raw_script_content || scriptData.script_content || '',
      target_url: targetUrl
    };
  };

  const loadExecutionData = async () => {
    try {
      setLoading(true);
      
      // Load execution data from API
      try {
        const execution = await getExecutionById(executionId);
        setExecutionData(execution);
        console.log('✅ Loaded execution data:', execution);
      } catch (error) {
        console.error('❌ Failed to load execution data:', error);
        toast({
          title: "Warning",
          description: "Could not load execution data, but you can still generate a script.",
          variant: "destructive"
        });
      }
      
      // Check if script already exists
      try {
        const existingScriptData = await getExecutionScript(executionId, framework);
        console.log('✅ Found existing script:', existingScriptData);
        
        // Handle different response structures
        let scriptToUse = existingScriptData;
        if (existingScriptData.script) {
          scriptToUse = existingScriptData.script;
        } else if (existingScriptData.data) {
          scriptToUse = existingScriptData.data;
        }
        
        setScript(normalizeScript(scriptToUse));
        setHasExistingScript(true);
      } catch (error) {
        console.log('ℹ️ No existing script found, ready to generate new one');
        setHasExistingScript(false);
      }
    } catch (error) {
      console.error('Error loading execution data:', error);
      toast({
        title: "Error",
        description: "Failed to load execution data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const generateScript = async () => {
    try {
      setGenerating(true);
      
      try {
        const result = await generateScriptAPI({
          execution_id: executionId,
          framework: framework,
          ...generationOptions
        });
        
        console.log('✅ Script generation response:', result);
        
        // Handle different response structures
        let scriptToUse = result;
        if (result.generated_script) {
          console.log('📄 Using generated_script field:', result.generated_script);
          scriptToUse = result.generated_script;
        } else if (result.data) {
          console.log('📄 Using data field:', result.data);
          scriptToUse = result.data;
        } else {
          console.log('📄 Using root level result:', result);
          // If no nested structure, create a compatible structure
          scriptToUse = {
            ...result,
            script_id: result.script_id || `script_${Date.now()}`,
            execution_id: result.execution_id || executionId,
            framework: result.framework || framework,
            generated_at: result.generated_at || new Date().toISOString(),
            script_name: result.script_name || `Test from execution ${executionId.substring(0, 8)}`,
            description: result.description || `Generated from execution on ${new Date().toLocaleDateString()}`,
            raw_script_content: result.script_content || '',
            editable_steps: result.editable_steps || [],
            total_steps: result.steps_count || 0,
            enabled_steps: result.steps_count || 0,
            validations_count: result.validations_count || 0,
            include_screenshots: generationOptions.include_screenshots,
            include_waits: generationOptions.include_waits,
            include_validations: generationOptions.include_assertions,
            target_url: result.target_url || executionData?.target_url || executionData?.url || ''
          };
        }
        
        const normalizedScript = normalizeScript(scriptToUse);
     
        setScript(normalizedScript);
        setHasExistingScript(true);
        
        // Additional debug log after state update
        setTimeout(() => {
          console.log('🔄 Script state after update:', normalizedScript);
          console.log('🔍 Script editable_steps after update:', normalizedScript.editable_steps);
        }, 100);
        
        toast({
          title: "Script Generated",
          description: "Your test script has been generated successfully!",
        });
      } catch (error) {
        console.error('Error generating script:', error);
        toast({
          title: "Generation Failed",
          description: "Failed to generate script. Please try again.",
          variant: "destructive"
        });
      } finally {
        setGenerating(false);
      }
    } catch (error) {
      console.error('Error in generateScript:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate script. Please try again.",
        variant: "destructive"
      });
      setGenerating(false);
    }
  };

  const saveScript = async () => {
    if (!script) return;
    
    try {
      await saveExecutionScript(executionId, framework, {
        script_content: script.raw_script_content || '',
        ...script
      });
      
      toast({
        title: "Script Saved",
        description: "Your script changes have been saved successfully."
      });
      onSave?.(script);
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "Could not save script changes. Please try again.",
        variant: "destructive"
      });
    }
  };  const downloadScript = async () => {
    if (!script) return;
    
    try {
      // Generate fresh script content
      const result = await generateCodeFromScript({
        script_content: script.raw_script_content || '',
        framework: framework,
        options: script
      });
      
      const blob = new Blob([result.script_content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${script.script_name.replace(/[^a-zA-Z0-9]/g, '_')}_${framework}.py`;
      a.click();
      URL.revokeObjectURL(url);
      
      toast({
        title: "Script Downloaded",
        description: "Script file downloaded successfully."
      });
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Could not download script. Please try again.",
        variant: "destructive"
      });
    }
  };

  const addStep = () => {
    if (!script) return;
    
    const editableSteps = script.editable_steps || [];
    const newStep: EditableStep = {
      id: `step_${Date.now()}`,
      step_number: editableSteps.length + 1,
      action_type: "click",
      description: "New step",
      validations: [],
      enabled: true,
      screenshot_after: false,
      success_in_original: true
    };
    
    setScript({
      ...script,
      editable_steps: [...editableSteps, newStep],
      total_steps: (script.total_steps || 0) + 1,
      enabled_steps: (script.enabled_steps || 0) + 1
    });
  };

  const removeStep = (stepId: string) => {
    if (!script) return;
    
    const editableSteps = script.editable_steps || [];
    const updatedSteps = editableSteps
      .filter(s => s.id !== stepId)
      .map((s, index) => ({ ...s, step_number: index + 1 }));
    
    setScript({
      ...script,
      editable_steps: updatedSteps,
      total_steps: updatedSteps.length,
      enabled_steps: updatedSteps.filter(s => s.enabled).length
    });
  };

  const updateStep = (stepId: string, updates: Partial<EditableStep>) => {
    if (!script) return;
    
    const editableSteps = script.editable_steps || [];
    const updatedSteps = editableSteps.map(step =>
      step.id === stepId ? { ...step, ...updates } : step
    );
    
    setScript({
      ...script,
      editable_steps: updatedSteps,
      enabled_steps: updatedSteps.filter(s => s.enabled).length
    });
  };

  const moveStep = (stepId: string, direction: 'up' | 'down') => {
    if (!script) return;
    
    const editableSteps = script.editable_steps || [];
    const steps = [...editableSteps];
    const index = steps.findIndex(s => s.id === stepId);
    
    if (direction === 'up' && index > 0) {
      [steps[index], steps[index - 1]] = [steps[index - 1], steps[index]];
    } else if (direction === 'down' && index < steps.length - 1) {
      [steps[index], steps[index + 1]] = [steps[index + 1], steps[index]];
    }
    
    // Renumber steps
    steps.forEach((step, i) => {
      step.step_number = i + 1;
    });
    
    setScript({
      ...script,
      editable_steps: steps
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading script editor...</p>
        </div>
      </div>
    );
  }

  // Show generation interface if no script exists
  if (!script) {
    return (
      <div className="space-y-6">
        {/* Execution Info */}
        {executionData ? (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Play className="h-5 w-5" />
                Browser Execution Data
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                The following data was captured from your browser interaction and will be used to generate the {framework} script.
              </p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <Label className="text-sm font-medium">Execution ID</Label>
                  <p className="text-sm text-muted-foreground font-mono">{executionData.execution_id}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <Badge variant={executionData.status === 'success' || executionData.status === 'completed' ? 'default' : 'destructive'}>
                    {executionData.status}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Target URL</Label>
                  <p className="text-sm text-muted-foreground">{executionData.target_url || executionData.url || 'Not specified'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Actions Recorded</Label>
                  <p className="text-sm text-muted-foreground font-medium">
                    {executionData.actions?.length || executionData.step_count || 0} user interactions
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Duration</Label>
                  <p className="text-sm text-muted-foreground">
                    {executionData.duration 
                      ? `${Math.round(executionData.duration)}s` 
                      : executionData.execution_time
                      ? `${Math.round(executionData.execution_time)}s`
                      : 'Unknown'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Success Rate</Label>
                  <p className="text-sm text-muted-foreground">
                    {executionData.success_rate 
                      ? `${Math.round(executionData.success_rate * 100)}%` 
                      : executionData.success_rate_percentage
                      ? `${executionData.success_rate_percentage}%`
                      : 'Unknown'}
                  </p>
                </div>
              </div>
              
              {/* Action Preview */}
              {(executionData.actions && executionData.actions.length > 0) && (
                <div>
                  <Label className="text-sm font-medium mb-2 block">Recorded Actions Preview:</Label>
                  <div className="bg-muted rounded-md p-3 max-h-32 overflow-y-auto">
                    {executionData.actions.slice(0, 5).map((action: any, index: number) => (
                      <div key={index} className="text-xs text-muted-foreground mb-1">
                        {index + 1}. {action.action_type || action.type}: {action.description || action.element || action.url || action.text || 'Unknown'}
                      </div>
                    ))}
                    {executionData.actions.length > 5 && (
                      <div className="text-xs text-muted-foreground italic">
                        ... and {executionData.actions.length - 5} more actions
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* If no actions array, show general info */}
              {(!executionData.actions || executionData.actions.length === 0) && (
                <div className="bg-muted rounded-md p-3">
                  <p className="text-xs text-muted-foreground">
                    Execution data available. Browser interactions were captured and are ready for script generation.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-orange-600">⚠️ No Execution Data Found</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Unable to load execution data for ID: <code className="bg-muted px-1 rounded">{executionId}</code>
              </p>
              <p className="text-sm text-muted-foreground">
                This might happen if the execution doesn't exist or the API is unavailable. You can still proceed to generate a basic script template.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Generation Options */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Script Generation Options</CardTitle>
            <p className="text-sm text-muted-foreground">
              Configure how your {framework} script will be generated from the execution data.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={generationOptions.include_assertions}
                  onCheckedChange={(checked) => setGenerationOptions({...generationOptions, include_assertions: checked})}
                />
                <div>
                  <Label>Include Assertions</Label>
                  <p className="text-xs text-muted-foreground">Add validation checks</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={generationOptions.include_waits}
                  onCheckedChange={(checked) => setGenerationOptions({...generationOptions, include_waits: checked})}
                />
                <div>
                  <Label>Include Waits</Label>
                  <p className="text-xs text-muted-foreground">Add timing delays</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={generationOptions.include_screenshots}
                  onCheckedChange={(checked) => setGenerationOptions({...generationOptions, include_screenshots: checked})}
                />
                <div>
                  <Label>Include Screenshots</Label>
                  <p className="text-xs text-muted-foreground">Capture screenshots during execution</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={generationOptions.add_error_handling}
                  onCheckedChange={(checked) => setGenerationOptions({...generationOptions, add_error_handling: checked})}
                />
                <div>
                  <Label>Error Handling</Label>
                  <p className="text-xs text-muted-foreground">Add try-catch blocks</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={generationOptions.use_best_locators}
                  onCheckedChange={(checked) => setGenerationOptions({...generationOptions, use_best_locators: checked})}
                />
                <div>
                  <Label>Best Locators</Label>
                  <p className="text-xs text-muted-foreground">Use most reliable selectors</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={generationOptions.add_comments}
                  onCheckedChange={(checked) => setGenerationOptions({...generationOptions, add_comments: checked})}
                />
                <div>
                  <Label>Add Comments</Label>
                  <p className="text-xs text-muted-foreground">Include explanatory comments</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Generate Button */}
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="p-6 text-center">
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Ready to Generate Your Script</h3>
              <p className="text-muted-foreground text-sm">
                {executionData 
                  ? `Transform your recorded ${executionData.actions?.length || 0} browser interactions into a working ${framework} test script.`
                  : `Generate a basic ${framework} test script template that you can customize.`
                }
              </p>
            </div>
            
            <Button 
              onClick={generateScript} 
              disabled={generating}
              size="lg"
              className="px-8 py-3 text-base"
            >
              {generating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Generating {framework} Script...
                </>
              ) : (
                <>
                  <Code2 className="h-5 w-5 mr-3" />
                  Generate {framework} Script
                </>
              )}
            </Button>
            
            {executionData && (
              <p className="text-xs text-muted-foreground mt-3">
                This will analyze your browser session and create executable test code
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show the script editor interface
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex-1">
          <h2 className="text-2xl font-bold">{script?.script_name || 'Generated Test Script'}</h2>
          <p className="text-muted-foreground">{script?.description || ''}</p>
          <div className="flex flex-wrap items-center gap-4 mt-2">
            <Badge variant="outline">{framework}</Badge>
            <span className="text-sm text-muted-foreground">
              {script?.enabled_steps || 0} de {script?.total_steps || 0} pasos activos
            </span>
            <span className="text-sm text-muted-foreground">
              {script?.validations_count || 0} validaciones
            </span>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2 lg:flex-nowrap">
          <Button 
            variant="outline" 
            onClick={generateScript}
            disabled={generating}
            className="text-primary flex-1 lg:flex-none"
            size="sm"
          >
            {generating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                <span className="hidden sm:inline">Regenerando...</span>
                <span className="sm:hidden">Regen...</span>
              </>
            ) : (
              <>
                <Code2 className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Regenerar</span>
                <span className="sm:hidden">Regen</span>
              </>
            )}
          </Button>
          <Button 
            variant="outline" 
            onClick={() => setShowCode(!showCode)}
            size="sm"
            className="flex-1 lg:flex-none"
          >
            <Code2 className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">{showCode ? 'Ocultar' : 'Ver Código'}</span>
            <span className="sm:hidden">{showCode ? 'Ocultar' : 'Código'}</span>
          </Button>
          <Button 
            variant="outline" 
            onClick={downloadScript}
            size="sm"
            className="flex-1 lg:flex-none"
          >
            <Download className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Descargar</span>
            <span className="sm:hidden">Down</span>
          </Button>
          <Button 
            onClick={saveScript}
            size="sm"
            className="flex-1 lg:flex-none"
          >
            <Save className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Guardar</span>
            <span className="sm:hidden">Save</span>
          </Button>
          {onClose && (
            <Button 
              variant="outline" 
              onClick={onClose}
              size="sm"
              className="flex-1 lg:flex-none"
            >
              <span className="hidden sm:inline">Cerrar</span>
              <span className="sm:hidden">X</span>
            </Button>
          )}
        </div>
      </div>

      {/* Human-Readable Summary */}
      {script?.script_summary && (
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Eye className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              Resumen del Script
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">{script.script_summary}</p>
          </CardContent>
        </Card>
      )}

      {/* Code Preview */}
      {showCode && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Generated Code</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-md overflow-x-auto text-sm">
              <code>{script?.raw_script_content || 'No code generated yet'}</code>
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Script Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Script Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Script Name</Label>
              <Input 
                value={script?.script_name || ''}
                onChange={(e) => script && setScript({...script, script_name: e.target.value})}
              />
            </div>
            <div>
              <Label>Target URL</Label>
              <Input 
                value={script?.target_url || ''}
                onChange={(e) => script && setScript({...script, target_url: e.target.value})}
                placeholder={executionData?.target_url || executionData?.url || "https://example.com"}
              />
            </div>
          </div>
          
          <div>
            <Label>Description</Label>
            <Textarea 
              value={script?.description || ''}
              onChange={(e) => script && setScript({...script, description: e.target.value})}
              rows={2}
            />
          </div>
          
          <div className="flex gap-6">
            <div className="flex items-center space-x-2">
              <Switch 
                checked={script?.include_waits || false}
                onCheckedChange={(checked) => script && setScript({...script, include_waits: checked})}
              />
              <Label>Include waits</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                checked={script?.include_screenshots || false}
                onCheckedChange={(checked) => script && setScript({...script, include_screenshots: checked})}
              />
              <Label>Include screenshots</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch 
                checked={script?.include_validations || false}
                onCheckedChange={(checked) => script && setScript({...script, include_validations: checked})}
              />
              <Label>Include validations</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Steps Editor */}
      <Card className="border-t-4 border-t-blue-500">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30">
          <div className="flex flex-row items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-xl text-gray-800 dark:text-gray-200">Pasos del Test</CardTitle>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {script?.enabled_steps || 0} de {script?.total_steps || 0} pasos activos
                </p>
              </div>
            </div>
            <Button onClick={addStep} size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Agregar Paso
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {(script?.editable_steps || []).map((step, index) => {
              // Debug logs for rendering
              if (index === 0) {
                console.log('🔍 Rendering Test Steps section, script:', script);
                console.log('🔍 script?.editable_steps:', script?.editable_steps);
                console.log('🔍 script?.editable_steps?.length:', script?.editable_steps?.length);
              }
              
              return (
                <div key={step.id} className="relative">
                  {/* Connection line to next step */}
                  {index < (script?.editable_steps || []).length - 1 && (
                    <div className="absolute left-8 top-16 w-0.5 h-8 bg-gradient-to-b from-gray-300 to-gray-200 dark:from-gray-600 dark:to-gray-700 z-0"></div>
                  )}
                  
                  <StepEditor
                    step={step}
                    isFirst={index === 0}
                    isLast={index === (script?.editable_steps || []).length - 1}
                    isEditing={editingStep === step.id}
                    onEdit={() => setEditingStep(editingStep === step.id ? null : step.id)}
                    onUpdate={(updates) => updateStep(step.id, updates)}
                    onRemove={() => removeStep(step.id)}
                    onMove={(direction) => moveStep(step.id, direction)}
                  />
                </div>
              );
            })}
            
            {(!script?.editable_steps || script.editable_steps.length === 0) && (
              <div className="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800/30">
                <div className="flex flex-col items-center gap-4">
                  <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full">
                    <Target className="h-8 w-8 text-gray-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
                      No hay pasos todavía
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                      Los pasos se generan automáticamente desde el código de la IA
                    </p>
                    <Button onClick={addStep} className="bg-blue-600 hover:bg-blue-700 text-white">
                      <Plus className="h-4 w-4 mr-2" />
                      Agregar Primer Paso
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Step Editor Component (I'll create this as a separate component)
interface StepEditorProps {
  step: EditableStep;
  isFirst: boolean;
  isLast: boolean;
  isEditing: boolean;
  onEdit: () => void;
  onUpdate: (updates: Partial<EditableStep>) => void;
  onRemove: () => void;
  onMove: (direction: 'up' | 'down') => void;
}

function StepEditor({ step, isFirst, isLast, isEditing, onEdit, onUpdate, onRemove, onMove }: StepEditorProps) {
  const ActionIcon = actionTypeIcons[step.action_type as keyof typeof actionTypeIcons] || Settings;
  
  // Simplified color scheme - only 2 tones for stability
  const getActionColors = (actionType: string) => {
    const isImportant = ['navigate', 'click', 'verify'].includes(actionType);
    
    if (isImportant) {
      return {
        bg: 'bg-blue-50 dark:bg-blue-950/20',
        border: 'border-blue-200 dark:border-blue-800',
        icon: 'text-blue-600 dark:text-blue-400',
        iconBg: 'bg-blue-100 dark:bg-blue-900/40'
      };
    } else {
      return {
        bg: 'bg-slate-50 dark:bg-slate-800/30',
        border: 'border-slate-200 dark:border-slate-700',
        icon: 'text-slate-600 dark:text-slate-400',
        iconBg: 'bg-slate-100 dark:bg-slate-700/50'
      };
    }
  };

  const colors = getActionColors(step.action_type);
  
  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${colors.bg} ${colors.border} ${!step.enabled ? 'opacity-60' : ''} ${step.success_in_original ? '' : 'border-orange-300 bg-orange-50 dark:bg-orange-950/20'}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4 flex-1">
            {/* Step number and icon */}
            <div className="flex flex-col items-center gap-2">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-white dark:bg-gray-800 border-2 border-current font-bold text-sm text-gray-600 dark:text-gray-300">
                {step.step_number}
              </div>
              <div className={`p-2 rounded-lg ${colors.iconBg}`}>
                <ActionIcon className={`h-5 w-5 ${colors.icon}`} />
              </div>
            </div>
            
            {/* Step content */}
            <div className="flex-1 pt-1">
              {isEditing ? (
                <StepEditForm 
                  step={step} 
                  onUpdate={onUpdate}
                  onCancel={onEdit}
                />
              ) : (
                <StepDisplayView step={step} />
              )}
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-1">
              <Switch 
                checked={step.enabled}
                onCheckedChange={(checked) => onUpdate({ enabled: checked })}
              />
              <span className="text-xs text-muted-foreground">
                {step.enabled ? 'ON' : 'OFF'}
              </span>
            </div>
            
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="sm" onClick={onEdit} className="h-8 w-8 p-0">
                <Edit3 className="h-3 w-3" />
              </Button>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => onMove('up')}
                disabled={isFirst}
                className="h-8 w-8 p-0"
              >
                <ArrowUp className="h-3 w-3" />
              </Button>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => onMove('down')}
                disabled={isLast}
                className="h-8 w-8 p-0"
              >
                <ArrowDown className="h-3 w-3" />
              </Button>
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/30">
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Eliminar Paso</AlertDialogTitle>
                    <AlertDialogDescription>
                      ¿Estás seguro de que quieres eliminar este paso? Esta acción no se puede deshacer.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction onClick={onRemove} className="bg-red-600 hover:bg-red-700">
                      Eliminar
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// I'll create the StepDisplayView and StepEditForm components in the next part
function StepDisplayView({ step }: { step: EditableStep }) {
  const [showTechnicalDetails, setShowTechnicalDetails] = useState(false);
  
  // Generate human-friendly description
  const getHumanDescription = (step: EditableStep) => {
    const baseAction = actionTypeLabels[step.action_type as keyof typeof actionTypeLabels] || step.action_type;
    
    if (step.action_type === 'click' && step.element_selector) {
      return `Hacer clic en "${step.element_selector.description}"`;
    } else if (step.action_type === 'type_text' && step.input_data) {
      return `Escribir "${step.input_data}" en ${step.element_selector?.description || 'el campo'}`;
    } else if (step.action_type === 'navigate' && step.url) {
      return `Navegar a ${step.url}`;
    } else if (step.action_type === 'select_option' && step.input_data) {
      return `Seleccionar "${step.input_data}" de ${step.element_selector?.description || 'la lista'}`;
    } else if (step.action_type === 'wait' && step.wait_time) {
      return `Esperar ${step.wait_time} segundos`;
    } else if (step.action_type === 'verify' && step.element_selector) {
      return `Verificar que "${step.element_selector.description}" está visible`;
    }
    
    return step.description;
  };

  return (
    <div className="space-y-3">
      {/* Human-friendly description */}
      <div className="flex items-center gap-2">
        <span className="font-medium text-gray-800 dark:text-gray-200">{getHumanDescription(step)}</span>
        <Badge variant="outline" className="text-xs">
          {actionTypeLabels[step.action_type as keyof typeof actionTypeLabels] || step.action_type}
        </Badge>
        {!step.success_in_original && (
          <Badge variant="destructive" className="text-xs">
            Falló en original
          </Badge>
        )}
      </div>

      {/* Expandable technical details */}
      {(step.element_selector || step.input_data || step.url || step.validations.length > 0) && (
        <div className="space-y-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
            className="text-xs text-muted-foreground hover:text-foreground p-0 h-auto"
          >
            {showTechnicalDetails ? (
              <>
                <EyeOff className="h-3 w-3 mr-1" />
                Ocultar detalles técnicos
              </>
            ) : (
              <>
                <Eye className="h-3 w-3 mr-1" />
                Ver detalles técnicos
              </>
            )}
          </Button>
          
          {showTechnicalDetails && (
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2 border-l-4 border-blue-200 dark:border-blue-600">
              {step.element_selector && (
                <div className="text-sm space-y-1">
                  <p className="font-medium text-gray-700 dark:text-gray-300">🎯 Elemento objetivo:</p>
                  <p className="text-gray-600 dark:text-gray-400">{step.element_selector.description}</p>
                  <div className="bg-white dark:bg-gray-700 rounded p-2 font-mono text-xs border dark:border-gray-600">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {step.element_selector.method}
                      </Badge>
                      <code className="text-foreground">{step.element_selector.value}</code>
                    </div>
                    {step.element_selector.backup_selectors && step.element_selector.backup_selectors.length > 0 && (
                      <details className="mt-2">
                        <summary className="text-xs cursor-pointer text-muted-foreground hover:text-foreground">
                          Selectores alternativos ({step.element_selector.backup_selectors.length})
                        </summary>
                        <div className="mt-1 space-y-1">
                          {step.element_selector.backup_selectors.map((backup, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {backup.method}
                              </Badge>
                              <code className="text-xs">{backup.value}</code>
                            </div>
                          ))}
                        </div>
                      </details>
                    )}
                  </div>
                </div>
              )}
              
              {step.input_data && (
                <div className="text-sm">
                  <span className="font-medium text-gray-700 dark:text-gray-300">📝 Datos de entrada:</span>
                  <code className="ml-2 bg-white dark:bg-gray-700 px-2 py-1 rounded text-xs border dark:border-gray-600">"{step.input_data}"</code>
                </div>
              )}
              
              {step.url && (
                <div className="text-sm">
                  <span className="font-medium text-gray-700 dark:text-gray-300">🌐 URL:</span>
                  <code className="ml-2 bg-white dark:bg-gray-700 px-2 py-1 rounded text-xs border dark:border-gray-600 break-all">{step.url}</code>
                </div>
              )}
              
              {step.validations.length > 0 && (
                <div className="flex items-center gap-1">
                  <CheckSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {step.validations.length} validación{step.validations.length !== 1 ? 'es' : ''}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
      
      {/* Notes section - hidden by default, only shown in technical details */}
      {step.notes && showTechnicalDetails && (
        <div className="bg-gray-100 dark:bg-gray-700 border-l-4 border-gray-300 dark:border-gray-600 p-2 rounded mt-2">
          <p className="text-xs text-gray-600 dark:text-gray-400">
            💻 <strong>Código:</strong> <code className="text-xs font-mono">{step.notes.replace('Línea de código: ', '')}</code>
          </p>
        </div>
      )}
    </div>
  );
}

function StepEditForm({ step, onUpdate, onCancel }: {
  step: EditableStep;
  onUpdate: (updates: Partial<EditableStep>) => void;
  onCancel: () => void;
}) {
  const [formData, setFormData] = useState(step);
  
  const handleSave = () => {
    onUpdate(formData);
    onCancel();
  };
  
  return (
    <div className="space-y-4 border rounded-lg p-4 bg-background">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Action Type</Label>
          <Select 
            value={formData.action_type}
            onValueChange={(value) => setFormData({...formData, action_type: value})}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(actionTypeLabels).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label>Description</Label>
          <Input 
            value={formData.description}
            onChange={(e) => setFormData({...formData, description: e.target.value})}
          />
        </div>
      </div>
      
      {/* Element Selector Section */}
      {formData.action_type !== 'navigate' && formData.action_type !== 'wait' && formData.action_type !== 'take_screenshot' && (
        <div className="space-y-3">
          <Label className="text-sm font-medium">Element Selector</Label>
          {formData.element_selector ? (
            <div className="border rounded-lg p-3 space-y-3">
              <div>
                <Label className="text-xs">Description</Label>
                <Input 
                  value={formData.element_selector.description}
                  onChange={(e) => setFormData({
                    ...formData, 
                    element_selector: {
                      ...formData.element_selector!,
                      description: e.target.value
                    }
                  })}
                  placeholder="e.g., Login button, Email input field"
                  className="text-sm"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-xs">Selector Method</Label>
                  <Select 
                    value={formData.element_selector.method}
                    onValueChange={(value) => setFormData({
                      ...formData, 
                      element_selector: {
                        ...formData.element_selector!,
                        method: value
                      }
                    })}
                  >
                    <SelectTrigger className="text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {selectorMethods.map((method) => (
                        <SelectItem key={method.value} value={method.value}>
                          {method.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label className="text-xs">Selector Value</Label>
                  <Input 
                    value={formData.element_selector.value}
                    onChange={(e) => setFormData({
                      ...formData, 
                      element_selector: {
                        ...formData.element_selector!,
                        value: e.target.value
                      }
                    })}
                    placeholder="e.g., #login-btn, [data-testid='submit']"
                    className="text-sm font-mono"
                  />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button 
                  type="button"
                  variant="outline" 
                  size="sm"
                  onClick={() => setFormData({
                    ...formData, 
                    element_selector: undefined
                  })}
                >
                  Remove Selector
                </Button>
              </div>
            </div>
          ) : (
            <div className="border-2 border-dashed rounded-lg p-4 text-center">
              <p className="text-sm text-muted-foreground mb-2">No element selector defined</p>
              <Button 
                type="button"
                variant="outline" 
                size="sm"
                onClick={() => setFormData({
                  ...formData, 
                  element_selector: {
                    description: "New element",
                    method: "css",
                    value: "",
                    backup_selectors: []
                  }
                })}
              >
                Add Element Selector
              </Button>
            </div>
          )}
        </div>
      )}
      
      {(formData.action_type === 'type_text' || formData.action_type === 'select_option') && (
        <div>
          <Label>Input Data</Label>
          <Input 
            value={formData.input_data || ''}
            onChange={(e) => setFormData({...formData, input_data: e.target.value})}
            placeholder="Text to type or option to select"
          />
        </div>
      )}
      
      {formData.action_type === 'navigate' && (
        <div>
          <Label>URL</Label>
          <Input 
            value={formData.url || ''}
            onChange={(e) => setFormData({...formData, url: e.target.value})}
            placeholder="https://example.com"
          />
        </div>
      )}
      
      {formData.action_type === 'wait' && (
        <div>
          <Label>Wait Time (seconds)</Label>
          <Input 
            type="number"
            value={formData.wait_time || ''}
            onChange={(e) => setFormData({...formData, wait_time: parseInt(e.target.value) || 0})}
          />
        </div>
      )}
      
      <div>
        <Label>Notes (optional)</Label>
        <Textarea 
          value={formData.notes || ''}
          onChange={(e) => setFormData({...formData, notes: e.target.value})}
          rows={2}
          placeholder="Additional notes about this step"
        />
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch 
          checked={formData.screenshot_after}
          onCheckedChange={(checked) => setFormData({...formData, screenshot_after: checked})}
        />
        <Label>Take screenshot after this step</Label>
      </div>
      
      <div className="flex gap-2">
        <Button onClick={handleSave} size="sm">
          Save Changes
        </Button>
        <Button variant="outline" onClick={onCancel} size="sm">
          Cancel
        </Button>
      </div>
    </div>
  );
}