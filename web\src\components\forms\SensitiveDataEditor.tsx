"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Trash2, Plus, Edit2, Globe, Key } from "lucide-react";

interface SensitiveDataEditorProps {
  value?: Record<string, Record<string, string>>;
  onChange: (value: Record<string, Record<string, string>>) => void;
}

interface DomainMapping {
  domain: string;
  placeholders: Record<string, string>;
}

export function SensitiveDataEditor({ value = {}, onChange }: SensitiveDataEditorProps) {
  const [isAddingDomain, setIsAddingDomain] = useState(false);
  const [newDomain, setNewDomain] = useState("");
  const [editingDomain, setEditingDomain] = useState<string | null>(null);
  const [newPlaceholder, setNewPlaceholder] = useState({ key: "", value: "" });

  const domains = Object.keys(value);

  const addDomain = () => {
    if (!newDomain.trim()) return;
    
    const updatedValue = {
      ...value,
      [newDomain.trim()]: {}
    };
    onChange(updatedValue);
    setNewDomain("");
    setIsAddingDomain(false);
  };

  const removeDomain = (domain: string) => {
    const updatedValue = { ...value };
    delete updatedValue[domain];
    onChange(updatedValue);
  };

  const addPlaceholder = (domain: string) => {
    if (!newPlaceholder.key.trim() || !newPlaceholder.value.trim()) return;
    
    const updatedValue = {
      ...value,
      [domain]: {
        ...value[domain],
        [newPlaceholder.key.trim()]: newPlaceholder.value.trim()
      }
    };
    onChange(updatedValue);
    setNewPlaceholder({ key: "", value: "" });
  };

  const removePlaceholder = (domain: string, placeholderKey: string) => {
    const updatedValue = {
      ...value,
      [domain]: { ...value[domain] }
    };
    delete updatedValue[domain][placeholderKey];
    onChange(updatedValue);
  };

  const updatePlaceholder = (domain: string, oldKey: string, newKey: string, newValue: string) => {
    const updatedValue = {
      ...value,
      [domain]: { ...value[domain] }
    };
    
    // Remove old key if it changed
    if (oldKey !== newKey) {
      delete updatedValue[domain][oldKey];
    }
    
    updatedValue[domain][newKey] = newValue;
    onChange(updatedValue);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          Sensitive Data Configuration
        </CardTitle>
        <CardDescription>
          Configure placeholder values for sensitive data like usernames and passwords for each domain.
          During test execution, placeholders will be replaced with actual values.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {domains.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No sensitive data configured</p>
            <p className="text-sm">Add a domain to start configuring placeholders</p>
          </div>
        ) : (
          <div className="space-y-4">
            {domains.map((domain) => (
              <DomainCard
                key={domain}
                domain={domain}
                placeholders={value[domain] || {}}
                onRemoveDomain={() => removeDomain(domain)}
                onAddPlaceholder={(key, val) => {
                  setNewPlaceholder({ key, value: val });
                  addPlaceholder(domain);
                }}
                onRemovePlaceholder={(key) => removePlaceholder(domain, key)}
                onUpdatePlaceholder={(oldKey, newKey, newValue) => 
                  updatePlaceholder(domain, oldKey, newKey, newValue)
                }
              />
            ))}
          </div>
        )}

        <Dialog open={isAddingDomain} onOpenChange={setIsAddingDomain}>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              Add Domain
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Domain</DialogTitle>
              <DialogDescription>
                Enter the domain URL where you want to configure sensitive data placeholders.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="domain">Domain URL</Label>
                <Input
                  id="domain"
                  placeholder="https://example.com"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addDomain();
                    }
                  }}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddingDomain(false)}>
                Cancel
              </Button>
              <Button onClick={addDomain}>Add Domain</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}

interface DomainCardProps {
  domain: string;
  placeholders: Record<string, string>;
  onRemoveDomain: () => void;
  onAddPlaceholder: (key: string, value: string) => void;
  onRemovePlaceholder: (key: string) => void;
  onUpdatePlaceholder: (oldKey: string, newKey: string, newValue: string) => void;
}

function DomainCard({
  domain,
  placeholders,
  onRemoveDomain,
  onAddPlaceholder,
  onRemovePlaceholder,
  onUpdatePlaceholder,
}: DomainCardProps) {
  const [isAddingPlaceholder, setIsAddingPlaceholder] = useState(false);
  const [newPlaceholder, setNewPlaceholder] = useState({ key: "", value: "" });
  const [editingPlaceholder, setEditingPlaceholder] = useState<string | null>(null);
  const [editValues, setEditValues] = useState({ key: "", value: "" });

  const placeholderEntries = Object.entries(placeholders);

  const handleAddPlaceholder = () => {
    if (!newPlaceholder.key.trim() || !newPlaceholder.value.trim()) return;
    
    onAddPlaceholder(newPlaceholder.key.trim(), newPlaceholder.value.trim());
    setNewPlaceholder({ key: "", value: "" });
    setIsAddingPlaceholder(false);
  };

  const startEdit = (key: string, value: string) => {
    setEditValues({ key, value });
    setEditingPlaceholder(key);
  };

  const saveEdit = () => {
    if (!editValues.key.trim() || !editValues.value.trim()) return;
    
    onUpdatePlaceholder(editingPlaceholder!, editValues.key.trim(), editValues.value.trim());
    setEditingPlaceholder(null);
    setEditValues({ key: "", value: "" });
  };

  const cancelEdit = () => {
    setEditingPlaceholder(null);
    setEditValues({ key: "", value: "" });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <CardTitle className="text-base">{domain}</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemoveDomain}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {placeholderEntries.length === 0 ? (
          <p className="text-sm text-muted-foreground py-2">
            No placeholders configured for this domain
          </p>
        ) : (
          <div className="space-y-2">
            {placeholderEntries.map(([key, value]) => (
              <div
                key={key}
                className="flex items-center justify-between p-2 border rounded-md bg-muted/50"
              >
                {editingPlaceholder === key ? (
                  <div className="flex items-center gap-2 flex-1">
                    <Input
                      placeholder="Placeholder key"
                      value={editValues.key}
                      onChange={(e) => setEditValues({ ...editValues, key: e.target.value })}
                      className="h-8"
                    />
                    <Input
                      placeholder="Placeholder value"
                      value={editValues.value}
                      onChange={(e) => setEditValues({ ...editValues, value: e.target.value })}
                      className="h-8"
                      type="password"
                    />
                    <Button size="sm" onClick={saveEdit}>
                      Save
                    </Button>
                    <Button size="sm" variant="outline" onClick={cancelEdit}>
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center gap-2 flex-1">
                      <Badge variant="outline">{key}</Badge>
                      <span className="text-sm text-muted-foreground">→</span>
                      <code className="text-sm font-mono bg-background px-2 py-1 rounded">
                        {value.replace(/./g, '•')}
                      </code>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => startEdit(key, value)}
                      >
                        <Edit2 className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onRemovePlaceholder(key)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}

        {isAddingPlaceholder ? (
          <div className="flex items-center gap-2 p-2 border rounded-md bg-background">
            <Input
              placeholder="x_username, x_password, etc."
              value={newPlaceholder.key}
              onChange={(e) => setNewPlaceholder({ ...newPlaceholder, key: e.target.value })}
              className="h-8"
            />
            <Input
              placeholder="Actual value"
              value={newPlaceholder.value}
              onChange={(e) => setNewPlaceholder({ ...newPlaceholder, value: e.target.value })}
              className="h-8"
              type="password"
            />
            <Button size="sm" onClick={handleAddPlaceholder}>
              Add
            </Button>
            <Button size="sm" variant="outline" onClick={() => setIsAddingPlaceholder(false)}>
              Cancel
            </Button>
          </div>
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAddingPlaceholder(true)}
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Placeholder
          </Button>
        )}
      </CardContent>
    </Card>
  );
}