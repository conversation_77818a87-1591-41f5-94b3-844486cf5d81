import { NextRequest, NextResponse } from 'next/server'
import { SupabaseAuthServerService } from '@/lib/auth-supabase-server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug auth endpoint called')

    const user = await SupabaseAuthServerService.getCurrentUserServer()
    console.log('🔍 Server-side user:', user)

    if (!user) {
      console.log('❌ No user found on server side')
      return NextResponse.json({
        authenticated: false,
        error: 'No user found'
      }, { status: 401 })
    }

    console.log('✅ User authenticated on server side:', user.email)
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    })
  } catch (error) {
    console.error('❌ Debug auth error:', error)
    return NextResponse.json({
      authenticated: false,
      error: (error as Error).message
    }, { status: 500 })
  }
}