import { fetchExternalApi } from '../core/fetch';

/**
 * Calls the OpenAI API directly with custom messages and configuration
 * 
 * @param messages - Array of messages for the conversation
 * @param apiKey - OpenAI API key for authentication
 * @param model - OpenAI model to use (defaults to gpt-3.5-turbo)
 * @returns Promise resolving to OpenAI API response
 * 
 * @example
 * ```typescript
 * const response = await callOpenAI([
 *   { role: "system", content: "You are a helpful assistant" },
 *   { role: "user", content: "Generate a test case for login functionality" }
 * ], "sk-...", "gpt-4");
 * 
 * console.log(response.choices[0].message.content);
 * ```
 */
export async function callOpenAI(messages: any[], apiKey: string, model: string = 'gpt-3.5-turbo'): Promise<any> {
  return fetchExternalApi('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${api<PERSON>ey}`,
    },
    body: JSON.stringify({
      model,
      messages,
      max_tokens: 2000,
      temperature: 0.7
    }),
  });
}