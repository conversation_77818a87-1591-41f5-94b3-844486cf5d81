import { test, expect } from '@playwright/test';
import { ensureLoggedIn } from './helpers/auth';
import { 
  navigateToProvidersPage,
  createProvider,
  editProvider,
  deleteProvider,
  searchProviders,
  clearSearch,
  verifyProviderInTable,
  mockProviders,
  getProviderCount
} from './helpers/llm-providers';

test.describe('LLM Providers CRUD Operations', () => {
  test.beforeEach(async ({ page }) => {
    // Ensure user is logged in
    await ensureLoggedIn(page);
    
    // Navigate to the LLM providers page
    await navigateToProvidersPage(page);
  });

  test('should display LLM providers list', async ({ page }) => {
    // Check if the page title is correct
    await expect(page.locator('h1').filter({ hasText: 'LLM Providers' })).toBeVisible();
    
    // Check if the create button is visible
    await expect(page.locator('button:has-text("Add Provider")')).toBeVisible();
    
    // Check if the table is visible
    await expect(page.locator('table')).toBeVisible();
    
    // Check table headers
    await expect(page.locator('th:has-text("Name")')).toBeVisible();
    await expect(page.locator('th:has-text("Type")')).toBeVisible();
    await expect(page.locator('th:has-text("Status")')).toBeVisible();
    await expect(page.locator('th:has-text("Base URL")')).toBeVisible();
    await expect(page.locator('th:has-text("API Version")')).toBeVisible();
    await expect(page.locator('th:has-text("Rate Limits")')).toBeVisible();
    await expect(page.locator('th:has-text("Actions")')).toBeVisible();
    
    // Check if search functionality is available
    await expect(page.locator('[placeholder*="Search"]')).toBeVisible();
  });

  test('should create a new LLM provider', async ({ page }) => {
    const initialCount = await getProviderCount(page);
    
    // Create a new provider using helper
    await createProvider(page, mockProviders.openai);
    
    // Verify the provider count increased
    const newCount = await getProviderCount(page);
    expect(newCount).toBe(initialCount + 1);
    
    // Verify the provider details in table
    await verifyProviderInTable(page, mockProviders.openai);
  });

  test('should edit an existing LLM provider', async ({ page }) => {
    // First, create a provider to edit
    await createProvider(page, mockProviders.anthropic);
    
    // Edit the provider
    const updatedProvider = {
      ...mockProviders.anthropic,
      name: 'Updated Anthropic Provider',
      description: 'Updated description for testing'
    };
    
    await editProvider(page, mockProviders.anthropic.name, updatedProvider);
    
    // Verify the changes
    await verifyProviderInTable(page, updatedProvider);
    
    // Verify old name is no longer visible
    await expect(page.locator(`text=${mockProviders.anthropic.name}`)).not.toBeVisible();
  });

  test('should delete an LLM provider', async ({ page }) => {
    const initialCount = await getProviderCount(page);
    
    // First, create a provider to delete
    await createProvider(page, mockProviders.google);
    
    // Verify provider count increased
    const countAfterCreate = await getProviderCount(page);
    expect(countAfterCreate).toBe(initialCount + 1);
    
    // Delete the provider
    await deleteProvider(page, mockProviders.google.name);
    
    // Verify provider count returned to original
    const finalCount = await getProviderCount(page);
    expect(finalCount).toBe(initialCount);
    
    // Verify the provider is no longer visible
    await verifyProviderInTable(page, mockProviders.google, false);
  });

  test('should validate form fields', async ({ page }) => {
    // Click create provider button
    await page.click('button:has-text("Create Provider")');
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Try to submit empty form
    await page.click('button:has-text("Create Provider")');
    
    // Check for validation errors
    await expect(page.locator('text=Name is required')).toBeVisible();
    await expect(page.locator('text=Type is required')).toBeVisible();
    
    // Fill name but leave type empty
    await page.fill('[name="name"]', 'Test Provider');
    await page.click('button:has-text("Create Provider")');
    
    // Name error should be gone, type error should remain
    await expect(page.locator('text=Name is required')).not.toBeVisible();
    await expect(page.locator('text=Type is required')).toBeVisible();
    
    // Test invalid URL
    await page.selectOption('[name="type"]', 'openai');
    await page.fill('[name="baseUrl"]', 'invalid-url');
    await page.click('button:has-text("Create Provider")');
    
    // Should show URL validation error
    await expect(page.locator('text=Please enter a valid URL')).toBeVisible();
    
    // Close dialog
    await page.keyboard.press('Escape');
  });

  test('should search and filter providers', async ({ page }) => {
    // Create multiple providers for testing search
    const testProviders = [
      { ...mockProviders.openai, name: 'OpenAI Search Test' },
      { ...mockProviders.anthropic, name: 'Anthropic Search Test' },
      { ...mockProviders.google, name: 'Google Search Test' }
    ];
    
    // Create all test providers
    for (const provider of testProviders) {
      await createProvider(page, provider);
    }
    
    // Test search by name
    await searchProviders(page, 'OpenAI');
    await expect(page.locator('text=OpenAI Search Test')).toBeVisible();
    await expect(page.locator('text=Anthropic Search Test')).not.toBeVisible();
    await expect(page.locator('text=Google Search Test')).not.toBeVisible();
    
    // Clear search and verify all providers are visible
    await clearSearch(page);
    await expect(page.locator('text=OpenAI Search Test')).toBeVisible();
    await expect(page.locator('text=Anthropic Search Test')).toBeVisible();
    await expect(page.locator('text=Google Search Test')).toBeVisible();
    
    // Search by type
    await searchProviders(page, 'anthropic');
    await expect(page.locator('text=Anthropic Search Test')).toBeVisible();
    await expect(page.locator('text=OpenAI Search Test')).not.toBeVisible();
    await expect(page.locator('text=Google Search Test')).not.toBeVisible();
    
    // Search by description
    await searchProviders(page, 'Google provider');
    await expect(page.locator('text=Google Search Test')).toBeVisible();
    await expect(page.locator('text=OpenAI Search Test')).not.toBeVisible();
    await expect(page.locator('text=Anthropic Search Test')).not.toBeVisible();
  });
});