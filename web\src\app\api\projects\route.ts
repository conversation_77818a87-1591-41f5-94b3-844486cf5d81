import { NextRequest, NextResponse } from 'next/server'
import { ProjectsApiHandler } from '@/lib/api-supabase/projects'

export async function GET(request: NextRequest) {
  const handler = new ProjectsApiHandler()
  return handler.handleRequest(async () => {
    const result = await handler.getProjects()
    return NextResponse.json(result)
  })
}

export async function POST(request: NextRequest) {
  const handler = new ProjectsApiHandler()
  return handler.handleRequest(async () => {
    const body = await request.json()
    const result = await handler.createProject(body)
    return NextResponse.json(result, { status: 201 })
  })
}