/**
 * Integration Module - QAK External Service Integrations
 * 
 * This module provides comprehensive integration functionality for external
 * services and platforms, enabling seamless data exchange and workflow automation.
 * 
 * Features:
 * - Linear Integration: Complete workspace management, OAuth flow, and document sync
 * - Knowledge Base: Document upload, management, and AI-powered test generation
 * - External APIs: Direct integration with third-party services like OpenAI
 * - Authentication: Secure token handling and workspace authorization
 * - Data Sync: Real-time synchronization with external platforms
 * - Document Management: File upload, storage, and retrieval for knowledge base
 * 
 * @module integration
 */

// Linear Integration
export {
  callLinearApi,
  getLinearWorkspaces,
  getLinearAuthUrl,
  handleLinearAuthCallback,
  deleteLinearWorkspace,
  syncLinearWorkspace,
  updateLinearSyncConfig,
  getLinearDocuments,
  type LinearDocumentParams
} from './linear';

// Knowledge Base Integration
export {
  uploadKnowledgeBaseDocument,
  getKnowledgeBaseDocuments,
  generateManualTestsFromKB
} from './knowledgeBase';

// External API Integration
export {
  callOpenAI
} from './external';