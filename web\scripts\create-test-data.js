/**
 * Script para crear datos de prueba básicos
 * Ejecutar con: node scripts/create-test-data.js
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5001';

// Función para hacer peticiones HTTP
async function fetchApi(url, options = {}) {
  const fullUrl = `${API_BASE_URL}/api${url}`;
  
  const response = await fetch(fullUrl, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': 'true',
      ...(options.headers || {}),
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`HTTP ${response.status}: ${errorText}`);
  }

  return response.json();
}

// Función para crear tenant de prueba
async function createTestTenant() {
  console.log('🏢 Creando tenant de prueba...');
  
  const tenantData = {
    name: 'Empresa Demo',
    slug: 'empresa-demo',
    description: 'Empresa de demostración para pruebas',
    contactEmail: '<EMAIL>',
    isActive: true
  };

  try {
    const result = await fetchApi('/Tenant', {
      method: 'POST',
      body: JSON.stringify(tenantData)
    });
    
    console.log('✅ Tenant creado:', result);
    return result;
  } catch (error) {
    console.error('❌ Error creando tenant:', error.message);
    throw error;
  }
}

// Función para crear tenant con autenticación
async function createTestTenantWithAuth(token) {
  console.log('🏢 Creando tenant adicional de prueba...');
  
  const tenantData = {
    name: 'Tenant Pruebas',
    slug: 'tenant-pruebas',
    description: 'Tenant adicional para pruebas de funcionalidad',
    contactEmail: '<EMAIL>',
    isActive: true
  };

  try {
    const result = await fetchApi('/Tenant', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(tenantData)
    });
    
    console.log('✅ Tenant adicional creado:', result.data || result);
    return result.data || result;
  } catch (error) {
    console.error('❌ Error creando tenant adicional:', error.message);
    throw error;
  }
}

// Función para crear usuario de prueba
async function createTestUser() {
  console.log('👤 Creando usuario de prueba...');
  
  const userData = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'TestPassword123!',
    firstName: 'Usuario',
    lastName: 'Demo',
    role: 1, // UserRole.User = 1
    tenantId: 'default'
  };

  try {
    const result = await fetchApi('/Auth/register', {
      method: 'POST',
      body: JSON.stringify(userData)
    });
    
    console.log('✅ Usuario creado:', result);
    return result;
  } catch (error) {
    if (error.message.includes('already exists')) {
      console.log('⚠️ Usuario ya existe, continuando...');
      return { email: userData.email, message: 'User already exists' };
    }
    console.error('❌ Error creando usuario:', error.message);
    throw error;
  }
}

// Función para autenticarse y obtener token
async function authenticateUser() {
  console.log('🔐 Autenticando usuario...');
  
  const loginData = {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  };

  try {
    const result = await fetchApi('/Auth/login', {
      method: 'POST',
      body: JSON.stringify(loginData)
    });
    
    console.log('🔐 Respuesta de login:', result);
    
    // Check if email verification is required
    if (result.requiresEmailVerification) {
      console.log('📧 Verificación de email requerida, usando endpoint de desarrollo...');
      
      // Use development endpoint to verify email
      await fetchApi('/Auth/dev-verify-email', {
        method: 'POST',
        body: JSON.stringify({ email: '<EMAIL>' })
      });
      
      console.log('✅ Email verificado para desarrollo, reintentando login...');
      
      // Retry login after verification
      const retryResult = await fetchApi('/Auth/login', {
        method: 'POST',
        body: JSON.stringify(loginData)
      });
      
      console.log('✅ Usuario autenticado después de verificación');
      return retryResult.token;
    }
    
    console.log('✅ Usuario autenticado');
    return result.token;
  } catch (error) {
    console.error('❌ Error autenticando usuario:', error.message);
    throw error;
  }
}

// Función para crear proyecto de prueba
async function createTestProject(authToken) {
  console.log('📁 Creando proyecto de prueba...');
  
  const projectData = {
    name: 'Proyecto Demo',
    description: 'Proyecto de prueba para desarrollo y testing',
    tags: ['demo', 'testing', 'development']
  };

  try {
    const result = await fetchApi('/projects', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(projectData)
    });
    
    console.log('✅ Proyecto creado:', result);
    return result;
  } catch (error) {
    console.error('❌ Error creando proyecto:', error.message);
    throw error;
  }
}

// Función para inicializar el sistema usando bootstrap
async function initializeSystem() {
  console.log('🔧 Inicializando sistema con bootstrap...');
  
  const initData = {
    bootstrapKey: 'memasenelparking',
    companyName: 'Empresa Demo',
    adminName: 'Admin Demo',
    adminEmail: '<EMAIL>',
    adminPassword: 'AdminPassword123!'
  };

  try {
    const result = await fetchApi('/bootstrap/initialize-system', {
      method: 'POST',
      body: JSON.stringify(initData)
    });
    
    console.log('✅ Sistema inicializado:', result);
    return result;
  } catch (error) {
    console.error('❌ Error inicializando sistema:', error.message);
    throw error;
  }
}

// Función para obtener token de emergencia
async function getEmergencyToken() {
  console.log('🆘 Obteniendo token de emergencia...');
  
  try {
    const result = await fetchApi('/bootstrap/emergency-access', {
      method: 'POST',
      body: JSON.stringify({ bootstrapKey: 'memasenelparking' })
    });
    
    console.log('✅ Token de emergencia obtenido');
    return result.data?.token || result.token;
  } catch (error) {
    console.error('❌ Error obteniendo token de emergencia:', error.message);
    throw error;
  }
}

// Función principal
async function main() {
  console.log('🚀 Iniciando creación de datos de prueba...');
  console.log('🌐 API Base URL:', API_BASE_URL);
  
  try {
    // 1. Intentar inicializar el sistema (esto creará el primer tenant y admin)
    let initResult;
    try {
      initResult = await initializeSystem();
    } catch (error) {
      if (error.message.includes('401') || error.message.includes('Invalid bootstrap key') || error.message.includes('500')) {
        console.log('⚠️ Sistema ya inicializado o error en inicialización');
        initResult = null;
      } else {
        throw error;
      }
    }
    
    // 2. Obtener token de emergencia para operaciones administrativas
    const emergencyToken = await getEmergencyToken();
    
    // 3. Crear tenant adicional para pruebas
    let tenant;
    try {
      tenant = await createTestTenantWithAuth(emergencyToken);
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠️ Tenant ya existe, continuando...');
        tenant = { name: 'Tenant Pruebas' }; // Mock object for display
      } else {
        throw error;
      }
    }
    
    // 4. Crear usuario de prueba
    const user = await createTestUser();
    
    // 5. Autenticar usuario de prueba
    const authToken = await authenticateUser();
    
    // 6. Crear proyecto
    const project = await createTestProject(authToken);
    
    console.log('\n🎉 ¡Datos de prueba creados exitosamente!');
    console.log('\n📋 Resumen:');
    console.log('- Sistema inicializado:', initResult ? 'Sí' : 'Ya existía');
    console.log('- Tenant:', tenant?.name || 'Empresa Demo');
    console.log('- Usuario:', user?.email || '<EMAIL>');
    console.log('- Proyecto:', project?.name || 'Proyecto Demo');
    console.log('\n🔑 Credenciales de acceso:');
    console.log('- Admin: <EMAIL> / AdminPassword123!');
    console.log('- Usuario: <EMAIL> / TestPassword123!');
    
  } catch (error) {
    console.error('\n💥 Error en la creación de datos de prueba:', error.message);
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}

module.exports = {
  createTestTenant,
  createTestUser,
  authenticateUser,
  createTestProject
};