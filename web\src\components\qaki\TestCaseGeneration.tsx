"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getSuitesByProjectId, createSuite, createTestCase } from '@/lib/api/projects';
import { generateManualTestCases } from '@/lib/api/ai';
import { 
  ArrowLeft, 
  TestTube, 
  <PERSON>rkles, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Copy,
  Download,
  RefreshCw,
  Upload,
  Trash2,
  Edit,
  Eye,
  FileText,
  Package
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SelectedProject {
  id: string;
  name: string;
  description?: string;
}

interface TestCaseGenerationProps {
  project: SelectedProject;
  onBack: () => void;
}

interface GeneratedTestCase {
  name: string;
  description: string;
  steps: string[];
  expectedResult: string;
  priority: string;
  type: string;
  aiGeneratedContent?: string;
}

interface TestGenerationResult {
  testCases: GeneratedTestCase[];
  story: Record<string, any>;
  totalGenerated: number;
  tenantId?: string;
  generationMetadata?: {
    approach: string;
    includes: string[];
    additionalData: Record<string, any>;
  };
  llmMetadata?: {
    provider: string;
    model: string;
    usage?: {
      promptTokens?: number;
      completionTokens?: number;
      totalTokens?: number;
      estimatedCost?: number;
    };
  };
}

interface TestSuite {
  id: string;
  name: string;
  description?: string;
  testCases?: any[];
}

const testTypes = [
  { value: 'functional', label: 'Funcional' },
  { value: 'integration', label: 'Integración' },
  { value: 'ui', label: 'Interfaz de Usuario' },
  { value: 'api', label: 'API' },
  { value: 'security', label: 'Seguridad' },
  { value: 'performance', label: 'Rendimiento' },
];

const priorities = [
  { value: 'high', label: 'Alta', color: 'bg-red-500' },
  { value: 'medium', label: 'Media', color: 'bg-yellow-500' },
  { value: 'low', label: 'Baja', color: 'bg-green-500' },
];

export function TestCaseGeneration({ project, onBack }: TestCaseGenerationProps) {
  const [userStory, setUserStory] = useState('');
  const [additionalContext, setAdditionalContext] = useState('');
  const [generationResult, setGenerationResult] = useState<TestGenerationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedTestCases, setSelectedTestCases] = useState<Set<number>>(new Set());
  const [suites, setSuites] = useState<TestSuite[]>([]);
  const [selectedSuite, setSelectedSuite] = useState<string>('');
  const [newSuiteName, setNewSuiteName] = useState('');
  const [exporting, setExporting] = useState(false);
  const [tenantId, setTenantId] = useState('default-tenant');
  const { toast } = useToast();

  const handleGenerateTests = async () => {
    if (!userStory.trim()) {
      toast({
        title: "Historia requerida",
        description: "Por favor ingresa una historia de usuario para generar test cases",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      setGenerationResult(null);

      // Use the API function instead of direct fetch
      const result = await generateManualTestCases({
        userStory: userStory.trim(),
        language: 'es' // Default to Spanish
      });

      if (result.manualTestCases && Array.isArray(result.manualTestCases)) {
        // Convert API response to local format
        const testCases = result.manualTestCases.map((testCase: any, index: number) => {
          if (typeof testCase === 'string') {
            // Handle string format
            return {
              id: `test-${index + 1}`,
              name: `Test Case ${index + 1}`,
              description: testCase,
              steps: [testCase],
              expectedResult: 'To be defined',
              priority: 'medium',
              type: 'functional'
            };
          } else if (typeof testCase === 'object' && testCase !== null) {
            // Handle object format
            return {
              id: testCase.id || `test-${index + 1}`,
              name: testCase.name || testCase.title || `Test Case ${index + 1}`,
              description: testCase.description || testCase.summary || '',
              steps: Array.isArray(testCase.steps) ? testCase.steps : [testCase.description || ''],
              expectedResult: testCase.expectedResult || testCase.expected_result || 'To be defined',
              priority: testCase.priority || 'medium',
              type: testCase.type || 'functional'
            };
          }
          return null;
        }).filter((testCase): testCase is any => testCase !== null);

        setGenerationResult({
          testCases,
          story: {
            userStory: userStory,
            context: additionalContext || undefined,
            projectId: project.id,
            projectName: project.name,
          },
          totalGenerated: testCases.length,
          generationMetadata: {
            approach: 'AI-generated from QAKI Assistant',
            includes: ['manual-tests'],
            additionalData: { generatedAt: new Date().toISOString() }
          }
        });

        // Select all test cases by default
        const allIndices = new Set<number>(testCases.map((_: any, index: number) => index));
        setSelectedTestCases(allIndices);
        
        toast({
          title: "Test cases generados",
          description: `Se generaron ${testCases.length} casos de prueba exitosamente`,
        });

        // Load available suites for export
        await loadProjectSuites();
      } else {
        throw new Error('No test cases received from API');
      }

    } catch (error) {
      console.error('Test generation error:', error);
      toast({
        title: "Error en la generación",
        description: error instanceof Error ? error.message : 'Error desconocido al generar test cases',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadProjectSuites = async () => {
    try {
      const apiSuites = await getSuitesByProjectId(project.id);
      // Map API suites to local format
      const suiteList = apiSuites.map(suite => ({
        id: suite.suite_id,
        name: suite.name,
        description: suite.description,
        testCount: suite.test_cases?.length || 0
      }));
      setSuites(suiteList);
    } catch (error) {
      console.error('Error loading suites:', error);
    }
  };

  const handleExportTests = async () => {
    if (!generationResult || selectedTestCases.size === 0) {
      toast({
        title: "Selección requerida",
        description: "Por favor selecciona al menos un test case para exportar",
        variant: "destructive",
      });
      return;
    }

    const targetSuite = selectedSuite || newSuiteName.trim();
    if (!targetSuite) {
      toast({
        title: "Suite requerida",
        description: "Por favor selecciona una suite existente o crea una nueva",
        variant: "destructive",
      });
      return;
    }

    try {
      setExporting(true);

      // Get selected test cases
      const selectedTests = Array.from(selectedTestCases).map(index => 
        generationResult.testCases[index]
      );

      // First, create or get the suite
      let suiteId = selectedSuite;
      if (!selectedSuite && newSuiteName.trim()) {
        // Create new suite
        const newSuite = await createSuite(project.id, {
          project_id: project.id,
          name: newSuiteName.trim(),
          description: `Suite creada automáticamente desde QAKI Assistant - ${new Date().toLocaleDateString('es-ES')}`,
          type: 'manual',
          tags: ['qaki-assistant'],
          execution_times: 1
        });
        suiteId = newSuite.suite_id;
      }

      // Create test cases in the suite
      const createPromises = selectedTests.map(async (testCase) => {
        const testCaseData = {
          name: testCase.name,
          description: testCase.description,
          instructions: Array.isArray(testCase.steps) ? testCase.steps.join('\n') : testCase.steps,
          user_story: userStory,
          url: '',
          priority: 'Medium' as const,
          type: 'Functional' as const,
          status: 'Not Executed',
          tags: ['ai-generated', 'qaki-assistant'],
        };

        return await createTestCase(project.id, suiteId, testCaseData);
      });

      await Promise.all(createPromises);

      toast({
        title: "Export exitoso",
        description: `Se exportaron ${selectedTests.length} test cases a la suite "${targetSuite}"`,
      });

      // Clear selections
      setSelectedTestCases(new Set());
      setSelectedSuite('');
      setNewSuiteName('');

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Error en export",
        description: error instanceof Error ? error.message : 'Error desconocido',
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };

  const toggleTestCaseSelection = (index: number) => {
    const newSelection = new Set(selectedTestCases);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedTestCases(newSelection);
  };

  const selectAllTestCases = () => {
    if (!generationResult) return;
    const allIndices = new Set(generationResult.testCases.map((_, index) => index));
    setSelectedTestCases(allIndices);
  };

  const deselectAllTestCases = () => {
    setSelectedTestCases(new Set());
  };

  const copyTestCase = (testCase: GeneratedTestCase) => {
    const text = `Nombre: ${testCase.name}

Descripción: ${testCase.description}

Pasos:
${testCase.steps.map((step, i) => `${i + 1}. ${step}`).join('\n')}

Resultado esperado: ${testCase.expectedResult}

Prioridad: ${testCase.priority}
Tipo: ${testCase.type}`;

    navigator.clipboard.writeText(text);
    toast({
      title: "Copiado",
      description: "Test case copiado al portapapeles",
    });
  };

  const downloadTestCases = () => {
    if (!generationResult) return;

    const selectedTests = Array.from(selectedTestCases).map(index => 
      generationResult.testCases[index]
    );

    const content = `Test Cases Generados - ${project.name}
Fecha: ${new Date().toLocaleString('es-ES')}
Historia original: ${userStory}

${selectedTests.map((testCase, i) => `
=== Test Case ${i + 1}: ${testCase.name} ===

Descripción: ${testCase.description}

Pasos:
${testCase.steps.map((step, j) => `${j + 1}. ${step}`).join('\n')}

Resultado esperado: ${testCase.expectedResult}

Prioridad: ${testCase.priority}
Tipo: ${testCase.type}
`).join('\n')}`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `test-cases-${project.name}-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getPriorityColor = (priority: string) => {
    const priorityConfig = priorities.find(p => p.value === priority);
    return priorityConfig?.color || 'bg-gray-500';
  };

  const reset = () => {
    setUserStory('');
    setAdditionalContext('');
    setGenerationResult(null);
    setSelectedTestCases(new Set());
    setSelectedSuite('');
    setNewSuiteName('');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft size={16} />
            </Button>
            <div className="flex items-center gap-2">
              <TestTube size={20} className="text-green-500" />
              <CardTitle>Generación de Test Cases - {project.name}</CardTitle>
            </div>
          </div>
          <CardDescription>
            Genera casos de prueba automatizados a partir de historias de usuario con contexto del Knowledge Base
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="generate" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="generate">Generar Test Cases</TabsTrigger>
          <TabsTrigger value="export" disabled={!generationResult}>
            Revisar y Exportar ({selectedTestCases.size})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Input Section */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Historia de Usuario</CardTitle>
                  <CardDescription>
                    Describe la funcionalidad para generar test cases
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="story">Historia de Usuario *</Label>
                    <Textarea
                      id="story"
                      placeholder="Como [rol], quiero [funcionalidad] para [beneficio]..."
                      value={userStory}
                      onChange={(e) => setUserStory(e.target.value)}
                      rows={6}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="context">Contexto Adicional</Label>
                    <Textarea
                      id="context"
                      placeholder="Criterios de aceptación, casos edge, restricciones técnicas..."
                      value={additionalContext}
                      onChange={(e) => setAdditionalContext(e.target.value)}
                      rows={4}
                      className="mt-1"
                    />
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button 
                      onClick={handleGenerateTests} 
                      disabled={loading || !userStory.trim()}
                      className="flex-1"
                    >
                      {loading ? (
                        <>
                          <RefreshCw size={16} className="mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Sparkles size={16} className="mr-2" />
                          Generar Test Cases
                        </>
                      )}
                    </Button>
                    {generationResult && (
                      <Button variant="outline" onClick={reset}>
                        Nuevo
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Tips */}
              <Alert>
                <TestTube size={16} />
                <AlertDescription>
                  <strong>Tips para mejores test cases:</strong>
                  <ul className="mt-2 text-sm space-y-1">
                    <li>• Incluye criterios de aceptación claros</li>
                    <li>• Menciona casos edge y escenarios de error</li>
                    <li>• Especifica requisitos de UI o API</li>
                    <li>• Incluye validaciones de seguridad si aplica</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </div>

            {/* Results Preview */}
            <div className="space-y-6">
              {loading && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center">
                        <RefreshCw size={32} className="mx-auto mb-4 animate-spin text-muted-foreground" />
                        <p className="text-muted-foreground">Generando test cases con IA...</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Analizando historia y consultando Knowledge Base
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {generationResult && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <CheckCircle size={20} className="text-green-500" />
                        Test Cases Generados ({generationResult.totalGenerated})
                      </CardTitle>
                      <Button size="sm" onClick={() => {
                        const tabsList = document.querySelector('[role="tablist"]');
                        const exportTab = tabsList?.querySelector('[value="export"]') as HTMLElement;
                        exportTab?.click();
                      }}>
                        Revisar y Exportar
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {generationResult.testCases.slice(0, 3).map((testCase, index) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-sm">{testCase.name}</h4>
                            <div className="flex gap-1">
                              <Badge variant="secondary" className="text-xs">
                                {testCase.type}
                              </Badge>
                              <div className={`w-2 h-2 rounded-full ${getPriorityColor(testCase.priority)} mt-1`} />
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {testCase.description}
                          </p>
                          <div className="text-xs text-muted-foreground mt-2">
                            {testCase.steps.length} pasos
                          </div>
                        </div>
                      ))}
                      
                      {generationResult.testCases.length > 3 && (
                        <div className="text-center text-sm text-muted-foreground">
                          +{generationResult.testCases.length - 3} test cases más...
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {!loading && !generationResult && (
                <Card className="border-dashed">
                  <CardContent className="pt-6">
                    <div className="text-center py-8 text-muted-foreground">
                      <TestTube size={48} className="mx-auto mb-4" />
                      <p>Los test cases generados aparecerán aquí</p>
                      <p className="text-sm mt-2">
                        Completa la historia de usuario y haz clic en "Generar Test Cases"
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="export" className="space-y-6">
          {generationResult && (
            <>
              {/* Export Controls */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload size={20} />
                    Exportar al Proyecto
                  </CardTitle>
                  <CardDescription>
                    Selecciona los test cases y la suite de destino
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-4">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={selectAllTestCases}
                      disabled={selectedTestCases.size === generationResult.testCases.length}
                    >
                      Seleccionar todos
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={deselectAllTestCases}
                      disabled={selectedTestCases.size === 0}
                    >
                      Deseleccionar todos
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={downloadTestCases}
                      disabled={selectedTestCases.size === 0}
                    >
                      <Download size={16} className="mr-2" />
                      Descargar
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="existing-suite">Suite existente</Label>
                      <Select value={selectedSuite} onValueChange={setSelectedSuite}>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Seleccionar suite..." />
                        </SelectTrigger>
                        <SelectContent>
                          {suites.map((suite) => (
                            <SelectItem key={suite.id} value={suite.id}>
                              <div>
                                <div className="font-medium">{suite.name}</div>
                                {suite.description && (
                                  <div className="text-xs text-muted-foreground">{suite.description}</div>
                                )}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="new-suite">O crear nueva suite</Label>
                      <Input
                        id="new-suite"
                        placeholder="Nombre de la nueva suite..."
                        value={newSuiteName}
                        onChange={(e) => setNewSuiteName(e.target.value)}
                        disabled={!!selectedSuite}
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <Button 
                    onClick={handleExportTests}
                    disabled={exporting || selectedTestCases.size === 0 || (!selectedSuite && !newSuiteName.trim())}
                    className="w-full"
                  >
                    {exporting ? (
                      <>
                        <RefreshCw size={16} className="mr-2 animate-spin" />
                        Exportando...
                      </>
                    ) : (
                      <>
                        <Package size={16} className="mr-2" />
                        Exportar {selectedTestCases.size} Test Cases
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* Test Cases List */}
              <div className="space-y-4">
                {generationResult.testCases.map((testCase, index) => (
                  <Card key={index} className={`transition-all ${
                    selectedTestCases.has(index) ? 'ring-2 ring-primary/50 bg-primary/5' : ''
                  }`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={selectedTestCases.has(index)}
                          onCheckedChange={() => toggleTestCaseSelection(index)}
                          className="mt-1"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <CardTitle className="text-lg">{testCase.name}</CardTitle>
                            <div className="flex gap-2">
                              <Badge variant="secondary">{testCase.type}</Badge>
                              <div className="flex items-center gap-1">
                                <div className={`w-3 h-3 rounded-full ${getPriorityColor(testCase.priority)}`} />
                                <span className="text-sm capitalize">{testCase.priority}</span>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyTestCase(testCase)}
                              >
                                <Copy size={16} />
                              </Button>
                            </div>
                          </div>
                          <CardDescription className="mt-1">
                            {testCase.description}
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h5 className="font-medium text-sm mb-2">Pasos:</h5>
                          <ol className="list-decimal list-inside space-y-1 text-sm">
                            {testCase.steps.map((step, stepIndex) => (
                              <li key={stepIndex} className="text-muted-foreground">
                                {step}
                              </li>
                            ))}
                          </ol>
                        </div>
                        
                        <div>
                          <h5 className="font-medium text-sm mb-2">Resultado esperado:</h5>
                          <p className="text-sm text-muted-foreground bg-muted/50 p-2 rounded">
                            {testCase.expectedResult}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}