import { fetchApi } from '../core/fetch';
import type {
  CodegenExecutionInfo,
  CodegenExecutionListResponse,
  CodegenExecutionRequest,
  CodegenExecutionResponse,
  CodegenHealthResponse,
  CodegenHistoryResponse,
  CodegenHistorySessionDetailResponse,
  CodegenSessionInfo,
  CodegenSessionListResponse,
  CodegenStatsResponse,
  CodegenTestCaseRequest,
  PlaywrightCodegenRequest
} from '@/lib/types';

/**
 * Start a new codegen session
 */
export const startCodegenSession = (data: PlaywrightCodegenRequest): Promise<CodegenSessionInfo> =>
  fetchApi<CodegenSessionInfo>('/codegen/start', {
    method: 'POST',
    body: JSON.stringify(data)
  });

/**
 * Get session status
 */
export const getCodegenSession = (sessionId: string): Promise<CodegenSessionInfo> =>
  fetchApi<CodegenSessionInfo>(`/codegen/session/${sessionId}`);

/**
 * Stop session
 */
export const stopCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> =>
  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}/stop`, {
    method: 'POST'
  });

/**
 * Get generated code
 */
export const getCodegenGeneratedCode = (sessionId: string): Promise<{
  session_id: string;
  status: string;
  generated_code?: string;
  target_language: string;
  created_at: string;
  updated_at: string;
}> => fetchApi(`/codegen/session/${sessionId}/code`);

/**
 * Convert to test case
 */
export const convertCodegenToTestcase = (data: CodegenTestCaseRequest): Promise<Record<string, any>> =>
  fetchApi<Record<string, any>>(`/codegen/session/${data.session_id}/convert`, {
    method: 'POST',
    body: JSON.stringify(data)
  });

/**
 * Cleanup session
 */
export const cleanupCodegenSession = (sessionId: string): Promise<{ message: string; session_id: string }> =>
  fetchApi<{ message: string; session_id: string }>(`/codegen/session/${sessionId}`, {
    method: 'DELETE'
  });

/**
 * List active sessions
 */
export const listCodegenSessions = (): Promise<CodegenSessionListResponse> =>
  fetchApi<CodegenSessionListResponse>('/codegen/sessions');

/**
 * Get statistics
 */
export const getCodegenStats = (): Promise<CodegenStatsResponse> =>
  fetchApi<CodegenStatsResponse>('/codegen/stats');

/**
 * Bulk cleanup
 */
export const bulkCleanupCodegenSessions = (): Promise<{
  message: string;
  sessions_cleaned: number;
  sessions_remaining: number;
}> => fetchApi('/codegen/bulk-cleanup', { method: 'POST' });

/**
 * Health check
 */
export const getCodegenHealth = (): Promise<CodegenHealthResponse> =>
  fetchApi<CodegenHealthResponse>('/codegen/health');

/**
 * History endpoints
 */
export const getCodegenHistory = (limit?: number): Promise<CodegenHistoryResponse> =>
  fetchApi<CodegenHistoryResponse>(`/codegen/history${limit ? `?limit=${limit}` : ''}`);

export const getCodegenHistorySession = (sessionId: string): Promise<CodegenHistorySessionDetailResponse> =>
  fetchApi<CodegenHistorySessionDetailResponse>(`/codegen/history/${sessionId}`);

/**
 * Execution endpoints
 */
export const executeCodegenTest = (data: CodegenExecutionRequest): Promise<CodegenExecutionResponse> =>
  fetchApi<CodegenExecutionResponse>('/codegen/execute', {
    method: 'POST',
    body: JSON.stringify(data)
  });

export const getCodegenExecution = (executionId: string): Promise<CodegenExecutionInfo> =>
  fetchApi<CodegenExecutionInfo>(`/codegen/execution/${executionId}`);

export const listCodegenExecutions = (): Promise<CodegenExecutionListResponse> =>
  fetchApi<CodegenExecutionListResponse>('/codegen/executions');

export const stopCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> =>
  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}/stop`, {
    method: 'POST'
  });

export const cleanupCodegenExecution = (executionId: string): Promise<{ message: string; execution_id: string }> =>
  fetchApi<{ message: string; execution_id: string }>(`/codegen/execution/${executionId}`, {
    method: 'DELETE'
  });