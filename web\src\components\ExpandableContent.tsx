"use client";

import { useState, type ReactNode } from 'react';
import { Button } from '@/components/ui/button';

interface ExpandableContentProps {
  shortContent: ReactNode;
  fullContent: ReactNode;
  buttonTexts?: {
    showMore: string;
    showLess: string;
  };
  className?: string;
}

export function ExpandableContent({
  shortContent,
  fullContent,
  buttonTexts = { showMore: 'Show more', showLess: 'Show less' },
  className,
}: ExpandableContentProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className={className}>
      <div>{isExpanded ? fullContent : shortContent}</div>
      <Button
        variant="link"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="p-0 h-auto mt-1 text-primary hover:text-primary/80"
      >
        {isExpanded ? buttonTexts.showLess : buttonTexts.showMore}
      </Button>
    </div>
  );
}

interface ExpandableTextProps {
  text: string | any;  // Allow any type, will be converted to string internally
  maxLength?: number;
  buttonTexts?: {
    showMore: string;
    showLess: string;
  };
  className?: string;
  contentClassName?: string;
}

export function ExpandableText({
  text,
  maxLength = 200,
  buttonTexts = { showMore: 'Show more', showLess: 'Show less' },
  className,
  contentClassName = "font-mono text-sm whitespace-pre-wrap"
}: ExpandableTextProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  // Ensure text is a string
  const safeText = typeof text === 'string' ? text : String(text || '');

  if (safeText.length <= maxLength) {
    return <div className={`${className} ${contentClassName}`}>{safeText}</div>;
  }

  const shortText = safeText.substring(0, maxLength) + "...";

  return (
    <div className={className}>
      <div className={contentClassName}>
        {isExpanded ? safeText : shortText}
      </div>
      <Button
        variant="link"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="p-0 h-auto mt-1 text-primary hover:text-primary/80"
      >
        {isExpanded ? buttonTexts.showLess : buttonTexts.showMore}
      </Button>
    </div>
  );
}
