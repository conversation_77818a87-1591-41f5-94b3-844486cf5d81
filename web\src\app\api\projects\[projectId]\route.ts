import { NextRequest } from 'next/server'
import { ProjectsApiHandler } from '@/lib/api-supabase/projects'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  const handler = new ProjectsApiHandler()
  const { projectId } = await params
  return handler.handleRequest(() => handler.getProject(projectId))
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  const handler = new ProjectsApiHandler()
  const { projectId } = await params
  return handler.handleRequest(async () => {
    const body = await request.json()
    return handler.updateProject(projectId, body)
  })
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  const handler = new ProjectsApiHandler()
  const { projectId } = await params
  return handler.handleRequest(() => handler.deleteProject(projectId))
}