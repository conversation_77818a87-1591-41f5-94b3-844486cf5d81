import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  // Get from environment variables with fallbacks
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL ||
                     (typeof window !== 'undefined' && window.location.origin.includes('vercel.app')
                       ? 'https://qekegvxuykuuokipfple.supabase.co'
                       : process.env.NEXT_PUBLIC_SUPABASE_URL)

  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
                         (typeof window !== 'undefined' && window.location.origin.includes('vercel.app')
                           ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFla2Vndnh1eWt1dW9raXBmcGxlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTc5Nzc2NDksImV4cCI6MjA3MzU1MzY0OX0.7sFgaSFtsiwdU4uKWjkXKVNlykK62A7itHGnx81kM-I'
                           : process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)

  console.log('Supabase client initialization:', {
    hasUrl: !!supabaseUrl,
    hasKey: !!supabaseAnonKey,
    url: supabaseUrl?.substring(0, 30) + '...',
    isClient: typeof window !== 'undefined'
  })

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase configuration:', {
      supabaseUrl: !!supabaseUrl,
      supabaseAnonKey: !!supabaseAnonKey,
      env: process.env.NODE_ENV
    })
    throw new Error('@supabase/ssr: Your project\'s URL and API key are required to create a Supabase client! Check your Supabase project\'s API settings to find these values')
  }

  return createBrowserClient(
    supabaseUrl,
    supabaseAnonKey
  )
}