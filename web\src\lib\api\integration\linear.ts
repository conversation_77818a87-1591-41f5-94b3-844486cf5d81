import { fetchApi, fetchExternal<PERSON><PERSON> } from '../core/fetch';

/**
 * Linear workspace parameters interface
 */
export interface LinearDocumentParams {
  workspace_id?: string;
  project_id?: string;
  limit?: number;
  offset?: number;
}

/**
 * Calls Linear API through the QAK proxy server
 * 
 * @param endpoint - The Linear API endpoint to call
 * @param options - Additional request options
 * @returns Promise resolving to Linear API response
 * 
 * @example
 * ```typescript
 * // Get Linear issues
 * const issues = await callLinearApi('/issues', {
 *   method: 'GET',
 *   headers: { 'Authorization': 'Bearer token' }
 * });
 * ```
 */
export async function callLinearApi(endpoint: string, options?: RequestInit): Promise<any> {
  return fetchApi(`/v1/linear${endpoint}`, options);
}

/**
 * Gets all Linear workspaces for a tenant
 * 
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to workspaces list
 * 
 * @example
 * ```typescript
 * const workspaces = await getLinearWorkspaces("tenant-123");
 * workspaces.forEach(workspace => console.log(workspace.name));
 * ```
 */
export async function getLinearWorkspaces(tenantId: string): Promise<any> {
  return fetchApi('/v1/linear/workspaces', {
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
}

/**
 * Gets Linear authorization URL for OAuth flow
 * 
 * @param redirectUri - The redirect URI after authorization
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to authorization URL
 * 
 * @example
 * ```typescript
 * const authUrl = await getLinearAuthUrl("https://app.qak.com/linear/callback", "tenant-123");
 * window.location.href = authUrl.url;
 * ```
 */
export async function getLinearAuthUrl(redirectUri: string, tenantId: string): Promise<any> {
  return fetchApi(`/v1/linear/auth/url?redirectUri=${encodeURIComponent(redirectUri)}`, {
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
}

/**
 * Handles Linear OAuth callback with authorization code
 * 
 * @param code - The authorization code from Linear
 * @param state - The state parameter for security
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to authentication result
 * 
 * @example
 * ```typescript
 * const result = await handleLinearAuthCallback(code, state, "tenant-123");
 * if (result.success) {
 *   console.log("Linear workspace connected successfully");
 * }
 * ```
 */
export async function handleLinearAuthCallback(code: string, state: string, tenantId: string): Promise<any> {
  return fetchApi('/v1/linear/auth/callback', {
    method: 'POST',
    headers: {
      'X-Tenant-ID': tenantId
    },
    body: JSON.stringify({ code, state })
  });
}

/**
 * Deletes a Linear workspace integration
 * 
 * @param workspaceId - The workspace identifier to delete
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to deletion result
 * 
 * @example
 * ```typescript
 * await deleteLinearWorkspace("workspace-456", "tenant-123");
 * console.log("Linear workspace integration removed");
 * ```
 */
export async function deleteLinearWorkspace(workspaceId: string, tenantId: string): Promise<any> {
  return fetchApi(`/v1/linear/workspaces/${workspaceId}`, {
    method: 'DELETE',
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
}

/**
 * Synchronizes a Linear workspace to pull latest data
 * 
 * @param workspaceId - The workspace identifier to sync
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to sync result
 * 
 * @example
 * ```typescript
 * const syncResult = await syncLinearWorkspace("workspace-456", "tenant-123");
 * console.log(`Synced ${syncResult.issuesCount} issues`);
 * ```
 */
export async function syncLinearWorkspace(workspaceId: string, tenantId: string): Promise<any> {
  return fetchApi(`/v1/linear/workspaces/${workspaceId}/sync`, {
    method: 'POST',
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
}

/**
 * Updates Linear workspace synchronization configuration
 * 
 * @param workspaceId - The workspace identifier
 * @param config - The sync configuration settings
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to update result
 * 
 * @example
 * ```typescript
 * await updateLinearSyncConfig("workspace-456", {
 *   syncInterval: "1h",
 *   includeLabels: ["bug", "feature"],
 *   excludeStates: ["Canceled"]
 * }, "tenant-123");
 * ```
 */
export async function updateLinearSyncConfig(workspaceId: string, config: any, tenantId: string): Promise<any> {
  return fetchApi(`/v1/linear/workspaces/${workspaceId}/sync-config`, {
    method: 'PUT',
    headers: {
      'X-Tenant-ID': tenantId
    },
    body: JSON.stringify(config)
  });
}

/**
 * Gets Linear documents with optional filtering
 * 
 * @param params - Query parameters for filtering documents
 * @param tenantId - The tenant identifier
 * @returns Promise resolving to documents list
 * 
 * @example
 * ```typescript
 * const documents = await getLinearDocuments({
 *   workspace_id: "workspace-456",
 *   project_id: "project-789",
 *   limit: 50
 * }, "tenant-123");
 * ```
 */
export async function getLinearDocuments(params: LinearDocumentParams, tenantId: string): Promise<any> {
  const queryParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString());
    }
  });
  
  return fetchApi(`/v1/linear/documents?${queryParams}`, {
    headers: {
      'X-Tenant-ID': tenantId
    }
  });
}