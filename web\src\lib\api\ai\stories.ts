import { fetchApi } from '../core/fetch';

/**
 * Input interface for enhancing user stories
 */
export interface EnhanceUserStoryInput {
  userStory: string;
  language: string;
}

/**
 * Output interface for enhanced user stories
 */
export interface EnhanceUserStoryOutput {
  enhancedUserStory: string;
}

/**
 * Input interface for generating manual test cases
 */
export interface GenerateManualTestCasesInput {
  userStory: string;
  language: string;
}

/**
 * Output interface for manual test cases
 */
export interface GenerateManualTestCasesOutput {
  manualTestCases: (string | any)[];
}

/**
 * Enhances a user story using AI to add more detail and clarity
 * 
 * @param input - The user story input containing story text and language preference
 * @returns Promise resolving to enhanced user story
 * 
 * @example
 * ```typescript
 * const enhanced = await enhanceUserStory({
 *   userStory: "As a user I want to login",
 *   language: "es"
 * });
 * console.log(enhanced.enhancedUserStory);
 * ```
 */
export async function enhanceUserStory(input: EnhanceUserStoryInput): Promise<EnhanceUserStoryOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    user_story: input.userStory,
    language: input.language
  };

  try {
    // Using the documented endpoint
    const result = await fetchApi<any>('/stories/enhance', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });
    return {
      enhancedUserStory: result.enhanced_story || result.enhancedUserStory || '',
    };
  } catch (error) {
    throw new Error(`Failed to enhance user story: ${(error as Error).message}`);
  }
}

/**
 * Generates manual test cases from an enhanced user story
 * 
 * @param input - The enhanced user story and language preference
 * @returns Promise resolving to array of manual test cases
 * 
 * @example
 * ```typescript
 * const testCases = await generateManualTestCases({
 *   userStory: "As a user I want to login with email and password...",
 *   language: "es"
 * });
 * testCases.manualTestCases.forEach(testCase => console.log(testCase));
 * ```
 */
export async function generateManualTestCases(input: GenerateManualTestCasesInput): Promise<GenerateManualTestCasesOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    enhanced_story: input.userStory,
    language: input.language
  };

  try {
    // Using the documented endpoint
    const result = await fetchApi<any>('/stories/generate-manual-tests', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    // Convert API response to our expected output format
    let manualTestCases: (string | any)[] = [];

    // Handle different response formats from the API
    if (result && result.manual_tests) {
      if (Array.isArray(result.manual_tests)) {
        // Check if it's an array of objects (new format) or strings (old format)
        if (result.manual_tests.length > 0 && typeof result.manual_tests[0] === 'object') {
          // New format: array of test case objects - keep as objects
          manualTestCases = result.manual_tests;
        } else {
          // Old format: direct array of strings
          manualTestCases = result.manual_tests;
        }
      } else if (typeof result.manual_tests === 'string') {
        // String response - might be JSON or plain text
        try {
          // Try parsing as JSON
          const parsed = JSON.parse(result.manual_tests);
          if (Array.isArray(parsed)) {
            if (parsed.length > 0 && typeof parsed[0] === 'object') {
              // Array of objects - keep as objects
              manualTestCases = parsed;
            } else {
              // Array of strings
              manualTestCases = parsed;
            }
          } else {
            // Create a single item array if it's an object
            manualTestCases = [parsed];
          }
        } catch (e) {
          // If parsing fails, split lines
          manualTestCases = result.manual_tests.split('\n').filter((line: string) => line.trim().length > 0);
        }
      }
    } else if (result && Array.isArray(result.manualTestCases)) {
      // Alternative field name
      manualTestCases = result.manualTestCases;
    } else if (result && typeof result === 'object') {
      // Try to use the result directly
      manualTestCases = [result];
    }

    return { manualTestCases };
  } catch (error) {
    console.error("Manual test generation error:", error);
    throw new Error(`Failed to generate manual test cases: ${(error as Error).message}`);
  }
}