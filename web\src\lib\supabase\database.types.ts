export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          username: string
          first_name: string | null
          last_name: string | null
          full_name: string
          role: 'User' | 'Admin' | 'SuperAdmin'
          email_verified: boolean
          tenant_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          username: string
          first_name?: string | null
          last_name?: string | null
          full_name: string
          role?: 'User' | 'Admin' | 'SuperAdmin'
          email_verified?: boolean
          tenant_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          username?: string
          first_name?: string | null
          last_name?: string | null
          full_name?: string
          role?: 'User' | 'Admin' | 'SuperAdmin'
          email_verified?: boolean
          tenant_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      projects: {
        Row: {
          project_id: string
          name: string
          description: string
          tags: string[]
          created_at: string
          updated_at: string
          default_environment_id: string | null
          github_config: Json | null
          user_id: string
        }
        Insert: {
          project_id?: string
          name: string
          description: string
          tags?: string[]
          created_at?: string
          updated_at?: string
          default_environment_id?: string | null
          github_config?: Json | null
          user_id: string
        }
        Update: {
          project_id?: string
          name?: string
          description?: string
          tags?: string[]
          created_at?: string
          updated_at?: string
          default_environment_id?: string | null
          github_config?: Json | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "projects_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      environments: {
        Row: {
          env_id: string
          project_id: string
          name: string
          description: string
          base_url: string
          is_default: boolean
          config_overrides: Json
          headless: boolean | null
          wait_time: number | null
          timeout: number | null
          viewport_width: number | null
          viewport_height: number | null
          user_agent: string | null
          tags: string[]
          variables: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          env_id?: string
          project_id: string
          name: string
          description: string
          base_url: string
          is_default?: boolean
          config_overrides?: Json
          headless?: boolean | null
          wait_time?: number | null
          timeout?: number | null
          viewport_width?: number | null
          viewport_height?: number | null
          user_agent?: string | null
          tags?: string[]
          variables?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          env_id?: string
          project_id?: string
          name?: string
          description?: string
          base_url?: string
          is_default?: boolean
          config_overrides?: Json
          headless?: boolean | null
          wait_time?: number | null
          timeout?: number | null
          viewport_width?: number | null
          viewport_height?: number | null
          user_agent?: string | null
          tags?: string[]
          variables?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "environments_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["project_id"]
          }
        ]
      }
      test_suites: {
        Row: {
          suite_id: string
          project_id: string
          name: string
          description: string
          type: 'manual' | 'github' | 'automated'
          tags: string[]
          created_at: string
          updated_at: string
          execution_times: number
        }
        Insert: {
          suite_id?: string
          project_id: string
          name: string
          description: string
          type?: 'manual' | 'github' | 'automated'
          tags?: string[]
          created_at?: string
          updated_at?: string
          execution_times?: number
        }
        Update: {
          suite_id?: string
          project_id?: string
          name?: string
          description?: string
          type?: 'manual' | 'github' | 'automated'
          tags?: string[]
          created_at?: string
          updated_at?: string
          execution_times?: number
        }
        Relationships: [
          {
            foreignKeyName: "test_suites_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["project_id"]
          }
        ]
      }
      test_cases: {
        Row: {
          id: string
          suite_id: string
          project_id: string
          name: string
          description: string
          instructions: string
          user_story: string
          gherkin: string | null
          url: string
          tags: string[]
          status: 'Pending' | 'Running' | 'InProgress' | 'Passed' | 'Failed' | 'Blocked' | 'Skipped'
          priority: 'Low' | 'Medium' | 'High' | 'Critical'
          type: 'Functional' | 'Performance' | 'Security' | 'Integration' | 'UnitTest'
          created_at: string
          updated_at: string
          history_files: string[]
          last_execution: string | null
          last_result: string | null
          error_message: string | null
          sensitive_data: Json | null
          allowed_domains: string[] | null
          code: string | null
          framework: string | null
          enable_file_handling: boolean | null
        }
        Insert: {
          id?: string
          suite_id: string
          project_id: string
          name: string
          description: string
          instructions: string
          user_story: string
          gherkin?: string | null
          url: string
          tags?: string[]
          status?: 'Pending' | 'Running' | 'InProgress' | 'Passed' | 'Failed' | 'Blocked' | 'Skipped'
          priority?: 'Low' | 'Medium' | 'High' | 'Critical'
          type?: 'Functional' | 'Performance' | 'Security' | 'Integration' | 'UnitTest'
          created_at?: string
          updated_at?: string
          history_files?: string[]
          last_execution?: string | null
          last_result?: string | null
          error_message?: string | null
          sensitive_data?: Json | null
          allowed_domains?: string[] | null
          code?: string | null
          framework?: string | null
          enable_file_handling?: boolean | null
        }
        Update: {
          id?: string
          suite_id?: string
          project_id?: string
          name?: string
          description?: string
          instructions?: string
          user_story?: string
          gherkin?: string | null
          url?: string
          tags?: string[]
          status?: 'Pending' | 'Running' | 'InProgress' | 'Passed' | 'Failed' | 'Blocked' | 'Skipped'
          priority?: 'Low' | 'Medium' | 'High' | 'Critical'
          type?: 'Functional' | 'Performance' | 'Security' | 'Integration' | 'UnitTest'
          created_at?: string
          updated_at?: string
          history_files?: string[]
          last_execution?: string | null
          last_result?: string | null
          error_message?: string | null
          sensitive_data?: Json | null
          allowed_domains?: string[] | null
          code?: string | null
          framework?: string | null
          enable_file_handling?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "test_cases_suite_id_fkey"
            columns: ["suite_id"]
            isOneToOne: false
            referencedRelation: "test_suites"
            referencedColumns: ["suite_id"]
          },
          {
            foreignKeyName: "test_cases_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["project_id"]
          }
        ]
      }
      project_variables: {
        Row: {
          var_id: string
          project_id: string
          name: string
          value: string
          description: string | null
          is_secret: boolean
          tags: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          var_id?: string
          project_id: string
          name: string
          value: string
          description?: string | null
          is_secret?: boolean
          tags?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          var_id?: string
          project_id?: string
          name?: string
          value?: string
          description?: string | null
          is_secret?: boolean
          tags?: string[]
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_variables_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["project_id"]
          }
        ]
      }
      suite_variables: {
        Row: {
          var_id: string
          suite_id: string
          project_id: string
          name: string
          value: string
          description: string | null
          is_secret: boolean
          overrides_project: boolean | null
          tags: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          var_id?: string
          suite_id: string
          project_id: string
          name: string
          value: string
          description?: string | null
          is_secret?: boolean
          overrides_project?: boolean | null
          tags?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          var_id?: string
          suite_id?: string
          project_id?: string
          name?: string
          value?: string
          description?: string | null
          is_secret?: boolean
          overrides_project?: boolean | null
          tags?: string[]
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "suite_variables_suite_id_fkey"
            columns: ["suite_id"]
            isOneToOne: false
            referencedRelation: "test_suites"
            referencedColumns: ["suite_id"]
          },
          {
            foreignKeyName: "suite_variables_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["project_id"]
          }
        ]
      }
      test_file_attachments: {
        Row: {
          file_id: string
          test_case_id: string
          file_name: string
          file_size: number
          content_type: string
          upload_date: string
          file_path: string | null
        }
        Insert: {
          file_id?: string
          test_case_id: string
          file_name: string
          file_size: number
          content_type: string
          upload_date?: string
          file_path?: string | null
        }
        Update: {
          file_id?: string
          test_case_id?: string
          file_name?: string
          file_size?: number
          content_type?: string
          upload_date?: string
          file_path?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "test_file_attachments_test_case_id_fkey"
            columns: ["test_case_id"]
            isOneToOne: false
            referencedRelation: "test_cases"
            referencedColumns: ["id"]
          }
        ]
      }
      test_executions: {
        Row: {
          execution_id: string
          user_id: string
          project_id: string | null
          test_type: 'smoke' | 'full' | 'case' | 'suite'
          status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
          config: Json | null
          results: Json | null
          started_at: string
          completed_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          execution_id?: string
          user_id: string
          project_id?: string | null
          test_type: 'smoke' | 'full' | 'case' | 'suite'
          status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
          config?: Json | null
          results?: Json | null
          started_at?: string
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          execution_id?: string
          user_id?: string
          project_id?: string | null
          test_type?: 'smoke' | 'full' | 'case' | 'suite'
          status?: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
          config?: Json | null
          results?: Json | null
          started_at?: string
          completed_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "test_executions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "test_executions_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["project_id"]
          }
        ]
      }
      execution_steps: {
        Row: {
          step_id: string
          execution_id: string
          step_number: number
          action: string | null
          target: string | null
          value: string | null
          screenshot_url: string | null
          success: boolean | null
          error_message: string | null
          timestamp: string
        }
        Insert: {
          step_id?: string
          execution_id: string
          step_number: number
          action?: string | null
          target?: string | null
          value?: string | null
          screenshot_url?: string | null
          success?: boolean | null
          error_message?: string | null
          timestamp?: string
        }
        Update: {
          step_id?: string
          execution_id?: string
          step_number?: number
          action?: string | null
          target?: string | null
          value?: string | null
          screenshot_url?: string | null
          success?: boolean | null
          error_message?: string | null
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "execution_steps_execution_id_fkey"
            columns: ["execution_id"]
            isOneToOne: false
            referencedRelation: "test_executions"
            referencedColumns: ["execution_id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}