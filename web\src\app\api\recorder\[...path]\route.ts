import { NextRequest, NextResponse } from 'next/server';

// Base URL for the .NET API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';

async function proxyRequest(
  request: NextRequest,
  method: string,
  path: string,
  body?: any
) {
  try {
    const url = `${API_BASE_URL}/api/recorder${path}`;

    const headers = new Headers();
    // Copy authorization header from the incoming request
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers.set('authorization', authHeader);
    }
    headers.set('content-type', 'application/json');

    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    });

    const responseData = await response.json();

    return NextResponse.json(responseData, { status: response.status });
  } catch (error) {
    console.error('Proxy request failed:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST /api/recorder/sessions - Create browser session
export async function POST(request: NextRequest) {
  const url = new URL(request.url);
  const path = url.pathname.replace('/api/recorder', '');

  if (path === '/sessions') {
    return proxyRequest(request, 'POST', '/sessions');
  }

  if (path === '/recording/start') {
    return proxyRequest(request, 'POST', '/recording/start');
  }

  if (path === '/recording/stop') {
    return proxyRequest(request, 'POST', '/recording/stop');
  }

  if (path === '/sessions/stop') {
    return proxyRequest(request, 'POST', '/sessions/stop');
  }

  if (path === '/chat/send') {
    const body = await request.json();
    return proxyRequest(request, 'POST', '/chat/send', body);
  }

  return NextResponse.json({ error: 'Endpoint not found' }, { status: 404 });
}

// GET /api/recorder/* - Handle GET requests
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const path = url.pathname.replace('/api/recorder', '');

  if (path === '/steps') {
    return proxyRequest(request, 'GET', '/steps');
  }

  if (path === '/chat/messages') {
    return proxyRequest(request, 'GET', '/chat/messages');
  }

  if (path === '/health') {
    return proxyRequest(request, 'GET', '/health');
  }

  return NextResponse.json({ error: 'Endpoint not found' }, { status: 404 });
}