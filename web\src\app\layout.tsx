import type { Metadata } from 'next';
import './globals.css';
import { Providers } from '@/lib/query-provider';
import { AuthProvider } from '@/contexts/AuthContextSupabase';
import { ClientLayout } from '@/components/layout/ClientLayout';
import { Toaster } from '@/components/ui/toaster';

export const metadata: Metadata = {
  title: 'QAK - QA Automation',
  description: 'Manage your QA projects, suites, and test cases with AI-powered tools.',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '16x16', type: 'image/x-icon' },
      { url: '/favicon.ico', sizes: '32x32', type: 'image/x-icon' },
    ],
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
  openGraph: {
    title: 'QAK - QA Automation',
    description: 'Manage your QA projects, suites, and test cases with AI-powered tools.',
    url: 'https://qak.app',
    siteName: 'QAK',
    locale: 'es_ES',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'QAK - QA Automation',
    description: 'Manage your QA projects, suites, and test cases with AI-powered tools.',
    site: '@heyQAK',
    creator: '@heyQAK',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased dark">
        <AuthProvider>
          <Providers>
            <ClientLayout>
              {children}
            </ClientLayout>
            <Toaster />
          </Providers>
        </AuthProvider>
      </body>
    </html>
  );
}
