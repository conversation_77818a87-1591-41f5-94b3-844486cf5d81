/**
 * Script and Code Generation Functions
 * 
 * Functions for automated generation of test scripts and code templates.
 * Supports multiple frameworks and programming languages for test automation.
 * 
 * @module ScriptGenerationAPI
 */

import { fetchApi } from '../core/fetch';

// ============================================================================
// SCRIPT GENERATION TYPES
// ============================================================================

export type ScriptFormat = 'cypress' | 'playwright-js' | 'playwright-python' | 'selenium-python' | 'robot-framework' | 'java-selenium';

export type CodeGenerationParams = {
  framework: ScriptFormat;
  tests: Array<{
    title: string;
    steps: string[];
    expectedResults?: string[];
  }>;
  projectName?: string;
  baseUrl?: string;
  includeSetup?: boolean;
  includeUtilities?: boolean;
  language?: 'typescript' | 'javascript' | 'python' | 'java';
  executionType?: 'V1' | 'V2';
};

export type GeneratedScript = {
  framework: ScriptFormat;
  language: string;
  code: string;
  dependencies?: string[];
  setupInstructions?: string;
  executionCommands?: string[];
  metadata?: {
    generatedAt: string;
    testCount: number;
    estimatedExecutionTime?: number;
  };
};

export type FrameworkTemplate = {
  name: string;
  framework: ScriptFormat;
  language: string;
  description: string;
  features: string[];
  sampleCode: string;
};

// ============================================================================
// SCRIPT GENERATION FUNCTIONS
// ============================================================================

/**
 * Generate test scripts for a specific framework.
 * Creates complete, executable test automation code from test specifications.
 * @param params - Code generation parameters
 * @returns Promise<GeneratedScript> Generated test script
 * @example
 * ```typescript
 * const script = await generateTestScript({
 *   framework: 'cypress',
 *   tests: [{
 *     title: 'Login Test',
 *     steps: ['Navigate to login page', 'Enter credentials', 'Click login']
 *   }],
 *   baseUrl: 'https://app.example.com',
 *   includeSetup: true
 * });
 * console.log('Generated Cypress code:', script.code);
 * ```
 */
export const generateTestScript = (params: CodeGenerationParams): Promise<GeneratedScript> => {
  return fetchApi<GeneratedScript>('/ai/generate-script', {
    method: 'POST',
    body: JSON.stringify(params),
  });
};

/**
 * Generate Cypress test scripts specifically.
 * Optimized for Cypress framework with E2E testing patterns.
 * @param tests - Test specifications
 * @param options - Cypress-specific options
 * @returns Promise<GeneratedScript> Generated Cypress script
 */
export const generateCypressScript = (
  tests: Array<{ title: string; steps: string[] }>,
  options?: {
    baseUrl?: string;
    includeSetup?: boolean;
    useDataTestIds?: boolean;
    includeApiTests?: boolean;
  }
): Promise<GeneratedScript> => {
  return generateTestScript({
    framework: 'cypress',
    tests,
    baseUrl: options?.baseUrl,
    includeSetup: options?.includeSetup,
    language: 'javascript',
  });
};

/**
 * Generate Playwright test scripts.
 * Supports both JavaScript and Python variants.
 * @param tests - Test specifications
 * @param options - Playwright-specific options
 * @returns Promise<GeneratedScript> Generated Playwright script
 */
export const generatePlaywrightScript = (
  tests: Array<{ title: string; steps: string[] }>,
  options?: {
    language?: 'javascript' | 'python';
    baseUrl?: string;
    includeSetup?: boolean;
    parallelExecution?: boolean;
    browserType?: 'chromium' | 'firefox' | 'webkit';
  }
): Promise<GeneratedScript> => {
  const framework = options?.language === 'python' ? 'playwright-python' : 'playwright-js';
  return generateTestScript({
    framework,
    tests,
    baseUrl: options?.baseUrl,
    includeSetup: options?.includeSetup,
    language: options?.language || 'javascript',
  });
};

/**
 * Generate Selenium test scripts.
 * Supports Python and Java implementations.
 * @param tests - Test specifications
 * @param options - Selenium-specific options
 * @returns Promise<GeneratedScript> Generated Selenium script
 */
export const generateSeleniumScript = (
  tests: Array<{ title: string; steps: string[] }>,
  options?: {
    language?: 'python' | 'java';
    baseUrl?: string;
    includeSetup?: boolean;
    driverType?: 'chrome' | 'firefox' | 'edge';
    usePageObjectModel?: boolean;
  }
): Promise<GeneratedScript> => {
  const framework = options?.language === 'java' ? 'java-selenium' : 'selenium-python';
  return generateTestScript({
    framework,
    tests,
    baseUrl: options?.baseUrl,
    includeSetup: options?.includeSetup,
    language: options?.language || 'python',
  });
};

/**
 * Generate Robot Framework test scripts.
 * Creates keyword-driven test automation scripts.
 * @param tests - Test specifications
 * @param options - Robot Framework options
 * @returns Promise<GeneratedScript> Generated Robot Framework script
 */
export const generateRobotFrameworkScript = (
  tests: Array<{ title: string; steps: string[] }>,
  options?: {
    baseUrl?: string;
    includeSetup?: boolean;
    useSeleniumLibrary?: boolean;
    includeKeywords?: boolean;
  }
): Promise<GeneratedScript> => {
  return generateTestScript({
    framework: 'robot-framework',
    tests,
    baseUrl: options?.baseUrl,
    includeSetup: options?.includeSetup,
    language: 'python', // Robot Framework typically uses Python
  });
};

// ============================================================================
// FRAMEWORK TEMPLATES
// ============================================================================

/**
 * Get available framework templates.
 * Returns pre-configured templates for quick project setup.
 * @returns Promise<FrameworkTemplate[]> Available templates
 */
export const getFrameworkTemplates = (): Promise<FrameworkTemplate[]> => {
  return fetchApi<FrameworkTemplate[]>('/ai/framework-templates');
};

/**
 * Get template for a specific framework.
 * @param framework - Framework identifier
 * @returns Promise<FrameworkTemplate> Framework template
 */
export const getFrameworkTemplate = (framework: ScriptFormat): Promise<FrameworkTemplate> => {
  return fetchApi<FrameworkTemplate>(`/ai/framework-templates/${framework}`);
};

// ============================================================================
// CODE UTILITIES
// ============================================================================

/**
 * Validate generated test script syntax.
 * Checks for common errors and provides improvement suggestions.
 * @param script - Generated script to validate
 * @returns Promise<any> Validation results
 */
export const validateGeneratedScript = (script: GeneratedScript): Promise<any> => {
  return fetchApi<any>('/ai/validate-script', {
    method: 'POST',
    body: JSON.stringify(script),
  });
};

/**
 * Optimize test script for better performance and maintainability.
 * @param script - Script to optimize
 * @param options - Optimization preferences
 * @returns Promise<GeneratedScript> Optimized script
 */
export const optimizeTestScript = (
  script: GeneratedScript,
  options?: {
    optimizeSelectors?: boolean;
    addWaitStrategies?: boolean;
    improveErrorHandling?: boolean;
    addPageObjectModel?: boolean;
  }
): Promise<GeneratedScript> => {
  return fetchApi<GeneratedScript>('/ai/optimize-script', {
    method: 'POST',
    body: JSON.stringify({ script, options }),
  });
};

/**
 * Convert test script between different frameworks.
 * @param script - Source script
 * @param targetFramework - Target framework to convert to
 * @returns Promise<GeneratedScript> Converted script
 */
export const convertTestScript = (
  script: GeneratedScript,
  targetFramework: ScriptFormat
): Promise<GeneratedScript> => {
  return fetchApi<GeneratedScript>('/ai/convert-script', {
    method: 'POST',
    body: JSON.stringify({ script, targetFramework }),
  });
};