import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/supabase/database.types'

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignUpCredentials {
  email: string;
  password: string;
  username: string;
  fullName: string;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user: User;
    session: any;
  };
  message?: string;
  errorMessage?: string;
  errors?: string[];
}

export interface User {
  id: string;
  email: string;
  username: string;
  name: string;
  role: string;
  tenantId?: string;
  emailVerified: boolean;
}

type Profile = Database['public']['Tables']['profiles']['Row']

export class SupabaseAuthService {
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      })

      if (error) {
        return {
          success: false,
          errorMessage: error.message,
        }
      }

      if (!data.user) {
        return {
          success: false,
          errorMessage: 'No user data returned',
        }
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.user.id)
        .single()

      if (profileError || !profile) {
        return {
          success: false,
          errorMessage: 'Failed to fetch user profile',
        }
      }

      const user: User = {
        id: profile.id,
        email: profile.email,
        username: profile.username,
        name: profile.full_name,
        role: profile.role,
        tenantId: profile.tenant_id || undefined,
        emailVerified: profile.email_verified,
      }

      return {
        success: true,
        data: {
          user,
          session: data.session,
        },
      }
    } catch (error: any) {
      return {
        success: false,
        errorMessage: error.message || 'Login failed',
      }
    }
  }

  static async signInWithGoogle(): Promise<AuthResponse> {
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        return {
          success: false,
          errorMessage: error.message,
        }
      }

      return {
        success: true,
        message: 'Redirecting to Google...',
      }
    } catch (error: any) {
      return {
        success: false,
        errorMessage: error.message || 'Google sign in failed',
      }
    }
  }

  static async signUp(credentials: SignUpCredentials): Promise<AuthResponse> {
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: {
          data: {
            username: credentials.username,
            full_name: credentials.fullName,
          },
        },
      })

      if (error) {
        return {
          success: false,
          errorMessage: error.message,
        }
      }

      if (!data.user) {
        return {
          success: false,
          errorMessage: 'No user data returned',
        }
      }

      return {
        success: true,
        message: 'Please check your email to confirm your account',
      }
    } catch (error: any) {
      return {
        success: false,
        errorMessage: error.message || 'Sign up failed',
      }
    }
  }

  static async logout(): Promise<void> {
    const supabase = createClient()
    await supabase.auth.signOut()
  }

  static async getCurrentUser(): Promise<User | null> {
    try {
      const supabase = createClient()
      
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        return null
      }

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError || !profile) {
        return null
      }

      return {
        id: profile.id,
        email: profile.email,
        username: profile.username,
        name: profile.full_name,
        role: profile.role,
        tenantId: profile.tenant_id || undefined,
        emailVerified: profile.email_verified,
      }
    } catch {
      return null
    }
  }

  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser()
    return user !== null
  }

  static async getSession() {
    const supabase = createClient()
    const { data: { session } } = await supabase.auth.getSession()
    return session
  }

  static async refreshSession() {
    const supabase = createClient()
    const { data, error } = await supabase.auth.refreshSession()
    return { data, error }
  }

  // Helper method to get auth headers for API calls
  static async getAuthHeaders(): Promise<Record<string, string>> {
    const session = await this.getSession()
    if (!session?.access_token) return {}
    
    return {
      'Authorization': `Bearer ${session.access_token}`,
    }
  }

  // Update user profile
  static async updateProfile(updates: Partial<Profile>): Promise<{ success: boolean; error?: string }> {
    try {
      const supabase = createClient()
      const user = await this.getCurrentUser()
      
      if (!user) {
        return { success: false, error: 'User not authenticated' }
      }

      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }
}