'use client'

import * as React from 'react'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { 
  Key, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Copy,
  Shield,
  Clock,
  Zap
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { getLLMCredentials, createLLMCredential, updateLLMCredential, deleteLLMCredential, testLLMCredential } from '@/lib/api'

interface LLMCredential {
  id: string
  name: string
  provider: string
  apiKey: string
  isActive: boolean
  createdAt: string
  lastUsed?: string
  usageCount: number
  isValid: boolean
  expiresAt?: string
  description?: string
}

interface CreateCredentialRequest {
  name: string
  provider: string
  apiKey: string
  description?: string
}

const mockCredentials: LLMCredential[] = [
  {
    id: '1',
    name: 'OpenAI Production',
    provider: 'OpenAI',
    apiKey: 'sk-proj-***************************',
    isActive: true,
    createdAt: '2024-01-10T10:00:00Z',
    lastUsed: '2024-01-15T09:30:00Z',
    usageCount: 15420,
    isValid: true,
    expiresAt: '2024-12-31T23:59:59Z',
    description: 'Credencial principal para GPT-4 en producción'
  },
  {
    id: '2',
    name: 'Anthropic Claude',
    provider: 'Anthropic',
    apiKey: 'sk-ant-***************************',
    isActive: true,
    createdAt: '2024-01-08T14:20:00Z',
    lastUsed: '2024-01-14T16:45:00Z',
    usageCount: 8750,
    isValid: true,
    description: 'Credencial para Claude 3.5 Sonnet'
  },
  {
    id: '3',
    name: 'OpenAI Development',
    provider: 'OpenAI',
    apiKey: 'sk-proj-***************************',
    isActive: false,
    createdAt: '2024-01-05T11:15:00Z',
    lastUsed: '2024-01-12T08:20:00Z',
    usageCount: 2340,
    isValid: false,
    expiresAt: '2024-06-30T23:59:59Z',
    description: 'Credencial de desarrollo - Expirada'
  },
  {
    id: '4',
    name: 'Google Gemini',
    provider: 'Google',
    apiKey: 'AIza***************************',
    isActive: true,
    createdAt: '2024-01-12T16:30:00Z',
    lastUsed: '2024-01-15T07:15:00Z',
    usageCount: 1250,
    isValid: true,
    description: 'Credencial para Gemini Pro'
  }
]

const providers = [
  'OpenAI',
  'Anthropic',
  'Google',
  'Azure OpenAI',
  'Cohere',
  'Hugging Face',
  'Replicate'
]

export default function LLMCredentialsPage() {
  const { toast } = useToast()
  const [credentials, setCredentials] = useState<LLMCredential[]>(mockCredentials)
  const [isLoading, setIsLoading] = useState(false)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [selectedCredential, setSelectedCredential] = useState<LLMCredential | null>(null)
  const [editFormData, setEditFormData] = useState({
    name: '',
    provider: '',
    description: '',
    isActive: true
  })
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set())
  const [searchTerm, setSearchTerm] = useState('')
  const [filterProvider, setFilterProvider] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  const [newCredential, setNewCredential] = useState<CreateCredentialRequest>({
    name: '',
    provider: '',
    apiKey: '',
    description: ''
  })

  useEffect(() => {
    fetchCredentials()
  }, [])

  const fetchCredentials = async () => {
    try {
      const response = await getLLMCredentials()
      // Handle different response formats
      let credentialsData: LLMCredential[] = []
      if (response && typeof response === 'object' && 'data' in response && Array.isArray(response.data)) {
        credentialsData = response.data
      } else if (Array.isArray(response)) {
        credentialsData = response
      }
      setCredentials(credentialsData)
    } catch (error) {
      console.error('Error fetching credentials:', error)
      // Keep existing mock data on error
      toast({
        title: "Error",
        description: "Error al cargar credenciales, usando datos locales.",
        variant: "destructive",
      })
    }
  }

  const filteredCredentials = credentials.filter(credential => {
    const matchesSearch = credential.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         credential.provider.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesProvider = filterProvider === 'all' || credential.provider === filterProvider
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'active' && credential.isActive) ||
                         (filterStatus === 'inactive' && !credential.isActive) ||
                         (filterStatus === 'valid' && credential.isValid) ||
                         (filterStatus === 'invalid' && !credential.isValid)
    
    return matchesSearch && matchesProvider && matchesStatus
  })

  const handleCreateCredential = async () => {
    if (!newCredential.name || !newCredential.provider || !newCredential.apiKey) {
      toast({
        title: "Error",
        description: "Por favor completa todos los campos requeridos.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)
    try {
      await createLLMCredential(newCredential)

      setNewCredential({ name: '', provider: '', apiKey: '', description: '' })
      setShowCreateDialog(false)

      toast({
        title: "Credencial creada",
        description: "La credencial LLM ha sido creada exitosamente.",
      })

      // Refresh credentials list
      fetchCredentials()
    } catch (error) {
      console.error('Error creating credential:', error)
      toast({
        title: "Error",
        description: "No se pudo crear la credencial. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditCredential = async () => {
    if (!selectedCredential) return

    setIsLoading(true)
    try {
      await updateLLMCredential(selectedCredential.id, {
        name: editFormData.name,
        provider: editFormData.provider,
        description: editFormData.description,
        isActive: editFormData.isActive
      })

      setShowEditDialog(false)
      setSelectedCredential(null)

      toast({
        title: "Credencial actualizada",
        description: "La credencial ha sido actualizada exitosamente.",
      })

      // Refresh credentials list
      fetchCredentials()
    } catch (error) {
      console.error('Error updating credential:', error)
      toast({
        title: "Error",
        description: "No se pudo actualizar la credencial. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const openEditDialog = (credential: LLMCredential) => {
    setSelectedCredential(credential)
    setEditFormData({
      name: credential.name,
      provider: credential.provider,
      description: credential.description || '',
      isActive: credential.isActive
    })
    setShowEditDialog(true)
  }

  const handleDeleteCredential = async (credentialId: string) => {
    setIsLoading(true)
    try {
      await deleteLLMCredential(credentialId)

      setCredentials(credentials.filter(cred => cred.id !== credentialId))

      toast({
        title: "Credencial eliminada",
        description: "La credencial ha sido eliminada exitosamente.",
      })
    } catch (error) {
      console.error('Error deleting credential:', error)
      toast({
        title: "Error",
        description: "No se pudo eliminar la credencial. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleKeyVisibility = (credentialId: string) => {
    const newVisibleKeys = new Set(visibleKeys)
    if (newVisibleKeys.has(credentialId)) {
      newVisibleKeys.delete(credentialId)
    } else {
      newVisibleKeys.add(credentialId)
    }
    setVisibleKeys(newVisibleKeys)
  }

  const handleCopyKey = async (apiKey: string) => {
    try {
      await navigator.clipboard.writeText(apiKey)
      toast({
        title: "Copiado",
        description: "La clave API ha sido copiada al portapapeles.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo copiar la clave API.",
        variant: "destructive",
      })
    }
  }

  const handleTestCredential = async (credentialId: string) => {
    setIsLoading(true)
    try {
      const result = await testLLMCredential(credentialId)

      // Update credential validity based on test result
      const isValid = result && result.isValid !== false
      setCredentials(credentials.map(cred =>
        cred.id === credentialId ? { ...cred, isValid } : cred
      ))

      toast({
        title: isValid ? "Credencial válida" : "Credencial inválida",
        description: isValid
          ? "La credencial funciona correctamente."
          : "La credencial no es válida o ha expirado.",
        variant: isValid ? "default" : "destructive",
      })
    } catch (error) {
      console.error('Error testing credential:', error)
      toast({
        title: "Error",
        description: "No se pudo probar la credencial. Inténtalo de nuevo.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const maskApiKey = (apiKey: string): string => {
    if (apiKey.length <= 8) return '*'.repeat(apiKey.length)
    return apiKey.substring(0, 8) + '*'.repeat(Math.max(0, apiKey.length - 8))
  }

  const getStatusBadge = (credential: LLMCredential) => {
    if (!credential.isActive) {
      return <Badge variant="secondary">Inactiva</Badge>
    }
    if (!credential.isValid) {
      return <Badge variant="destructive">Inválida</Badge>
    }
    return <Badge variant="default" className="bg-accent text-accent-foreground">Activa</Badge>
  }

  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'openai':
        return '🤖'
      case 'anthropic':
        return '🧠'
      case 'google':
        return '🔍'
      case 'azure openai':
        return '☁️'
      default:
        return '🔧'
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Credenciales LLM</h1>
          <p className="text-muted-foreground mt-2">
            Gestiona las credenciales de API para proveedores de LLM
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Nueva Credencial
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Crear Nueva Credencial</DialogTitle>
              <DialogDescription>
                Agrega una nueva credencial de API para un proveedor LLM
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Nombre *</Label>
                <Input
                  id="name"
                  value={newCredential.name}
                  onChange={(e) => setNewCredential({ ...newCredential, name: e.target.value })}
                  placeholder="Ej: OpenAI Production"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="provider">Proveedor *</Label>
                <Select
                  value={newCredential.provider}
                  onValueChange={(value) => setNewCredential({ ...newCredential, provider: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona un proveedor" />
                  </SelectTrigger>
                  <SelectContent>
                    {providers.map((provider) => (
                      <SelectItem key={provider} value={provider}>
                        {getProviderIcon(provider)} {provider}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="apiKey">Clave API *</Label>
                <Input
                  id="apiKey"
                  type="password"
                  value={newCredential.apiKey}
                  onChange={(e) => setNewCredential({ ...newCredential, apiKey: e.target.value })}
                  placeholder="Ingresa la clave API"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Descripción</Label>
                <Input
                  id="description"
                  value={newCredential.description}
                  onChange={(e) => setNewCredential({ ...newCredential, description: e.target.value })}
                  placeholder="Descripción opcional"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancelar
              </Button>
              <Button onClick={handleCreateCredential} disabled={isLoading}>
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4 mr-2" />
                )}
                Crear Credencial
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Buscar</Label>
              <Input
                id="search"
                placeholder="Buscar por nombre o proveedor..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="provider-filter">Proveedor</Label>
              <Select value={filterProvider} onValueChange={setFilterProvider}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los proveedores</SelectItem>
                  {providers.map((provider) => (
                    <SelectItem key={provider} value={provider}>
                      {getProviderIcon(provider)} {provider}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="status-filter">Estado</Label>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="active">Activas</SelectItem>
                  <SelectItem value="inactive">Inactivas</SelectItem>
                  <SelectItem value="valid">Válidas</SelectItem>
                  <SelectItem value="invalid">Inválidas</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button variant="outline" className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualizar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Credentials Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Credenciales ({filteredCredentials.length})
          </CardTitle>
          <CardDescription>
            Lista de todas las credenciales de API configuradas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nombre</TableHead>
                  <TableHead>Proveedor</TableHead>
                  <TableHead>Clave API</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead className="text-right">Uso</TableHead>
                  <TableHead>Último Uso</TableHead>
                  <TableHead>Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCredentials.map((credential) => (
                  <TableRow key={credential.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{credential.name}</div>
                        {credential.description && (
                          <div className="text-sm text-muted-foreground">
                            {credential.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span>{getProviderIcon(credential.provider)}</span>
                        <span>{credential.provider}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <code className="text-sm font-mono">
                          {visibleKeys.has(credential.id) 
                            ? credential.apiKey 
                            : maskApiKey(credential.apiKey)
                          }
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleToggleKeyVisibility(credential.id)}
                        >
                          {visibleKeys.has(credential.id) ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleCopyKey(credential.apiKey)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(credential)}
                    </TableCell>
                    <TableCell className="text-right">
                      {credential.usageCount.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      {credential.lastUsed ? (
                        <div className="text-sm">
                          {new Date(credential.lastUsed).toLocaleDateString('es-ES')}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Nunca</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTestCredential(credential.id)}
                          disabled={isLoading}
                        >
                          <Zap className="h-4 w-4" />
                        </Button>
                         <Button
                           size="sm"
                           variant="outline"
                           onClick={() => openEditDialog(credential)}
                         >
                           <Edit className="h-4 w-4" />
                         </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>¿Eliminar credencial?</AlertDialogTitle>
                              <AlertDialogDescription>
                                Esta acción no se puede deshacer. La credencial "{credential.name}" será eliminada permanentemente.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancelar</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteCredential(credential.id)}
                                className="bg-destructive hover:bg-destructive/90"
                              >
                                Eliminar
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Credencial</DialogTitle>
            <DialogDescription>
              Modifica los detalles de la credencial
            </DialogDescription>
          </DialogHeader>
          {selectedCredential && (
             <div className="grid gap-4 py-4">
               <div className="grid gap-2">
                 <Label htmlFor="edit-name">Nombre</Label>
                 <Input
                   id="edit-name"
                   value={editFormData.name}
                   onChange={(e) => setEditFormData({
                     ...editFormData,
                     name: e.target.value
                   })}
                 />
               </div>
               <div className="grid gap-2">
                 <Label htmlFor="edit-provider">Proveedor</Label>
                 <Select
                   value={editFormData.provider}
                   onValueChange={(value) => setEditFormData({
                     ...editFormData,
                     provider: value
                   })}
                 >
                   <SelectTrigger>
                     <SelectValue />
                   </SelectTrigger>
                   <SelectContent>
                     {providers.map((provider) => (
                       <SelectItem key={provider} value={provider}>
                         {getProviderIcon(provider)} {provider}
                       </SelectItem>
                     ))}
                   </SelectContent>
                 </Select>
               </div>
               <div className="grid gap-2">
                 <Label htmlFor="edit-description">Descripción</Label>
                 <Input
                   id="edit-description"
                   value={editFormData.description}
                   onChange={(e) => setEditFormData({
                     ...editFormData,
                     description: e.target.value
                   })}
                 />
               </div>
               <div className="flex items-center space-x-2">
                 <input
                   type="checkbox"
                   id="edit-active"
                   checked={editFormData.isActive}
                   onChange={(e) => setEditFormData({
                     ...editFormData,
                     isActive: e.target.checked
                   })}
                   className="rounded"
                 />
                 <Label htmlFor="edit-active">Credencial activa</Label>
               </div>
             </div>
           )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEditCredential} disabled={isLoading}>
              {isLoading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Edit className="h-4 w-4 mr-2" />
              )}
              Guardar Cambios
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}