'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { SupabaseAuthService, type User } from '@/lib/auth-supabase'
import type { Session } from '@supabase/supabase-js'

interface AuthContextType {
  user: User | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>
  signUp: (email: string, password: string, username: string, fullName: string) => Promise<{ success: boolean; error?: string }>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  
  // Only create client on the client side
  const [supabase, setSupabase] = useState<ReturnType<typeof createClient> | null>(null)

  useEffect(() => {
    // Initialize Supabase client only on client side
    if (typeof window === 'undefined') return

    try {
      console.log('🔧 Initializing Supabase client...')
      const client = createClient()
      console.log('✅ Supabase client created successfully')
      setSupabase(client)
    } catch (error) {
      console.error('❌ Failed to initialize Supabase client:', error)
      console.error('Environment check:', {
        NODE_ENV: process.env.NODE_ENV,
        hasNextPublicUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasNextPublicKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        windowOrigin: typeof window !== 'undefined' ? window.location.origin : 'server'
      })
      setLoading(false)
      return
    }
  }, [])

  useEffect(() => {
    if (!supabase) return

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setSession(session)

        if (session?.user) {
          console.log('🔍 Session found, getting user profile...')
          const userResult = await SupabaseAuthService.getCurrentUser()
          if (userResult) {
            setUser(userResult)
            console.log('✅ User profile loaded successfully:', userResult.email)
          } else {
            console.warn('⚠️ No user profile found, but session exists - creating basic user')
            // Create basic user object from session
            const basicUser = {
              id: session.user.id,
              email: session.user.email || '',
              username: session.user.email?.split('@')[0] || '',
              name: session.user.user_metadata?.full_name || session.user.email || '',
              role: 'User',
              emailVerified: !!session.user.email_confirmed_at
            }
            setUser(basicUser)
            console.log('👤 Created basic user:', basicUser.email)
          }
        } else {
          console.log('❌ No session found')
          setUser(null)
        }
      } catch (error) {
        console.error('Error getting initial session:', error)
        setUser(null)
      } finally {
        console.log('Setting loading to false')
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event, !!session)
        setSession(session)

        if (session?.user) {
          console.log('Getting user profile after auth change...')
          const currentUser = await SupabaseAuthService.getCurrentUser()
          if (currentUser) {
            setUser(currentUser)
            console.log('User profile loaded after auth change')
          } else {
            console.warn('No user profile found after auth change - keeping session active')
            // Keep the session but create a basic user object
            setUser({
              id: session.user.id,
              email: session.user.email || '',
              username: session.user.email?.split('@')[0] || '',
              name: session.user.user_metadata?.full_name || session.user.email || '',
              role: 'User',
              emailVerified: !!session.user.email_confirmed_at
            })
          }
        } else {
          console.log('No session after auth change')
          setUser(null)
        }

        console.log('Setting loading to false after auth change')
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase])

  const signIn = async (email: string, password: string) => {
    if (!supabase) return { success: false, error: 'Supabase client not initialized' }
    
    try {
      const response = await SupabaseAuthService.login({ email, password })
      
      if (response.success && response.data) {
        setUser(response.data.user)
        setSession(response.data.session)
        return { success: true }
      } else {
        return { success: false, error: response.errorMessage || 'Login failed' }
      }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const signInWithGoogle = async () => {
    if (!supabase) return { success: false, error: 'Supabase client not initialized' }
    
    try {
      const response = await SupabaseAuthService.signInWithGoogle()
      
      if (response.success) {
        return { success: true }
      } else {
        return { success: false, error: response.errorMessage || 'Google sign in failed' }
      }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const signUp = async (email: string, password: string, username: string, fullName: string) => {
    if (!supabase) return { success: false, error: 'Supabase client not initialized' }
    
    try {
      const response = await SupabaseAuthService.signUp({ email, password, username, fullName })
      
      if (response.success) {
        return { success: true }
      } else {
        return { success: false, error: response.errorMessage || 'Sign up failed' }
      }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const signOut = async () => {
    await SupabaseAuthService.logout()
    setUser(null)
    setSession(null)
  }

  const refreshUser = async () => {
    const currentUser = await SupabaseAuthService.getCurrentUser()
    setUser(currentUser)
  }

  const value = {
    user,
    session,
    loading,
    signIn,
    signInWithGoogle,
    signUp,
    signOut,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}