-- DEPRECATED: This migration has been superseded by the new organized migration structure
-- Please use the following migrations instead:
-- - 20250117000001_auth_profiles.sql
-- - 20250117000002_core_tables.sql  
-- - 20250117000003_rls_policies.sql
-- - 20250117000004_test_execution.sql
--
-- This file is kept for reference but should not be executed in new environments

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  username TEXT NOT NULL UNIQUE,
  first_name TEXT,
  last_name TEXT,
  full_name TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'User' CHECK (role IN ('User', 'Admin', 'SuperAdmin')),
  email_verified BOOLEAN NOT NULL DEFAULT FALSE,
  tenant_id TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create projects table
CREATE TABLE projects (
  project_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  default_environment_id UUID,
  github_config JSONB,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create environments table
CREATE TABLE environments (
  env_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  base_url TEXT NOT NULL,
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  config_overrides JSONB DEFAULT '{}',
  headless BOOLEAN,
  wait_time INTEGER,
  timeout INTEGER,
  viewport_width INTEGER,
  viewport_height INTEGER,
  user_agent TEXT,
  tags TEXT[] DEFAULT '{}',
  variables JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create test_suites table
CREATE TABLE test_suites (
  suite_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  type TEXT NOT NULL DEFAULT 'manual' CHECK (type IN ('manual', 'github', 'automated')),
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  execution_times INTEGER NOT NULL DEFAULT 0
);

-- Create test_cases table
CREATE TABLE test_cases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  suite_id UUID NOT NULL REFERENCES test_suites(suite_id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  instructions TEXT NOT NULL,
  user_story TEXT NOT NULL,
  gherkin TEXT,
  url TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'Running', 'InProgress', 'Passed', 'Failed', 'Blocked', 'Skipped')),
  priority TEXT NOT NULL DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High', 'Critical')),
  type TEXT NOT NULL DEFAULT 'Functional' CHECK (type IN ('Functional', 'Performance', 'Security', 'Integration', 'UnitTest')),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  history_files TEXT[] DEFAULT '{}',
  last_execution TIMESTAMPTZ,
  last_result TEXT,
  error_message TEXT,
  sensitive_data JSONB,
  allowed_domains TEXT[],
  code TEXT,
  framework TEXT,
  enable_file_handling BOOLEAN
);

-- Create project_variables table
CREATE TABLE project_variables (
  var_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  is_secret BOOLEAN NOT NULL DEFAULT FALSE,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(project_id, name)
);

-- Create suite_variables table
CREATE TABLE suite_variables (
  var_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  suite_id UUID NOT NULL REFERENCES test_suites(suite_id) ON DELETE CASCADE,
  project_id UUID NOT NULL REFERENCES projects(project_id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  value TEXT NOT NULL,
  description TEXT,
  is_secret BOOLEAN NOT NULL DEFAULT FALSE,
  overrides_project BOOLEAN,
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(suite_id, name)
);

-- Create test_file_attachments table
CREATE TABLE test_file_attachments (
  file_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  test_case_id UUID NOT NULL REFERENCES test_cases(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  content_type TEXT NOT NULL,
  upload_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  file_path TEXT
);

-- Add foreign key constraint for default_environment_id
ALTER TABLE projects
ADD CONSTRAINT projects_default_environment_id_fkey
FOREIGN KEY (default_environment_id) REFERENCES environments(env_id);

-- Create indexes for better performance
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_environments_project_id ON environments(project_id);
CREATE INDEX idx_test_suites_project_id ON test_suites(project_id);
CREATE INDEX idx_test_cases_suite_id ON test_cases(suite_id);
CREATE INDEX idx_test_cases_project_id ON test_cases(project_id);
CREATE INDEX idx_project_variables_project_id ON project_variables(project_id);
CREATE INDEX idx_suite_variables_suite_id ON suite_variables(suite_id);
CREATE INDEX idx_test_file_attachments_test_case_id ON test_file_attachments(test_case_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_environments_updated_at BEFORE UPDATE ON environments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_test_suites_updated_at BEFORE UPDATE ON test_suites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_test_cases_updated_at BEFORE UPDATE ON test_cases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_project_variables_updated_at BEFORE UPDATE ON project_variables FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suite_variables_updated_at BEFORE UPDATE ON suite_variables FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE environments ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_suites ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_variables ENABLE ROW LEVEL SECURITY;
ALTER TABLE suite_variables ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_file_attachments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Profiles: Users can only see and edit their own profile
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- Projects: Users can only see and manage their own projects
CREATE POLICY "Users can view own projects" ON projects FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own projects" ON projects FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own projects" ON projects FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own projects" ON projects FOR DELETE USING (auth.uid() = user_id);

-- Environments: Users can only manage environments of their own projects
CREATE POLICY "Users can view environments of own projects" ON environments FOR SELECT
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = environments.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can insert environments for own projects" ON environments FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = environments.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can update environments of own projects" ON environments FOR UPDATE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = environments.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can delete environments of own projects" ON environments FOR DELETE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = environments.project_id AND projects.user_id = auth.uid()));

-- Test Suites: Users can only manage test suites of their own projects
CREATE POLICY "Users can view test suites of own projects" ON test_suites FOR SELECT
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_suites.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can insert test suites for own projects" ON test_suites FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_suites.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can update test suites of own projects" ON test_suites FOR UPDATE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_suites.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can delete test suites of own projects" ON test_suites FOR DELETE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_suites.project_id AND projects.user_id = auth.uid()));

-- Test Cases: Users can only manage test cases of their own projects
CREATE POLICY "Users can view test cases of own projects" ON test_cases FOR SELECT
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_cases.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can insert test cases for own projects" ON test_cases FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_cases.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can update test cases of own projects" ON test_cases FOR UPDATE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_cases.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can delete test cases of own projects" ON test_cases FOR DELETE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = test_cases.project_id AND projects.user_id = auth.uid()));

-- Project Variables: Users can only manage variables of their own projects
CREATE POLICY "Users can view project variables of own projects" ON project_variables FOR SELECT
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = project_variables.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can insert project variables for own projects" ON project_variables FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = project_variables.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can update project variables of own projects" ON project_variables FOR UPDATE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = project_variables.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can delete project variables of own projects" ON project_variables FOR DELETE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = project_variables.project_id AND projects.user_id = auth.uid()));

-- Suite Variables: Users can only manage suite variables of their own projects
CREATE POLICY "Users can view suite variables of own projects" ON suite_variables FOR SELECT
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = suite_variables.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can insert suite variables for own projects" ON suite_variables FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = suite_variables.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can update suite variables of own projects" ON suite_variables FOR UPDATE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = suite_variables.project_id AND projects.user_id = auth.uid()));

CREATE POLICY "Users can delete suite variables of own projects" ON suite_variables FOR DELETE
USING (EXISTS (SELECT 1 FROM projects WHERE projects.project_id = suite_variables.project_id AND projects.user_id = auth.uid()));

-- Test File Attachments: Users can only manage file attachments of their own test cases
CREATE POLICY "Users can view test file attachments of own projects" ON test_file_attachments FOR SELECT
USING (EXISTS (
  SELECT 1 FROM test_cases
  JOIN projects ON projects.project_id = test_cases.project_id
  WHERE test_cases.id = test_file_attachments.test_case_id AND projects.user_id = auth.uid()
));

CREATE POLICY "Users can insert test file attachments for own projects" ON test_file_attachments FOR INSERT
WITH CHECK (EXISTS (
  SELECT 1 FROM test_cases
  JOIN projects ON projects.project_id = test_cases.project_id
  WHERE test_cases.id = test_file_attachments.test_case_id AND projects.user_id = auth.uid()
));

CREATE POLICY "Users can update test file attachments of own projects" ON test_file_attachments FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM test_cases
  JOIN projects ON projects.project_id = test_cases.project_id
  WHERE test_cases.id = test_file_attachments.test_case_id AND projects.user_id = auth.uid()
));

CREATE POLICY "Users can delete test file attachments of own projects" ON test_file_attachments FOR DELETE
USING (EXISTS (
  SELECT 1 FROM test_cases
  JOIN projects ON projects.project_id = test_cases.project_id
  WHERE test_cases.id = test_file_attachments.test_case_id AND projects.user_id = auth.uid()
));

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, username, full_name, email_verified)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    NEW.email_confirmed_at IS NOT NULL
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();