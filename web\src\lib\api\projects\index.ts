/**
 * Projects API Module Index
 * 
 * Centralized exports for all project-related API functions including:
 * - Project CRUD operations
 * - Test Suite management
 * - Test Case management with file support
 * 
 * @example
 * ```typescript
 * import { 
 *   getProjects, 
 *   createProject, 
 *   getSuitesByProjectId,
 *   createTestCaseWithFiles 
 * } from '@/lib/api/projects';
 * ```
 */

// Project management
export {
  getProjects,
  createProject,
  getProjectById,
  updateProject,
  deleteProject,
  exportProject,
  importProject,
} from './projects';

// Test Suite management
export {
  getSuitesByProjectId,
  createSuite,
  getSuiteById,
  updateSuite,
  deleteSuite,
} from './suites';

// Test Case management
export {
  getTestCasesBySuiteId,
  createTestCase,
  createTestCaseWithFiles,
  getTestCaseById,
  updateTestCase,
  updateTestCaseStatus,
  deleteTestCase,
} from './testCases';

// Environment management
export {
  getProjectEnvironments,
  getProjectEnvironment,
  createProjectEnvironment,
  updateProjectEnvironment,
  deleteProjectEnvironment,
  setDefaultEnvironment,
  getDefaultEnvironment,
  getEnvironmentsByTag,
  testEnvironmentUrl,
} from './environments';

// Variables management
export {
  getProjectVariables,
  createProjectVariable,
  updateProjectVariable,
  deleteProjectVariable,
  getSuiteVariables,
  createSuiteVariable,
  updateSuiteVariable,
  deleteSuiteVariable,
  getComputedVariables,
} from './variables';