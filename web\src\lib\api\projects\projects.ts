import { fetchApi } from '../core/fetch';
import { AuthService } from '../../auth';
import type {
  Project,
  ProjectCreateInput,
  ProjectUpdateInput,
} from '@/lib/types';

/**
 * Projects API Module
 * 
 * Provides CRUD operations for project management including:
 * - Project creation, retrieval, update, and deletion
 * - Backend response mapping from C# to TypeScript types
 * - Handling of different response formats (PagedResponse, BaseResponse, etc.)
 * 
 * @example
 * ```typescript
 * // Get all projects
 * const projects = await getProjects();
 * 
 * // Create new project
 * const newProject = await createProject({
 *   name: "My Test Project",
 *   description: "Project description"
 * });
 * 
 * // Get specific project
 * const project = await getProjectById("project-uuid");
 * ```
 */

/**
 * Retrieves all projects for the current user/tenant
 * 
 * Handles multiple backend response formats and maps C# DTO to frontend types.
 * Supports snake_case (JsonPropertyName), camelCase, and PascalCase field variations.
 * 
 * @returns Promise<Project[]> Array of mapped projects
 * @throws Error if the request fails or response format is invalid
 */
export const getProjects = async (): Promise<Project[]> => {
  try {
    const backendResponse = await fetchApi<any>('/projects');
    
    // Handle PagedResponse format from C# backend
    let backendProjects: any[] = [];
    if (backendResponse && typeof backendResponse === 'object') {
      // Check if it's a PagedResponse/BaseResponse with Data property
      if (backendResponse.Data && Array.isArray(backendResponse.Data)) {
        backendProjects = backendResponse.Data;
      }
      // Check if it has Items property (alternative structure)
      else if (backendResponse.Items || backendResponse.items) {
        backendProjects = backendResponse.Items || backendResponse.items;
      }
      // Check if the response itself is an array (fallback)
      else if (Array.isArray(backendResponse)) {
        backendProjects = backendResponse;
      }
      else {
        console.warn('Unexpected backend response format:', backendResponse);
        return [];
      }
    }
    
    if (!Array.isArray(backendProjects)) {
      console.error('Backend projects is not an array:', backendProjects);
      return [];
    }
    
    // The C# DTO already uses JsonPropertyName with snake_case, so we can use it directly
    // But we'll also handle any potential naming variations for robustness
    const mappedProjects = backendProjects.map(project => {
      if (!project) {
        console.warn('Null project found in backend data');
        return null;
      }
      
      // Primary mapping: use id field (ObjectId from MongoDB)
      // Fallback mapping: handle legacy project_id and other variations
      const mappedProject = {
        project_id: project.id || project.Id || project.project_id || project.projectId || project.ProjectId || '',
        name: project.name || project.Name || '',
        description: project.description || project.Description || '',
        tags: project.tags || project.Tags || [],
        created_at: project.created_at || project.createdAt || project.CreatedAt || new Date().toISOString(),
        updated_at: project.updated_at || project.updatedAt || project.UpdatedAt || new Date().toISOString(),
        test_suites: project.test_suites || project.testSuites || project.TestSuites || [],
        environments: project.environments || project.Environments || [],
        default_environment_id: project.default_environment_id || project.defaultEnvironmentId || project.DefaultEnvironmentId,
        github_config: project.github_config || project.gitHubConfig || project.GitHubConfig
      };
      
      return mappedProject;
    }).filter(Boolean) as Project[]; // Remove any null projects
    
    console.log('Mapped projects count:', mappedProjects.length);

    return mappedProjects;
  } catch (error) {
    console.error('Error in getProjects:', error);
    throw error;
  }
};

/**
 * Creates a new project
 * 
 * @param data - Project creation data
 * @returns Promise<Project> Created project with backend-generated fields
 * @throws Error if creation fails or validation errors occur
 */
export const createProject = async (data: ProjectCreateInput): Promise<Project> => {
  const response = await fetchApi<{ success: boolean; data: any }>('/projects', { 
    method: 'POST', 
    body: JSON.stringify(data) 
  });
  
  // Handle the response structure from Supabase backend
  if (response.success && response.data) {
    const project = response.data;
    
    // Map the project with consistent field naming
    return {
      project_id: project.project_id || project.id || '',
      name: project.name || '',
      description: project.description || '',
      tags: project.tags || [],
      created_at: project.created_at || new Date().toISOString(),
      updated_at: project.updated_at || new Date().toISOString(),
      test_suites: project.test_suites || [],
      environments: project.environments || [],
      default_environment_id: project.default_environment_id || null,
      github_config: project.github_config || null
    };
  }
  
  throw new Error('Failed to create project: Invalid response format');
};

/**
 * Retrieves a specific project by ID
 * 
 * Maps backend response format to frontend Project type with fallback field mapping.
 * 
 * @param projectId - Unique project identifier
 * @returns Promise<Project> Mapped project data
 * @throws Error if project not found or request fails
 */
export const getProjectById = async (projectId: string): Promise<Project> => {
  try {
    const response = await fetchApi<any>(`/projects/${projectId}`);
    
    // Handle BaseResponse format from C# backend
    let project: any = response;
    if (response && typeof response === 'object' && response.Data) {
      project = response.Data;
    }
    
    if (!project) {
      throw new Error('Project not found');
    }
    
    // Map the project with the same logic as getProjects
    const mappedProject = {
      project_id: project.id || project.Id || project.project_id || project.projectId || project.ProjectId || '',
      name: project.name || project.Name || '',
      description: project.description || project.Description || '',
      tags: project.tags || project.Tags || [],
      created_at: project.created_at || project.createdAt || project.CreatedAt || new Date().toISOString(),
      updated_at: project.updated_at || project.updatedAt || project.UpdatedAt || new Date().toISOString(),
      test_suites: project.test_suites || project.testSuites || project.TestSuites || [],
      environments: project.environments || project.Environments || [],
      default_environment_id: project.default_environment_id || project.defaultEnvironmentId || project.DefaultEnvironmentId,
      github_config: project.github_config || project.gitHubConfig || project.GitHubConfig
    };
    
    return mappedProject as Project;
  } catch (error) {
    console.error('Error in getProjectById:', error);
    throw error;
  }
};

/**
 * Updates an existing project
 * 
 * @param projectId - Unique project identifier
 * @param data - Updated project data
 * @returns Promise<Project> Updated project
 * @throws Error if update fails or project not found
 */
export const updateProject = (projectId: string, data: ProjectUpdateInput): Promise<Project> => 
  fetchApi<Project>(`/projects/${projectId}`, { method: 'PUT', body: JSON.stringify(data) });

/**
 * Deletes a project
 * 
 * @param projectId - Unique project identifier
 * @returns Promise<void>
 * @throws Error if deletion fails or project not found
 */
export const deleteProject = (projectId: string): Promise<void> => 
  fetchApi<void>(`/projects/${projectId}`, { method: 'DELETE' });

/**
 * Exports project data as JSON
 *
 * @param projectId - Unique project identifier
 * @returns Promise<any> Exported project data
 * @throws Error if export fails
 */
export const exportProject = async (projectId: string): Promise<any> => {
  const response = await fetchApi(`/projects/${projectId}/export`);
  return response;
};

/**
 * Imports project data from a file
 *
 * @param file - File to import (JSON format)
 * @returns Promise<any> Import result
 * @throws Error if import fails
 */
export const importProject = async (file: File): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('/api/projects/import', {
    method: 'POST',
    body: formData,
    headers: {
      'ngrok-skip-browser-warning': 'true',
      ...AuthService.getAuthHeaders(),
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Import failed: ${response.status} ${errorText}`);
  }

  return response.json();
};