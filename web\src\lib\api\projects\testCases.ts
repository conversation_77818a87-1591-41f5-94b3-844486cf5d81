import { fetchApi, fetchApiWithFormData } from '../core/fetch';
import { AuthService } from '@/lib/auth';
import type {
  TestCase,
  TestCaseCreateInput,
  TestCaseUpdateInput,
  TestCaseStatusUpdateInput,
  ApiResponse,
} from '@/lib/types';

/**
 * Test Cases API Module
 * 
 * Provides CRUD operations for test case management including:
 * - Test case creation, retrieval, update, and deletion within suites
 * - File upload support for test cases with attachments
 * - Backend format mapping (historia_de_usuario ↔ historiaDeUsuario)
 * - Status management and execution tracking
 * 
 * @example
 * ```typescript
 * // Get all test cases for a suite
 * const response = await getTestCasesBySuiteId("project-uuid", "suite-uuid");
 * const testCases = response.items;
 * 
 * // Create test case with files
 * const newTestCase = await createTestCaseWithFiles("project-uuid", "suite-uuid", {
 *   name: "Login Test",
 *   description: "Test user login functionality",
 *   file_attachments: [file1, file2],
 *   enable_file_handling: true
 * });
 * ```
 */

/**
 * Helper function to map frontend TestCase format to backend format
 * 
 * Handles the field name differences between frontend and backend:
 * - user_story (frontend) → historiaDeUsuario (backend)
 * - instructions remains the same
 */
const mapTestCaseToBackendFormat = (frontendData: TestCaseCreateInput | TestCaseUpdateInput) => {
  const { user_story, instructions, ...rest } = frontendData;
  return {
    ...rest,
    historiaDeUsuario: user_story,  // Map frontend user_story to backend historiaDeUsuario
    instructions: instructions             // Keep instructions as-is (same in both frontend and backend)
  };
};

/**
 * Retrieves all test cases for a specific test suite
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @returns Promise<ApiResponse<TestCase>> Response with items array and metadata
 * @throws Error if the request fails or suite not found
 */
export const getTestCasesBySuiteId = (projectId: string, suiteId: string): Promise<ApiResponse<TestCase>> => 
  fetchApi<ApiResponse<TestCase>>(`/projects/${projectId}/suites/${suiteId}/tests`);

/**
 * Creates a new test case within a test suite
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param data - Test case creation data
 * @returns Promise<TestCase> Created test case with backend-generated fields
 * @throws Error if creation fails or validation errors occur
 */
export const createTestCase = async (projectId: string, suiteId: string, data: TestCaseCreateInput): Promise<TestCase> => {
  // Map frontend format to backend format before sending
  const backendData = mapTestCaseToBackendFormat(data);
  const response = await fetchApi<{success: boolean, data: TestCase}>(`/projects/${projectId}/suites/${suiteId}/tests`, { 
    method: 'POST', 
    body: JSON.stringify(backendData) 
  });
  return response.data;
};

/**
 * Creates a new test case with file attachments
 * 
 * Uses multipart/form-data for file uploads. Supports multiple files and
 * optional file processing on the backend.
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param data - Test case creation data with file attachments
 * @returns Promise<TestCase> Created test case with file references
 * @throws Error if creation fails or file upload errors occur
 */
export const createTestCaseWithFiles = async (
  projectId: string,
  suiteId: string,
  data: TestCaseCreateInput
): Promise<TestCase> => {
  const formData = new FormData();

  // Map frontend format to backend format
  const backendData = mapTestCaseToBackendFormat(data);

  // Add test case data as JSON
  formData.append('testCase', JSON.stringify(backendData));

  // Add files if present
  if (data.file_attachments && data.file_attachments.length > 0) {
    data.file_attachments.forEach((file, index) => {
      // Ensure file is actually a File object before appending
      if (file instanceof File) {
        formData.append(`files`, file);
      } else {
        console.warn(`Skipping invalid file at index ${index}:`, file);
      }
    });
  }

  // Add enable_file_handling flag
  formData.append('enableFileHandling', String(data.enable_file_handling || false));
  
  // Use a modified fetchApi for FormData
  const fullUrl = `/projects/${projectId}/suites/${suiteId}/tests/with-files`;
  const response = await fetch(fullUrl.startsWith('/api/') ? fullUrl : `/api${fullUrl}`, {
    method: 'POST',
    headers: {
      'ngrok-skip-browser-warning': 'true',
      ...AuthService.getAuthHeaders(),
    },
    body: formData,
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  const result = await response.json();
  return result.data;
};

/**
 * Retrieves a specific test case by ID
 * 
 * Maps backend response format to frontend TestCase type with field name conversion.
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param testId - Unique test case identifier
 * @returns Promise<TestCase> Mapped test case data
 * @throws Error if test case not found or request fails
 */
export const getTestCaseById = async (projectId: string, suiteId: string, testId: string): Promise<TestCase> => {
  // Use the dedicated test case endpoint
  const response = await fetchApi<{success: boolean, data: any}>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`);
  
  if (!response.success || !response.data) {
    throw new Error(`Test case with ID ${testId} not found in suite ${suiteId}`);
  }

  const testCase = response.data;

  // Map backend test case format to frontend format
  return {
    id: testCase.id || testCase.Id || testId,
    suite_id: testCase.suiteId || testCase.SuiteId || suiteId,
    project_id: testCase.projectId || testCase.ProjectId || projectId,
    name: testCase.name || testCase.Name || '',
    description: testCase.description || testCase.Description || '',
    instructions: testCase.instructions || testCase.instrucciones || testCase.Instrucciones || '',
    user_story: testCase.user_story || testCase.historia_de_usuario || testCase.historiaDeUsuario || testCase.HistoriaDeUsuario || '',
    gherkin: testCase.gherkin || testCase.Gherkin || '',
    url: testCase.url || testCase.Url || '',
    tags: testCase.tags || testCase.Tags || [],
    status: testCase.status || testCase.Status || 'Not Executed',
    priority: testCase.priority || testCase.Priority || 'Medium',
    type: testCase.type || testCase.Type || 'functional',
    created_at: testCase.createdAt || testCase.CreatedAt || new Date().toISOString(),
    updated_at: testCase.updatedAt || testCase.UpdatedAt || new Date().toISOString(),
    history_files: testCase.historyFiles || testCase.HistoryFiles || [],
    last_execution: testCase.lastExecution || testCase.LastExecution || null,
  };
};

/**
 * Updates an existing test case
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param testId - Unique test case identifier
 * @param data - Updated test case data
 * @returns Promise<TestCase> Updated test case
 * @throws Error if update fails or test case not found
 */
export const updateTestCase = (projectId: string, suiteId: string, testId: string, data: TestCaseUpdateInput): Promise<TestCase> => {
  // Map frontend format to backend format before sending
  const backendData = mapTestCaseToBackendFormat(data);
  return fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { 
    method: 'PUT', 
    body: JSON.stringify(backendData) 
  });
};

/**
 * Updates the status of a test case
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param testId - Unique test case identifier
 * @param data - Status update data
 * @returns Promise<TestCase> Updated test case
 * @throws Error if update fails or test case not found
 */
export const updateTestCaseStatus = (projectId: string, suiteId: string, testId: string, data: TestCaseStatusUpdateInput): Promise<TestCase> => 
  fetchApi<TestCase>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/status`, { 
    method: 'PATCH', 
    body: JSON.stringify(data) 
  });

/**
 * Deletes a test case
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param testId - Unique test case identifier
 * @returns Promise<void>
 * @throws Error if deletion fails or test case not found
 */
export const deleteTestCase = (projectId: string, suiteId: string, testId: string): Promise<void> => 
  fetchApi<void>(`/projects/${projectId}/suites/${suiteId}/tests/${testId}`, { method: 'DELETE' });