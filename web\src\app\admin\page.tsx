'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Users, Building2, Brain, Activity, TrendingUp, AlertTriangle, Cpu, Shield, Zap, RefreshCw, Building } from 'lucide-react'
import { 
  getGlobalUserStatistics, 
  getGlobalTenantSummary, 
  getLLMProviders, 
  getLLMModels, 
  getApiHealth 
} from '@/lib/api'

interface DashboardStats {
  users: {
    total: number
    active: number
    superAdmins: number
    admins: number
  }
  tenants: {
    total: number
    active: number
    trialEnded: number
  }
  llm: {
    providers: number
    models: number
    credentials: number
  }
  system: {
    apiKeyConfigured: boolean
    databaseStatus: string
  }
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      // Fetch all dashboard data in parallel using API functions
      const [usersData, tenantsData, healthData, providersData, modelsData] = await Promise.allSettled([
        getGlobalUserStatistics(),
        getGlobalTenantSummary(),
        getApiHealth(),
        getLLMProviders(),
        getLLMModels()
      ])
      
      // Extract data from settled promises
      const usersResult = usersData.status === 'fulfilled' ? usersData.value : null
      const tenantsResult = tenantsData.status === 'fulfilled' ? tenantsData.value : null
      const healthResult = healthData.status === 'fulfilled' ? healthData.value : null
      const providersResult = providersData.status === 'fulfilled' ? providersData.value : null
      const modelsResult = modelsData.status === 'fulfilled' ? modelsData.value : null

      // Log any errors
      if (usersData.status === 'rejected') {
        console.error('Failed to fetch users:', usersData.reason)
      }
      if (tenantsData.status === 'rejected') {
        console.error('Failed to fetch tenants:', tenantsData.reason)
      }
      if (healthData.status === 'rejected') {
        console.error('Failed to fetch health:', healthData.reason)
      }
      if (providersData.status === 'rejected') {
        console.error('Failed to fetch providers:', providersData.reason)
      }
      if (modelsData.status === 'rejected') {
        console.error('Failed to fetch models:', modelsData.reason)
      }
      
      setStats({
        users: {
          total: (usersResult as any)?.data?.totalUsers || 0,
          active: (usersResult as any)?.data?.activeUsers || 0,
          superAdmins: (usersResult as any)?.data?.superAdmins || 0,
          admins: (usersResult as any)?.data?.admins || 0,
        },
        tenants: {
          total: (tenantsResult as any)?.data?.totalTenants || 0,
          active: (tenantsResult as any)?.data?.activeTenants || 0,
          trialEnded: (tenantsResult as any)?.data?.trialEndedTenants || 0,
        },
        llm: {
          providers: (providersResult as any)?.data?.length || (Array.isArray(providersResult) ? providersResult.length : 0),
          models: (modelsResult as any)?.data?.length || (Array.isArray(modelsResult) ? modelsResult.length : 0),
          credentials: 0, // Will be updated when credentials endpoint is called
        },
        system: {
          apiKeyConfigured: (healthResult as any)?.api_key_configured || false,
          databaseStatus: (healthResult as any)?.database_status || 'unknown',
        },
      })
    } catch (err) {
      setError('Failed to fetch dashboard statistics')
      console.error('Dashboard stats error:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-destructive">{error}</div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
       <div className="flex items-center justify-between">
         <div>
           <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
             Panel de Administración
           </h1>
           <p className="text-muted-foreground mt-2 text-lg">
             Gestión y monitoreo del sistema QAK
           </p>
         </div>
         <div className="flex items-center space-x-4">
           <div className="bg-gradient-to-r from-accent to-primary text-primary-foreground px-4 py-2 rounded-xl text-sm font-medium">
             ✓ Sistema Operativo
            </div>
            <button className="bg-gradient-to-r from-primary to-accent text-primary-foreground px-6 py-2 rounded-xl font-medium hover:shadow-lg transition-all duration-200">
              Actualizar Datos
            </button>
          </div>
        </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="system">System Health</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
           <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
             <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20 hover:shadow-lg transition-all duration-300">
               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                 <CardTitle className="text-sm font-semibold text-foreground">Total de Usuarios</CardTitle>
                 <div className="p-2 bg-primary rounded-xl">
                   <Users className="h-5 w-5 text-primary-foreground" />
                 </div>
               </CardHeader>
               <CardContent>
                 <div className="text-3xl font-bold text-foreground">{stats?.users.total}</div>
                 <p className="text-sm text-muted-foreground mt-1">
                   {stats?.users.active} usuarios activos
                 </p>
                 <div className="mt-3 h-1 bg-muted rounded-full">
                   <div
                     className="h-1 bg-primary rounded-full transition-all duration-500"
                     style={{ width: `${(stats?.users.active || 0) / (stats?.users.total || 1) * 100}%` }}
                   />
                 </div>
               </CardContent>
             </Card>

             <Card className="bg-gradient-to-br from-accent/5 to-accent/10 border-accent/20 hover:shadow-lg transition-all duration-300">
               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                 <CardTitle className="text-sm font-semibold text-foreground">Tenants</CardTitle>
                 <div className="p-2 bg-accent rounded-xl">
                   <Building2 className="h-5 w-5 text-accent-foreground" />
                 </div>
               </CardHeader>
               <CardContent>
                 <div className="text-3xl font-bold text-foreground">{stats?.tenants.total}</div>
                 <p className="text-sm text-muted-foreground mt-1">
                   {stats?.tenants.active} tenants activos
                 </p>
                 <div className="mt-3 h-1 bg-muted rounded-full">
                   <div
                     className="h-1 bg-accent rounded-full transition-all duration-500"
                     style={{ width: `${(stats?.tenants.active || 0) / (stats?.tenants.total || 1) * 100}%` }}
                   />
                 </div>
               </CardContent>
             </Card>

             <Card className="bg-gradient-to-br from-secondary/50 to-secondary/30 border-secondary/20 hover:shadow-lg transition-all duration-300">
               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                 <CardTitle className="text-sm font-semibold text-foreground">Proveedores LLM</CardTitle>
                 <div className="p-2 bg-secondary-foreground rounded-xl">
                   <Brain className="h-5 w-5 text-secondary" />
                 </div>
               </CardHeader>
               <CardContent>
                 <div className="text-3xl font-bold text-foreground">{stats?.llm.providers}</div>
                 <p className="text-sm text-muted-foreground mt-1">
                   {stats?.llm.models} modelos disponibles
                 </p>
                 <div className="mt-3 h-1 bg-muted rounded-full">
                   <div
                     className="h-1 bg-secondary-foreground rounded-full transition-all duration-500"
                     style={{ width: `${Math.min((stats?.llm.models || 0) / 10 * 100, 100)}%` }}
                   />
                 </div>
               </CardContent>
             </Card>

             <Card className="bg-gradient-to-br from-muted/50 to-muted/30 border-muted/20 hover:shadow-lg transition-all duration-300">
               <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                 <CardTitle className="text-sm font-semibold text-foreground">Estado del Sistema</CardTitle>
                 <div className={`p-2 rounded-xl ${stats?.system.apiKeyConfigured ? 'bg-accent' : 'bg-destructive'}`}>
                   <Activity className="h-5 w-5 text-primary-foreground" />
                 </div>
               </CardHeader>
               <CardContent>
                 <div className={`text-3xl font-bold ${stats?.system.apiKeyConfigured ? 'text-accent' : 'text-destructive'}`}>
                   {stats?.system.apiKeyConfigured ? '✓' : '✗'}
                 </div>
                 <p className={`text-sm mt-1 ${stats?.system.apiKeyConfigured ? 'text-accent' : 'text-destructive'}`}>
                   {stats?.system.apiKeyConfigured ? 'API Configurada' : 'API No Configurada'}
                 </p>
                 <div className="mt-3 h-1 bg-muted rounded-full">
                   <div
                     className={`h-1 rounded-full transition-all duration-500 ${stats?.system.apiKeyConfigured ? 'bg-accent' : 'bg-destructive'}`}
                     style={{ width: stats?.system.apiKeyConfigured ? '100%' : '25%' }}
                   />
                 </div>
               </CardContent>
             </Card>
           </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
             <Card className="col-span-4 bg-gradient-to-br from-card to-muted/20 border-border">
               <CardHeader>
                 <CardTitle className="text-xl font-bold text-foreground flex items-center gap-2">
                   <Users className="h-5 w-5" />
                   Distribución de Usuarios
                 </CardTitle>
                 <CardDescription className="text-muted-foreground">
                   Análisis de roles y permisos en el sistema
                 </CardDescription>
               </CardHeader>
               <CardContent className="pl-2">
                 <div className="space-y-6">
                   <div className="flex items-center">
                     <div className="w-24 text-sm font-medium text-foreground">Super Admins</div>
                     <div className="flex-1 mx-4">
                       <div className="h-3 bg-muted rounded-full">
                         <div
                           className="h-3 bg-gradient-to-r from-destructive to-destructive/80 rounded-full transition-all duration-700"
                           style={{ width: `${(stats?.users.superAdmins || 0) / (stats?.users.total || 1) * 100}%` }}
                         />
                       </div>
                     </div>
                     <div className="w-12 text-sm font-bold text-destructive">{stats?.users.superAdmins}</div>
                   </div>
                   <div className="flex items-center">
                     <div className="w-24 text-sm font-medium text-foreground">Admins</div>
                     <div className="flex-1 mx-4">
                       <div className="h-3 bg-muted rounded-full">
                         <div
                           className="h-3 bg-gradient-to-r from-accent to-accent/80 rounded-full transition-all duration-700"
                           style={{ width: `${(stats?.users.admins || 0) / (stats?.users.total || 1) * 100}%` }}
                         />
                       </div>
                     </div>
                     <div className="w-12 text-sm font-bold text-accent">{stats?.users.admins}</div>
                   </div>
                   <div className="flex items-center">
                     <div className="w-24 text-sm font-medium text-foreground">Usuarios</div>
                     <div className="flex-1 mx-4">
                       <div className="h-3 bg-muted rounded-full">
                         <div
                           className="h-3 bg-gradient-to-r from-primary to-primary/80 rounded-full transition-all duration-700"
                           style={{ width: `${((stats?.users.total || 0) - (stats?.users.superAdmins || 0) - (stats?.users.admins || 0)) / (stats?.users.total || 1) * 100}%` }}
                         />
                       </div>
                     </div>
                     <div className="w-12 text-sm font-bold text-primary">{(stats?.users.total || 0) - (stats?.users.superAdmins || 0) - (stats?.users.admins || 0)}</div>
                   </div>
                 </div>
               </CardContent>
             </Card>
            
             <Card className="col-span-3 bg-gradient-to-br from-accent/5 to-accent/10 border-accent/20">
               <CardHeader>
                 <CardTitle className="text-xl font-bold text-accent-foreground flex items-center gap-2">
                   <Activity className="h-5 w-5" />
                   Salud del Sistema
                 </CardTitle>
                 <CardDescription className="text-accent-foreground/70">
                   Estado actual de los servicios
                 </CardDescription>
               </CardHeader>
               <CardContent>
                 <div className="space-y-4">
                   <div className="flex items-center justify-between p-3 bg-card/50 rounded-xl">
                     <span className="text-sm font-medium text-foreground">API Keys</span>
                     <span className={`text-sm font-bold px-3 py-1 rounded-full ${stats?.system.apiKeyConfigured ? 'bg-accent/10 text-accent' : 'bg-destructive/10 text-destructive'}`}>
                       {stats?.system.apiKeyConfigured ? '✓ Configuradas' : '✗ Faltantes'}
                     </span>
                   </div>
                   <div className="flex items-center justify-between p-3 bg-card/50 rounded-xl">
                     <span className="text-sm font-medium text-foreground">Base de Datos</span>
                     <span className="text-sm font-bold px-3 py-1 rounded-full bg-accent/10 text-accent">✓ Conectada</span>
                   </div>
                   <div className="flex items-center justify-between p-3 bg-card/50 rounded-xl">
                     <span className="text-sm font-medium text-foreground">Proxy API</span>
                     <span className="text-sm font-bold px-3 py-1 rounded-full bg-accent/10 text-accent">✓ api.qak.app</span>
                   </div>
                 </div>
               </CardContent>
             </Card>
          </div>
        </TabsContent>
        
         <TabsContent value="system" className="space-y-4">
           <div className="grid gap-4 md:grid-cols-2">
             <Card className="bg-card border-border">
               <CardHeader>
                 <CardTitle className="flex items-center gap-2 text-foreground">
                   <Activity className="h-5 w-5" />
                   System Health
                 </CardTitle>
               </CardHeader>
               <CardContent>
                 <div className="space-y-3">
                   <div className="flex items-center justify-between">
                     <span className="text-foreground">API Configuration</span>
                     <span className={`px-2 py-1 rounded text-xs ${
                       stats?.system.apiKeyConfigured
                         ? 'bg-accent/10 text-accent'
                         : 'bg-destructive/10 text-destructive'
                     }`}>
                       {stats?.system.apiKeyConfigured ? 'OK' : 'MISSING'}
                     </span>
                   </div>
                   <div className="flex items-center justify-between">
                     <span className="text-foreground">Database Connection</span>
                     <span className="px-2 py-1 rounded text-xs bg-accent/10 text-accent">
                       CONNECTED
                     </span>
                   </div>
                 </div>
               </CardContent>
             </Card>

             <Card className="bg-gradient-to-br from-secondary/30 to-secondary/20 border-secondary/20">
               <CardHeader>
                 <CardTitle className="flex items-center gap-2 text-secondary-foreground">
                   <AlertTriangle className="h-5 w-5" />
                   Acciones Rápidas
                 </CardTitle>
                 <CardDescription className="text-secondary-foreground/70">
                   Herramientas de administración del sistema
                 </CardDescription>
               </CardHeader>
               <CardContent>
                 <div className="space-y-3">
                   <button className="w-full text-left p-3 bg-card hover:bg-card/80 hover:shadow-md rounded-xl transition-all duration-200 flex items-center gap-3 group">
                     <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                       <Activity className="h-4 w-4 text-primary" />
                     </div>
                     <div>
                       <div className="font-medium text-card-foreground">Ver Logs del Sistema</div>
                       <div className="text-xs text-muted-foreground">Monitorear actividad y errores</div>
                     </div>
                   </button>
                   <button className="w-full text-left p-3 bg-card hover:bg-card/80 hover:shadow-md rounded-xl transition-all duration-200 flex items-center gap-3 group">
                     <div className="p-2 bg-accent/10 rounded-lg group-hover:bg-accent/20 transition-colors">
                       <Brain className="h-4 w-4 text-accent" />
                     </div>
                     <div>
                       <div className="font-medium text-card-foreground">Gestionar API Keys</div>
                       <div className="text-xs text-muted-foreground">Configurar credenciales LLM</div>
                     </div>
                   </button>
                   <button className="w-full text-left p-3 bg-card hover:bg-card/80 hover:shadow-md rounded-xl transition-all duration-200 flex items-center gap-3 group">
                     <div className="p-2 bg-secondary/20 rounded-lg group-hover:bg-secondary/30 transition-colors">
                       <TrendingUp className="h-4 w-4 text-secondary-foreground" />
                     </div>
                     <div>
                       <div className="font-medium text-card-foreground">Backup del Sistema</div>
                       <div className="text-xs text-muted-foreground">Crear respaldo de datos</div>
                     </div>
                   </button>
                 </div>
               </CardContent>
             </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}