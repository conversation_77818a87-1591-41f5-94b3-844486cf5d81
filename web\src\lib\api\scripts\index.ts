import { fetchApi } from '../core/fetch';

/**
 * Input interface for script generation
 */
export interface GenerateScriptInput {
  execution_id: string;
  framework: string;
  options?: any;
}

/**
 * Input interface for saving execution script
 */
export interface SaveExecutionScriptInput {
  script_content: string;
  framework: string;
  options?: any;
}

/**
 * Input interface for generating code from script
 */
export interface GenerateCodeFromScriptInput {
  script_content: string;
  framework: string;
  options?: any;
}

/**
 * Get existing script for execution and framework
 */
export const getExecutionScript = (executionId: string, framework: string): Promise<any> =>
  fetchApi(`/v2/script-generation/executions/${executionId}/scripts/${framework}`);

/**
 * Generate new script from execution
 */
export const generateScript = (data: GenerateScriptInput): Promise<any> => fetchApi('/v2/script-generation/generate', {
  method: 'POST',
  body: JSON.stringify(data)
});

/**
 * Save script to execution
 */
export const saveExecutionScript = (executionId: string, framework: string, data: SaveExecutionScriptInput): Promise<any> => fetchApi(`/v2/script-generation/executions/${executionId}/scripts/${framework}`, {
  method: 'POST',
  body: JSON.stringify(data)
});

/**
 * Generate code from script
 */
export const generateCodeFromScript = (data: GenerateCodeFromScriptInput): Promise<any> => fetchApi('/v2/script-generation/generate-code', {
  method: 'POST',
  body: JSON.stringify(data)
});