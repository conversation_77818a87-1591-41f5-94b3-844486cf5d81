import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { SupabaseAuthServerService } from '@/lib/auth-supabase-server'
import type { Database } from '@/lib/supabase/database.types'

type TestSuite = Database['public']['Tables']['test_suites']['Row']
type TestSuiteInsert = Database['public']['Tables']['test_suites']['Insert']

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const user = await SupabaseAuthServerService.getCurrentUserServer()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { projectId } = resolvedParams
    const supabase = await createClient()
    
    // First verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('project_id')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const { data: suites, error } = await supabase
      .from('test_suites')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching test suites:', error)
      return NextResponse.json({ error: 'Failed to fetch test suites' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      count: suites?.length || 0,
      items: suites || []
    })
  } catch (error) {
    console.error('Error in GET /api/projects/[projectId]/suites:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const user = await SupabaseAuthServerService.getCurrentUserServer()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const resolvedParams = await params
    const { projectId } = resolvedParams
    const body = await request.json()
    const { name, description, type = 'manual', tags = [] } = body

    if (!name || !description) {
      return NextResponse.json({ error: 'Name and description are required' }, { status: 400 })
    }

    const supabase = await createClient()
    
    // First verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('project_id')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    const suiteData: TestSuiteInsert = {
      project_id: projectId,
      name,
      description,
      type,
      tags
    }

    const { data: suite, error } = await supabase
      .from('test_suites')
      .insert(suiteData)
      .select('*')
      .single()

    if (error) {
      console.error('Error creating test suite:', error)
      return NextResponse.json({ error: 'Failed to create test suite' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: suite
    }, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/projects/[projectId]/suites:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}