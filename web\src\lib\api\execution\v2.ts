import { fetchApi } from '../core/fetch';
import type {
  V2ExecutionResponse,
  ExecuteSmokeTestOutput,
  StandardResult,
  ExecutionStatusResponse,
} from '@/lib/types';

/**
 * Execution API V2 Module (Current)
 * 
 * Modern test execution APIs with rich data extraction support.
 * These are the current recommended APIs for all test execution operations.
 * 
 * Key improvements over V1:
 * - Rich data extraction with screenshots and metadata
 * - Async execution pattern with polling support
 * - Multiple execution types (smoke, full, case, suite)
 * - Configuration profiles for different execution speeds
 * - Better error handling and status tracking
 * 
 * @example
 * ```typescript
 * // Smoke test execution
 * const smokeResult = await executeSmokeTestV2("https://example.com", "Test login flow");
 * 
 * // Test case execution
 * const caseResult = await executeTestCaseV2("test-id", "project-id", "suite-id");
 * 
 * // Suite execution with parallel support
 * const suiteResult = await executeTestSuiteV2("suite-id", "project-id", true);
 * ```
 */

// V2 Request Type Definitions
export interface V2SmokeTestRequest {
  type: "smoke";
  url: string;
  instructions?: string;
  config_profile?: string;
  max_steps?: number;
}

export interface V2FullTestRequest {
  type: "full";
  url: string;
  gherkin_scenarios: string[];
  instructions?: string;
  config_profile?: string;
}

export interface V2TestCaseRequest {
  type: "case";
  test_id: string;
  project_id?: string;
  suite_id?: string;
  execution_times?: number;
  config_profile?: string;
}

export interface V2SuiteRequest {
  type: "suite";
  suite_id: string;
  project_id?: string;
  parallel?: boolean;
  delay_between_tests?: number;
  config_profile?: string;
}

export type V2ExecutionRequest = V2SmokeTestRequest | V2FullTestRequest | V2TestCaseRequest | V2SuiteRequest;

/**
 * Core V2 test execution function
 * 
 * This is the foundation function for all V2 test executions.
 * It supports rich data extraction and async execution patterns.
 * 
 * @param request - V2 execution request with type-specific parameters
 * @returns Promise<V2ExecutionResponse> Rich execution response with metadata
 * @throws Error if execution fails or API returns error
 */
export async function executeTestV2(request: V2ExecutionRequest): Promise<V2ExecutionResponse> {
  console.log("🌐 executeTestV2 called with request:", request);

  try {
    console.log("📡 Making fetch call to /api/v2/tests/execute...");
    const response = await fetchApi<V2ExecutionResponse>('/v2/tests/execute', {
      method: 'POST',
      body: JSON.stringify(request),
    });

    console.log("📨 Raw V2 API response:", response);
    return response;

  } catch (error) {
    console.error("💥 executeTestV2 fetch failed:", error);
    throw new Error(`Failed to execute test via V2 API: ${(error as Error).message}`);
  }
}

/**
 * Execute smoke test using V2 API
 * 
 * Smoke tests are quick validation tests that check basic functionality.
 * They're ideal for rapid feedback and CI/CD pipelines.
 * 
 * @param url - Target URL to test
 * @param instructions - Optional test instructions
 * @param configProfile - Execution speed profile: "fast", "balanced", "thorough"
 * @returns Promise<V2ExecutionResponse> Execution result with screenshots and metadata
 */
export async function executeSmokeTestV2(
  url: string,
  instructions?: string,
  configProfile: string = "fast"
): Promise<V2ExecutionResponse> {
  const request: V2SmokeTestRequest = {
    type: "smoke",
    url,
    instructions,
    config_profile: configProfile
  };

  return executeTestV2(request);
}

/**
 * Execute test case using V2 API
 * 
 * Executes a specific test case with full data extraction capabilities.
 * Supports multiple execution times for load testing scenarios.
 * 
 * @param testId - Unique test case identifier
 * @param projectId - Optional project context
 * @param suiteId - Optional suite context
 * @param executionTimes - Number of times to execute (default: 1)
 * @param configProfile - Execution speed profile: "fast", "balanced", "thorough"
 * @returns Promise<V2ExecutionResponse> Detailed execution results
 */
export async function executeTestCaseV2(
  testId: string,
  projectId?: string,
  suiteId?: string,
  executionTimes: number = 1,
  configProfile: string = "balanced"
): Promise<V2ExecutionResponse> {
  const request: V2TestCaseRequest = {
    type: "case",
    test_id: testId,
    project_id: projectId,
    suite_id: suiteId,
    execution_times: executionTimes,
    config_profile: configProfile
  };

  return executeTestV2(request);
}

/**
 * Execute test suite using V2 API
 * 
 * Executes all test cases within a suite with support for parallel execution.
 * Ideal for comprehensive testing of feature sets.
 * 
 * @param suiteId - Unique suite identifier
 * @param projectId - Optional project context
 * @param parallel - Enable parallel execution of test cases
 * @param delayBetweenTests - Delay in seconds between test executions
 * @param configProfile - Execution speed profile: "fast", "balanced", "thorough"
 * @returns Promise<V2ExecutionResponse> Suite execution results with individual test results
 */
export async function executeTestSuiteV2(
  suiteId: string,
  projectId?: string,
  parallel: boolean = false,
  delayBetweenTests: number = 0.5,
  configProfile: string = "balanced"
): Promise<V2ExecutionResponse> {
  const request: V2SuiteRequest = {
    type: "suite",
    suite_id: suiteId,
    project_id: projectId,
    parallel,
    delay_between_tests: delayBetweenTests,
    config_profile: configProfile
  };

  return executeTestV2(request);
}

/**
 * Unified execution function with intelligent response handling
 * 
 * This function automatically handles both synchronous and asynchronous execution patterns.
 * It's the recommended high-level function for most use cases.
 * 
 * @param request - Any V2 execution request type
 * @returns Promise<ExecuteSmokeTestOutput | StandardResult | V2ExecutionResponse> 
 *          Returns appropriate response type based on execution pattern
 */
export async function executeTestUnified(
  request: V2ExecutionRequest
): Promise<ExecuteSmokeTestOutput | StandardResult | V2ExecutionResponse> {
  console.log("🚀 executeTestUnified called with request:", request);

  try {
    // Use V2 API only - no fallback to legacy
    const v2Result = await executeTestV2(request);
    console.log("✅ V2 API response received:", v2Result);

    // Check if this is an async execution (immediate return with RUNNING status)
    if (v2Result.success === true && v2Result.status === "Running") {
      console.log("🚀 V2 API started async execution, returning V2ExecutionResponse for polling:", v2Result);
      // Return the full V2ExecutionResponse so frontend can start polling
      return v2Result;
    }

    // Check if the execution completed successfully (legacy synchronous mode)
    if (v2Result.success === true && v2Result.status === "Completed") {
      console.log("🎉 V2 API succeeded synchronously, returning StandardResult:", v2Result.result);
      // Return the embedded result (StandardResult) with execution_id from the response
      const result = v2Result.result || {};
      // Ensure execution_id and status are always present at the top level
      const standardResult = {
        ...result,
        execution_id: v2Result.execution_id,
        status: v2Result.status,
        success: v2Result.success
      };
      return standardResult as StandardResult;
    } else {
      console.error("❌ V2 API returned unsuccessful result:", v2Result);
      // For unsuccessful results, prefer specific error message over generic ones
      const errorMessage = v2Result.error || v2Result.message || "V2 execution failed";
      // To provide more context, serialize the whole result object in the error.
      const detailedError = `${errorMessage} - Full Response: ${JSON.stringify(v2Result)}`;
      throw new Error(detailedError);
    }
  } catch (v2Error) {
    console.error("❌ V2 API failed with error:", v2Error);
    // No fallback - V2 should be fixed instead of using legacy API
    throw new Error(`Test execution failed: ${(v2Error as Error).message}`);
  }
}

/**
 * Pause a running execution
 * 
 * @param executionId - Unique execution identifier
 * @returns Promise with operation status and message
 */
export const pauseExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/execution/${executionId}/pause`, { method: 'POST' });

/**
 * Resume a paused execution
 * 
 * @param executionId - Unique execution identifier
 * @returns Promise with operation status and message
 */
export const resumeExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/execution/${executionId}/resume`, { method: 'POST' });

/**
 * Stop a running execution
 * 
 * @param executionId - Unique execution identifier
 * @returns Promise with operation status and message
 */
export const stopExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/execution/${executionId}/stop`, { method: 'POST' });

/**
 * Get execution status and results by ID
 * 
 * @param executionId - Unique execution identifier
 * @returns Promise<V2ExecutionResponse> Current execution status and results
 */
export const getExecutionById = async (executionId: string): Promise<V2ExecutionResponse> => {
  // Get status from C# backend
  const response = await fetchApi<{success: boolean, data: ExecutionStatusResponse}>(`/v2/tests/${executionId}/status`);

  // Convert ExecutionStatusResponse to V2ExecutionResponse format
  const statusData = response.data;
  return {
    success: response.success,
    execution_id: statusData.execution_id,
    status: statusData.status,
    message: statusData.message,
    result: statusData.results as StandardResult,
    error: undefined
  };
};

/**
 * Delete an execution and its associated data
 * 
 * @param executionId - Unique execution identifier
 * @returns Promise with operation status and message
 */
export const deleteExecution = (executionId: string): Promise<{ status: string; message: string }> =>
  fetchApi(`/v2/tests/executions/${executionId}`, { method: 'DELETE' });