"use client";

import { useParams } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { VariableManager } from "@/components/forms/VariableManager";
import { ProjectVariable, SuiteVariable } from "@/lib/types";
import { 
  getProjectVariables,
  getSuiteVariables,
  createProjectVariable,
  updateProjectVariable,
  deleteProjectVariable,
  createSuiteVariable,
  updateSuiteVariable,
  deleteSuiteVariable
} from "@/lib/api";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";

export default function SuiteVariablesPage() {
  const params = useParams();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;

  // Fetch project variables
  const { data: projectVariables = [], isLoading: loadingProject, error: projectError } = useQuery({
    queryKey: ['projectVariables', projectId],
    queryFn: () => getProjectVariables(projectId),
  });

  // Fetch suite variables
  const { data: suiteVariables = [], isLoading: loadingSuite, error: suiteError } = useQuery({
    queryKey: ['suiteVariables', projectId, suiteId],
    queryFn: () => getSuiteVariables(projectId, suiteId),
  });

  // Project variable mutations
  const createProjectMutation = useMutation({
    mutationFn: (variable: Omit<ProjectVariable, 'var_id' | 'project_id' | 'created_at' | 'updated_at'>) =>
      createProjectVariable(projectId, { ...variable, project_id: projectId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projectVariables', projectId] });
      toast({
        title: "Success",
        description: "Project variable created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create project variable",
        variant: "destructive",
      });
    },
  });

  const updateProjectMutation = useMutation({
    mutationFn: ({ varId, updates }: { varId: string; updates: Partial<ProjectVariable> }) =>
      updateProjectVariable(projectId, varId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projectVariables', projectId] });
      toast({
        title: "Success",
        description: "Project variable updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update project variable",
        variant: "destructive",
      });
    },
  });

  const deleteProjectMutation = useMutation({
    mutationFn: (varId: string) => deleteProjectVariable(projectId, varId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['projectVariables', projectId] });
      toast({
        title: "Success",
        description: "Project variable deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete project variable",
        variant: "destructive",
      });
    },
  });

  // Suite variable mutations
  const createSuiteMutation = useMutation({
    mutationFn: (variable: Omit<SuiteVariable, 'var_id' | 'suite_id' | 'project_id' | 'created_at' | 'updated_at'>) =>
      createSuiteVariable(projectId, suiteId, { ...variable, suite_id: suiteId, project_id: projectId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suiteVariables', projectId, suiteId] });
      toast({
        title: "Success",
        description: "Suite variable created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create suite variable",
        variant: "destructive",
      });
    },
  });

  const updateSuiteMutation = useMutation({
    mutationFn: ({ varId, updates }: { varId: string; updates: Partial<SuiteVariable> }) =>
      updateSuiteVariable(projectId, suiteId, varId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suiteVariables', projectId, suiteId] });
      toast({
        title: "Success",
        description: "Suite variable updated successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update suite variable",
        variant: "destructive",
      });
    },
  });

  const deleteSuiteMutation = useMutation({
    mutationFn: (varId: string) => deleteSuiteVariable(projectId, suiteId, varId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['suiteVariables', projectId, suiteId] });
      toast({
        title: "Success",
        description: "Suite variable deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete suite variable",
        variant: "destructive",
      });
    },
  });

  const handleProjectVariableCreate = async (variable: Omit<ProjectVariable, 'var_id' | 'project_id' | 'created_at' | 'updated_at'>) => {
    await createProjectMutation.mutateAsync(variable);
  };

  const handleProjectVariableUpdate = async (varId: string, updates: Partial<ProjectVariable>) => {
    await updateProjectMutation.mutateAsync({ varId, updates });
  };

  const handleProjectVariableDelete = async (varId: string) => {
    await deleteProjectMutation.mutateAsync(varId);
  };

  const handleSuiteVariableCreate = async (variable: Omit<SuiteVariable, 'var_id' | 'suite_id' | 'project_id' | 'created_at' | 'updated_at'>) => {
    await createSuiteMutation.mutateAsync(variable);
  };

  const handleSuiteVariableUpdate = async (varId: string, updates: Partial<SuiteVariable>) => {
    await updateSuiteMutation.mutateAsync({ varId, updates });
  };

  const handleSuiteVariableDelete = async (varId: string) => {
    await deleteSuiteMutation.mutateAsync(varId);
  };

  const isLoading = loadingProject || loadingSuite;
  const error = projectError || suiteError;

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading variables...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center text-red-600">
          <p>Error loading variables: {(error as Error).message}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suite
        </Link>
      </Button>

      <VariableManager
        projectId={projectId}
        suiteId={suiteId}
        projectVariables={projectVariables}
        suiteVariables={suiteVariables}
        onProjectVariableCreate={handleProjectVariableCreate}
        onProjectVariableUpdate={handleProjectVariableUpdate}
        onProjectVariableDelete={handleProjectVariableDelete}
        onSuiteVariableCreate={handleSuiteVariableCreate}
        onSuiteVariableUpdate={handleSuiteVariableUpdate}
        onSuiteVariableDelete={handleSuiteVariableDelete}
      />
    </div>
  );
}