"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";

import { useToast } from "@/hooks/use-toast";
import { executeTest, executeSmokeTestV2, executeTestUnified, pauseExecution, resumeExecution, stopExecution, getExecutionById } from "@/lib/api";
import { 
  ExecutionRequest, 
  ExecutionResponse as ExecutionResponseV2, 
  TestExecutionHistoryData,
  ExecutionTypeEnum,
  StandardResult,
  ExecutionStatus,
  V2ExecutionResponse
} from "@/lib/types";
import { UnifiedResultsViewer } from "@/components/execution/UnifiedResultsViewer";
import { GherkinHighlighter } from "@/components/execution/GherkinHighlighter";
import { ManualTestCasesTable } from "@/components/execution/ManualTestCasesTable";
import { Flame, Sparkles, Copy, Save } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { copyToClipboard } from "@/lib/clipboard";

const smokeTestPlaygroundSchema = z.object({
  baseUrl: z.string().url("A valid Base URL is required."),
  instructions: z.string().min(20, "Instructions must be at least 20 characters."),
  userStory: z.string().optional(),
});
type SmokeTestPlaygroundFormData = z.infer<typeof smokeTestPlaygroundSchema>;

// Type guard to check if the result is a StandardResult
const isStandardResult = (result: any): result is StandardResult => {
  return result && typeof result === 'object' && 'execution_id' in result && 'status' in result && 'test_type' in result;
};

// Type guard to check if the result is a V2ExecutionResponse (async mode)
const isV2ExecutionResponse = (result: any): result is V2ExecutionResponse => {
  return result && typeof result === 'object' && 'execution_id' in result && 'status' in result && 'success' in result && 'message' in result;
};

function ExecutionLoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Skeleton className="h-8 w-8 rounded-full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-5 w-1/3" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-3">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-16 w-full rounded-lg" />
        </div>
        <div className="space-y-3">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-16 w-full rounded-lg" />
        </div>
        <div className="space-y-3">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-16 w-full rounded-lg" />
        </div>
      </div>
      
      <div className="space-y-4">
        <Skeleton className="h-32 w-full rounded-lg" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-24" />
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
      
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-2 text-muted-foreground">
          <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-accent rounded-full animate-pulse delay-100"></div>
          <div className="w-2 h-2 bg-secondary-foreground rounded-full animate-pulse delay-200"></div>
          <span className="ml-3 text-sm">Analyzing your application...</span>
        </div>
      </div>
    </div>
  );
}


export default function SmokeTestPlaygroundPage() {
  const { toast } = useToast();
  const [executionResult, setExecutionResult] = useState<TestExecutionHistoryData | StandardResult | null>(null);
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [executionStatus, setExecutionStatus] = useState<ExecutionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [formDataForSave, setFormDataForSave] = useState<SmokeTestPlaygroundFormData | null>(null);
  const [enhancedUserStory, setEnhancedUserStory] = useState<string>("");
  const [manualTestCases, setManualTestCases] = useState<string[]>([]);


  const form = useForm<SmokeTestPlaygroundFormData>({
    resolver: zodResolver(smokeTestPlaygroundSchema),
    defaultValues: {
      baseUrl: "",
      instructions: "",
      userStory: "",
    },
  });

  const onExecuteSubmit = async (data: SmokeTestPlaygroundFormData) => {
    setIsLoading(true);
    setError(null);
    setExecutionResult(null);
    setExecutionId(null);
    setExecutionStatus(null);
    setFormDataForSave(data); // Store form data for potential save

    try {
      // Use the new V2 API with rich data extraction - now returns immediately with RUNNING status
      const result = await executeTestUnified({
        type: "smoke",
        url: data.baseUrl,
        instructions: data.instructions,
        config_profile: "fast", // Always use fast configuration
      });

      console.log("🚀 executeTestUnified immediate result:", result);

      // Check if this is the new async response format (V2ExecutionResponse with RUNNING status)
      if (isV2ExecutionResponse(result) && result.status === ExecutionStatus.RUNNING) {
        // New async mode - execution started in background
        setExecutionId(result.execution_id);
        setExecutionStatus(ExecutionStatus.RUNNING);
        console.log("🎯 Execution started in background:", result.execution_id);
        
        toast({
          title: "🚀 Test execution started!",
          description: "Test is running in the background. You can use the controls to pause, resume, or cancel.",
        });

        // Start polling for execution status
        startPollingExecution(result.execution_id);
        
      } else if (isStandardResult(result) || (result && typeof result === 'object' && 'execution_id' in result)) {
        // Legacy mode - execution completed immediately (StandardResult or similar)
        handleLegacyResult(result);
      } else {
        // Unexpected result format
        console.error("⚠️ Unexpected result format:", result);
        throw new Error("Unexpected response format from server");
      }

      // If there was a user story, create an enhanced version for editing
      if (data.userStory) {
        setEnhancedUserStory(data.userStory);
      }

      // Generate sample manual test cases based on instructions
      const instructionLines = data.instructions.split('\n').filter(line => line.trim());
      const sampleTestCases = instructionLines.map(line => {
        const cleanLine = line.replace(/^\d+\.\s*/, "");
        return cleanLine;
      });
      setManualTestCases(sampleTestCases);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setError(errorMessage);
      console.error("Smoke test execution failed:", error);
      toast({
        title: "❌ Test execution failed",
        description: errorMessage,
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  // Helper function to handle legacy execution results (for backward compatibility)
  const handleLegacyResult = (result: any) => {
    setExecutionResult(result);

    // Extract execution_id and status from the result regardless of type
    let extractedExecutionId: string | null = null;
    let extractedStatus: ExecutionStatus | null = null;

    if (isStandardResult(result)) {
      extractedExecutionId = result.execution_id;
      extractedStatus = result.status;
    } else if (result.execution_id) {
      // For results that have execution_id but aren't full StandardResult
      extractedExecutionId = result.execution_id;
      extractedStatus = result.metadata?.success ? ExecutionStatus.SUCCESS : ExecutionStatus.FAILURE;
    } else {
      // For legacy TestExecutionHistoryData without execution_id
      extractedStatus = result.metadata?.success ? ExecutionStatus.SUCCESS : ExecutionStatus.FAILURE;
    }

    // Set the execution state
    if (extractedExecutionId) {
      setExecutionId(extractedExecutionId);
      setExecutionStatus(extractedStatus || ExecutionStatus.SUCCESS);
      console.log("🎯 Execution ID extracted:", extractedExecutionId, "Status:", extractedStatus);
    } else {
      console.warn("⚠️ No execution_id found in result:", result);
      setExecutionStatus(extractedStatus || ExecutionStatus.FAILURE);
    }

    // Show success toast
    const isSuccess = isStandardResult(result) ? result.success : result.metadata?.success !== false;
    toast({
      title: "✅ Test executed successfully!",
      description: `Test completed with ${isSuccess ? 'success' : 'failure'} status.`,
    });

    setIsLoading(false);
  };

  // Function to poll execution status
  const startPollingExecution = (execId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse: V2ExecutionResponse = await getExecutionById(execId);
        console.log("📊 Polling status for", execId, ":", statusResponse.status);
        
        setExecutionStatus(statusResponse.status);
        
        // Check if execution is complete
        if ([ExecutionStatus.SUCCESS, ExecutionStatus.FAILURE, ExecutionStatus.ERROR, ExecutionStatus.CANCELLED].includes(statusResponse.status)) {
          clearInterval(pollInterval);
          setIsLoading(false);
          
          // Set the final result if available
          if (statusResponse.result) {
            setExecutionResult(statusResponse.result);
          }
          
          // Show completion toast
          const isSuccess = statusResponse.status === ExecutionStatus.SUCCESS;
          toast({
            title: isSuccess ? "✅ Test completed successfully!" : "❌ Test execution failed",
            description: `Test finished with ${statusResponse.status} status.`,
            variant: isSuccess ? "default" : "destructive"
          });
        }
        
      } catch (error) {
        console.error("Error polling execution status:", error);
        // Don't stop polling on individual errors, but limit attempts
      }
    }, 2000); // Poll every 2 seconds

    // Stop polling after 10 minutes as a safety measure
    setTimeout(() => {
      clearInterval(pollInterval);
      if (isLoading) {
        setIsLoading(false);
        toast({
          title: "⏰ Polling timeout",
          description: "Stopped checking execution status after 10 minutes.",
          variant: "destructive"
        });
      }
    }, 600000); // 10 minutes
  };

  const handlePause = async () => {
    if (!executionId) return;
    try {
      await pauseExecution(executionId);
      setExecutionStatus(ExecutionStatus.PAUSED);
      toast({ title: "Execution Paused", description: "The test execution has been paused." });
    } catch (error) {
      toast({ title: "Error", description: `Failed to pause execution: ${(error as Error).message}`, variant: "destructive" });
    }
  };

  const handleResume = async () => {
    if (!executionId) return;
    try {
      await resumeExecution(executionId);
      setExecutionStatus(ExecutionStatus.RUNNING);
      toast({ title: "Execution Resumed", description: "The test execution has been resumed." });
    } catch (error) {
      toast({ title: "Error", description: `Failed to resume execution: ${(error as Error).message}`, variant: "destructive" });
    }
  };

  const handleStop = async () => {
    if (!executionId) return;
    try {
      await stopExecution(executionId);
      setExecutionStatus(ExecutionStatus.CANCELLED);
      toast({ title: "Execution Cancelled", description: "The test execution has been cancelled." });
    } catch (error) {
      toast({ title: "Error", description: `Failed to cancel execution: ${(error as Error).message}`, variant: "destructive" });
    }
  };

  const handleCopyToClipboard = async (text: string, fieldName: string) => {
    const success = await copyToClipboard(text);
    
    if (success) {
      toast({ title: "Copied to Clipboard", description: `${fieldName} copied.` });
    } else {
      toast({ 
        title: "Copy Failed", 
        description: `Could not copy ${fieldName}. You may need to copy manually.`, 
        variant: "destructive" 
      });
    }
  };


  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="page-header flex items-center gap-3 text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
          <Flame className="h-8 w-8 text-primary" /> 
          Smoke Test Playground
        </h1>
        <p className="text-muted-foreground mt-2 text-lg">
          Execute intelligent smoke tests with AI-powered automation and real-time browser interaction
        </p>
      </div>

      <Card className="shadow-lg border-0 bg-gradient-to-br from-card via-card to-card/50">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl flex items-center gap-2">
                <Sparkles className="h-6 w-6 text-primary" />
                Configure Test Execution
              </CardTitle>
              <CardDescription className="text-base mt-2">
                Define your test parameters and let our AI agents execute comprehensive smoke tests
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-2">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onExecuteSubmit)} className="space-y-8">
              {/* Essential Configuration Section */}
              <div className="space-y-6 p-6 rounded-lg border bg-gradient-to-br from-muted/30 to-transparent">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <h3 className="text-lg font-semibold">Essential Configuration</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField control={form.control} name="baseUrl" render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="text-sm font-medium flex items-center gap-2">
                        Base URL
                        <div className="w-1.5 h-1.5 bg-destructive rounded-full" title="Required" />
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input 
                            type="url" 
                            placeholder="https://example.com" 
                            {...field} 
                            className="pr-10 focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                          />
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                            🌐
                          </div>
                        </div>
                      </FormControl>
                      <FormDescription className="text-xs">
                        The main URL where your application is hosted
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )} />


                </div>
              </div>

              {/* Test Instructions Section */}
              <div className="space-y-6 p-6 rounded-lg border bg-gradient-to-br from-accent/5 to-transparent">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-2 bg-accent rounded-full"></div>
                  <h3 className="text-lg font-semibold">Test Instructions</h3>
                </div>
                
                <FormField control={form.control} name="instructions" render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className="text-sm font-medium flex items-center gap-2">
                      Test Steps & Instructions
                      <div className="w-1.5 h-1.5 bg-destructive rounded-full" title="Required" />
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Textarea 
                          placeholder="Describe the test steps in detail:&#10;&#10;1. Navigate to the login page&#10;2. Enter credentials (<EMAIL> / password123)&#10;3. Click the login button&#10;4. Verify successful login&#10;5. Navigate to dashboard&#10;6. Verify all components load correctly"
                          {...field} 
                          className="min-h-[180px] focus:ring-2 focus:ring-primary/20 transition-all duration-200 resize-none"
                        />
                        <div className="absolute bottom-3 right-3 text-xs text-muted-foreground bg-background/80 px-2 py-1 rounded">
                          {field.value?.length || 0} / 2000 chars
                        </div>
                      </div>
                    </FormControl>
                    <FormDescription className="text-xs">
                      Be specific about actions, expected outcomes, and any test data to use
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>

              {/* Advanced Options Section */}
              <div className="space-y-6 p-6 rounded-lg border bg-gradient-to-br from-secondary/30 to-transparent">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-2 bg-secondary-foreground rounded-full"></div>
                  <h3 className="text-lg font-semibold">Advanced Options</h3>
                  <div className="ml-auto">
                    <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">Optional</span>
                  </div>
                </div>
                
                <FormField control={form.control} name="userStory" render={({ field }) => (
                  <FormItem className="space-y-3">
                    <div className="flex items-center justify-between">
                      <FormLabel className="text-sm font-medium flex items-center gap-2">
                        User Story Context
                        <div className="w-1.5 h-1.5 bg-muted-foreground rounded-full" title="Optional" />
                      </FormLabel>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="h-8 hover:bg-primary/10 hover:text-primary transition-colors"
                        onClick={() => field.value && setEnhancedUserStory(field.value)}
                        disabled={!field.value}
                      >
                        <Sparkles className="mr-2 h-4 w-4" />
                        Enhance with AI
                      </Button>
                    </div>
                    <FormControl>
                      <Textarea 
                        placeholder="As a [user type], I want to [action], so that [benefit]&#10;&#10;Example: As a new user, I want to register an account successfully, so that I can access the platform's features."
                        {...field} 
                        className="min-h-[100px] focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      Provide business context to help the AI understand the testing goals
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )} />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button 
                  type="submit" 
                  disabled={isLoading} 
                  className="flex-1 sm:flex-none h-12 px-8 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  {isLoading ? (
                    <>
                      <Sparkles className="mr-2 h-5 w-5 animate-spin" />
                      <span>Executing Test...</span>
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-5 w-5" />
                      <span>Execute Smoke Test</span>
                    </>
                  )}
                </Button>
                
                {isLoading && executionId && (
                  <div className="flex gap-2 flex-wrap">
                    {executionStatus === ExecutionStatus.RUNNING && (
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={handlePause} 
                        disabled={!executionId}
                        className="hover:bg-warning/10 hover:text-warning hover:border-warning/20 transition-colors"
                      >
                        ⏸️ Pause
                      </Button>
                    )}
                    {executionStatus === ExecutionStatus.PAUSED && (
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={handleResume} 
                        disabled={!executionId}
                        className="hover:bg-success/10 hover:text-success hover:border-success/20 transition-colors"
                      >
                        ▶️ Resume
                      </Button>
                    )}
                    {executionStatus !== ExecutionStatus.SUCCESS && 
                     executionStatus !== ExecutionStatus.FAILURE && 
                     executionStatus !== ExecutionStatus.CANCELLED && (
                      <Button 
                        type="button" 
                        variant="destructive" 
                        onClick={handleStop} 
                        disabled={!executionId}
                        className="hover:bg-destructive/90 transition-colors"
                      >
                        ⏹️ Cancel
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </form>
          </Form>

          {enhancedUserStory && (
            <Card className="mt-8 border-primary/20 bg-gradient-to-br from-primary/5 to-transparent">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <Sparkles className="h-5 w-5 text-primary"/>
                  </div>
                  <h3 className="text-lg font-semibold">AI Enhanced User Story</h3>
                </div>
                <Textarea
                  value={enhancedUserStory}
                  onChange={(e) => setEnhancedUserStory(e.target.value)}
                  className="min-h-[150px] font-mono text-sm focus:ring-2 focus:ring-primary/20 transition-all duration-200"
                  placeholder="Your enhanced user story will appear here..."
                />
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {isLoading && (
        <Card className="mt-8 border-accent/20 bg-gradient-to-br from-accent/5 to-transparent">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-accent/10 rounded-full">
                <Sparkles className="h-5 w-5 text-accent animate-spin"/>
              </div>
              <div>
                <h3 className="text-lg font-semibold">Test Execution in Progress</h3>
                <p className="text-sm text-muted-foreground">AI agents are interacting with your application...</p>
              </div>
            </div>
            <ExecutionLoadingSkeleton />
          </CardContent>
        </Card>
      )}

      {error && (
        <Alert variant="destructive" className="mt-8 border-destructive/20 bg-gradient-to-br from-destructive/5 to-transparent">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-destructive/10 rounded-full mt-0.5">
              <AlertCircle className="h-5 w-5 text-destructive" />
            </div>
            <div className="flex-1">
              <AlertTitle className="text-lg font-semibold mb-2">Execution Error</AlertTitle>
              <AlertDescription className="text-sm leading-relaxed">
                {error}
              </AlertDescription>
            </div>
          </div>
        </Alert>
      )}

      {executionResult && !isLoading && (
        <Card className="mt-8 border-success/20 bg-gradient-to-br from-success/5 to-transparent">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-success/10 rounded-full">
                  <Sparkles className="h-5 w-5 text-success"/>
                </div>
                <div>
                  <CardTitle className="text-xl">Test Execution Results</CardTitle>
                  <CardDescription>Comprehensive analysis and detailed reporting</CardDescription>
                </div>
              </div>
              <Button 
                onClick={() => setShowSaveModal(true)} 
                variant="outline"
                className="hover:bg-primary/10 hover:text-primary hover:border-primary/20 transition-colors"
              >
                <Save className="mr-2 h-4 w-4" />
                Save Test Case
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <UnifiedResultsViewer data={executionResult} />
          </CardContent>
        </Card>
      )}

      {showSaveModal && formDataForSave && executionResult && (
        <Dialog open={showSaveModal} onOpenChange={setShowSaveModal}>
          <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-hidden">
            <DialogHeader className="pb-4 border-b">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-full">
                  <Save className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <DialogTitle className="text-xl">Save Test Case Details</DialogTitle>
                  <DialogDescription className="text-base mt-1">
                    Copy the following details to manually create a new Test Case in your desired Project and Suite
                  </DialogDescription>
                </div>
              </div>
            </DialogHeader>
            
            <ScrollArea className="max-h-[60vh] p-1 pr-4">
              <div className="space-y-6 py-4">
                {/* Essential Details */}
                <div className="space-y-4 p-4 rounded-lg border bg-gradient-to-br from-muted/30 to-transparent">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Essential Details</h4>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="font-semibold flex items-center gap-2">
                        Base URL
                        <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                      </Label>
                      <div className="flex items-center gap-2">
                        <Input readOnly value={formDataForSave.baseUrl} className="font-mono text-sm bg-background/50" />
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleCopyToClipboard(formDataForSave.baseUrl, "Base URL")}
                          className="hover:bg-primary/10 hover:text-primary transition-colors"
                        >
                          <Copy className="h-4 w-4"/>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Test Instructions */}
                <div className="space-y-4 p-4 rounded-lg border bg-gradient-to-br from-accent/5 to-transparent">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Test Instructions</h4>
                  
                  <div className="space-y-2">
                    <Label className="font-semibold flex items-center gap-2">
                      Detailed Instructions
                      <div className="w-1.5 h-1.5 bg-accent rounded-full"></div>
                    </Label>
                    <div className="flex items-start gap-2">
                      <Textarea 
                        readOnly 
                        value={formDataForSave.instructions} 
                        className="font-mono text-sm min-h-[120px] bg-background/50 resize-none" 
                      />
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleCopyToClipboard(formDataForSave.instructions, "Instructions")}
                        className="hover:bg-accent/10 hover:text-accent transition-colors"
                      >
                        <Copy className="h-4 w-4"/>
                      </Button>
                    </div>
                  </div>
                </div>

                {/* User Stories Section */}
                {(formDataForSave.userStory || enhancedUserStory) && (
                  <div className="space-y-4 p-4 rounded-lg border bg-gradient-to-br from-secondary/20 to-transparent">
                    <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">User Context</h4>
                    
                    <div className="space-y-4">
                      {formDataForSave.userStory && (
                        <div className="space-y-2">
                          <Label className="font-semibold flex items-center gap-2">
                            Original User Story
                            <div className="w-1.5 h-1.5 bg-secondary-foreground rounded-full"></div>
                          </Label>
                          <div className="flex items-start gap-2">
                            <Textarea 
                              readOnly 
                              value={formDataForSave.userStory} 
                              className="font-mono text-sm min-h-[80px] bg-background/50 resize-none" 
                            />
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              onClick={() => handleCopyToClipboard(formDataForSave.userStory!, "User Story")}
                              className="hover:bg-secondary/20 transition-colors"
                            >
                              <Copy className="h-4 w-4"/>
                            </Button>
                          </div>
                        </div>
                      )}

                      {enhancedUserStory && (
                        <div className="space-y-2">
                          <Label className="font-semibold flex items-center gap-2">
                            AI Enhanced User Story
                            <Sparkles className="w-3 h-3 text-primary" />
                          </Label>
                          <div className="flex items-start gap-2">
                            <Textarea
                              value={enhancedUserStory}
                              onChange={(e) => setEnhancedUserStory(e.target.value)}
                              className="font-mono text-sm min-h-[120px] bg-background/50 resize-none"
                            />
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              onClick={() => handleCopyToClipboard(enhancedUserStory, "Enhanced User Story")}
                              className="hover:bg-primary/10 hover:text-primary transition-colors"
                            >
                              <Copy className="h-4 w-4"/>
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Generated Content */}
                <div className="space-y-4 p-4 rounded-lg border bg-gradient-to-br from-success/5 to-transparent">
                  <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Generated Test Content</h4>
                  
                  {/* Only show Gherkin for legacy format */}
                  {executionResult && 'generatedGherkin' in executionResult && executionResult.generatedGherkin && (
                    <div className="space-y-2">
                      <Label className="font-semibold flex items-center gap-2">
                        Generated Gherkin
                        <div className="w-1.5 h-1.5 bg-success rounded-full"></div>
                      </Label>
                      <div className="border rounded-lg overflow-hidden bg-background/50">
                        <GherkinHighlighter gherkinCode={executionResult.generatedGherkin} />
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label className="font-semibold flex items-center gap-2">
                      Manual Test Cases
                      <div className="w-1.5 h-1.5 bg-success rounded-full"></div>
                    </Label>
                    <div className="border rounded-lg overflow-hidden bg-background/50">
                      <ManualTestCasesTable testCases={manualTestCases.length > 0 ? manualTestCases : [
                        "Login to application with valid credentials",
                        "Navigate to dashboard",
                        "Verify all dashboard components load correctly"
                      ]} />
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-info/5 border border-info/20">
                  <div className="flex items-start gap-3">
                    <div className="p-1 bg-info/10 rounded-full mt-0.5">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-info-foreground mb-1">Reminder</p>
                      <p className="text-sm text-muted-foreground">
                        Don't forget to provide a meaningful Name, Description, and Tags when creating your Test Case in the project management interface.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ScrollArea>
            
            <DialogFooter className="pt-4 border-t">
              <DialogClose asChild>
                <Button type="button" variant="outline" className="px-8">
                  Close
                </Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
