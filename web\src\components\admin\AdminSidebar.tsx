'use client'

import { Users, Building2, Brain, Settings, BarChart3, Shield, Database, Activity, FileText, Cog, Key, Zap } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

const navigationSections = [
  {
    title: 'Panel Principal',
    items: [
      {
        name: 'Dashboard',
        href: '/admin',
        icon: BarChart3,
        description: 'Vista general del sistema'
      },
      {
        name: 'Monitoreo',
        href: '/admin/monitoring',
        icon: Activity,
        description: 'Estado y métricas'
      },
    ]
  },
  {
    title: 'Gestión de Usuarios',
    items: [
      {
        name: 'Usuarios',
        href: '/admin/users',
        icon: Users,
        description: 'Administrar usuarios'
      },
      {
        name: 'Tenants',
        href: '/admin/tenants',
        icon: Building2,
        description: 'Gestión de inquilinos'
      },
    ]
  },
  {
    title: 'Inteligencia Artificial',
    items: [
      {
        name: 'LLM Providers',
        href: '/admin/llm/providers',
        icon: Brain,
        description: 'Proveedores de IA'
      },
      {
        name: 'Modelos',
        href: '/admin/llm/models',
        icon: Zap,
        description: 'Modelos disponibles'
      },
      {
        name: 'Credenciales',
        href: '/admin/llm/credentials',
        icon: Key,
        description: 'API Keys y tokens'
      },
    ]
  },
  {
    title: 'Sistema',
    items: [
      {
        name: 'Configuración',
        href: '/admin/settings',
        icon: Cog,
        description: 'Ajustes del sistema'
      },
      {
        name: 'Logs',
        href: '/admin/logs',
        icon: FileText,
        description: 'Registros del sistema'
      },
      {
        name: 'Base de Datos',
        href: '/admin/database',
        icon: Database,
        description: 'Gestión de datos'
      },
    ]
  }
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-80 bg-gradient-to-b from-primary/10 to-primary/5 shadow-xl border-r border-border flex flex-col h-full">
      <div className="p-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-primary-foreground font-bold text-xl">Q</span>
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">QAK Admin</h1>
            <p className="text-sm text-muted-foreground mt-0.5">Panel de Administración</p>
          </div>
        </div>
      </div>
      
      <nav className="mt-2 px-4 flex-1 overflow-y-auto">
        <div className="space-y-6">
          {navigationSections.map((section, sectionIndex) => (
            <div key={section.title}>
              <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3 px-2">
                {section.title}
              </h3>
              <div className="space-y-1">
                {section.items.map((item) => {
                  const isActive = pathname === item.href || (item.href !== '/admin' && pathname.startsWith(item.href))

                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        'group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ease-in-out relative',
                        isActive
                          ? 'bg-gradient-to-r from-primary to-accent text-primary-foreground shadow-lg transform scale-[1.02]'
                          : 'text-foreground hover:bg-muted hover:text-foreground hover:transform hover:scale-[1.01]'
                      )}
                    >
                      <div className={cn(
                        'p-2 rounded-lg mr-3 transition-colors duration-200',
                        isActive
                          ? 'bg-primary-foreground/20'
                          : 'bg-muted group-hover:bg-muted/80'
                      )}>
                        <item.icon
                          className={cn(
                            'h-4 w-4 transition-colors duration-200',
                            isActive ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-foreground'
                          )}
                        />
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{item.name}</div>
                        <div className={cn(
                          'text-xs transition-colors duration-200',
                          isActive ? 'text-primary-foreground/70' : 'text-muted-foreground group-hover:text-foreground/70'
                        )}>
                          {item.description}
                        </div>
                      </div>
                      {isActive && (
                        <div className="w-1 h-8 bg-primary-foreground rounded-full absolute right-2"></div>
                      )}
                    </Link>
                  )
                })}
              </div>
            </div>
          ))}
        </div>
      </nav>
      
      <div className="mt-auto p-4">
        <div className="bg-gradient-to-r from-card to-muted/50 rounded-xl p-4 border border-border backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center shadow-lg">
                <Activity className="w-4 h-4 text-primary-foreground" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent rounded-full border-2 border-background animate-pulse"></div>
            </div>
            <div className="flex-1">
              <p className="text-sm font-semibold text-card-foreground">Sistema Operativo</p>
              <p className="text-xs text-muted-foreground">Proxy: api.qak.app ✓</p>
            </div>
          </div>
          <div className="mt-3 flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Uptime: 99.9%</span>
            <span className="text-accent font-medium">● Online</span>
          </div>
        </div>
      </div>
    </div>
  )
}