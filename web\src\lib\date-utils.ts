import { format } from 'date-fns';

/**
 * Safely formats a date value that might be null, undefined, or invalid
 * @param dateValue - The date value to format (string, Date, null, or undefined)
 * @param formatString - The format string to use (default: 'PPP p')
 * @param fallback - The fallback text to show for invalid dates (default: 'N/A')
 * @returns Formatted date string or fallback text
 */
export function formatSafeDate(
  dateValue: string | Date | null | undefined, 
  formatString: string = 'PPP p',
  fallback: string = 'N/A'
): string {
  if (!dateValue) {
    return fallback;
  }
  
  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.warn('Invalid date value:', dateValue);
      return fallback;
    }
    
    return format(date, formatString);
  } catch (error) {
    console.warn('Error formatting date:', dateValue, error);
    return fallback;
  }
}

/**
 * Safely parses a date value and returns a Date object or null
 * @param dateValue - The date value to parse
 * @returns Date object or null if invalid
 */
export function parseSafeDate(dateValue: string | Date | null | undefined): Date | null {
  if (!dateValue) {
    return null;
  }
  
  try {
    const date = dateValue instanceof Date ? dateValue : new Date(dateValue);
    
    // Check if the date is valid
    if (isNaN(date.getTime())) {
      return null;
    }
    
    return date;
  } catch (error) {
    console.warn('Error parsing date:', dateValue, error);
    return null;
  }
}

/**
 * Checks if a date value is valid
 * @param dateValue - The date value to check
 * @returns true if the date is valid, false otherwise
 */
export function isValidDate(dateValue: string | Date | null | undefined): boolean {
  return parseSafeDate(dateValue) !== null;
}
