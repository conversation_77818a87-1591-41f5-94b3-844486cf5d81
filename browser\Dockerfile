FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY pyproject.toml .
COPY browser_use_recorder/ ./browser_use_recorder/

# Install Python dependencies
RUN pip install --no-cache-dir -e .

# Copy the simplified API server and LLM providers
COPY api_server.py .
COPY llm_providers.py .

# Expose port
EXPOSE 8080

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Run the API server
CMD ["python", "api_server.py"]