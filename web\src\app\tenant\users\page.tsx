'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContextSupabase';
import { TenantService } from '@/lib/tenant';
import { Users, UserPlus, Shield, Search } from 'lucide-react';

interface TenantUser {
  id: string;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  joinedAt: string;
}

export default function TenantUsersPage() {
  const permissions = usePermissions();
  const { user } = useAuth();
  const [users, setUsers] = useState<TenantUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Verificar permisos de acceso
  if (!permissions.canManageUsersInOwnTenant()) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Shield className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-destructive mb-2">Acceso Denegado</h2>
          <p className="text-muted-foreground">No tienes permisos para gestionar usuarios del tenant.</p>
          <p className="text-sm text-muted-foreground mt-2">Rol actual: {user?.role || 'No definido'}</p>
          <p className="text-sm text-muted-foreground">Se requiere: Admin de tenant</p>
        </div>
      </div>
    );
  }

  useEffect(() => {
    loadTenantUsers();
  }, []);

  const loadTenantUsers = async () => {
    try {
      setLoading(true);
      // Aquí iría la llamada a la API para obtener usuarios del tenant
      // Por ahora, simulamos algunos datos
      const mockUsers: TenantUser[] = [
        {
          id: '1',
          username: 'usuario1',
          email: '<EMAIL>',
          role: 'Member',
          isActive: true,
          joinedAt: new Date().toISOString()
        },
        {
          id: '2',
          username: 'usuario2',
          email: '<EMAIL>',
          role: 'Admin',
          isActive: true,
          joinedAt: new Date().toISOString()
        }
      ];

      setUsers(mockUsers);
    } catch (error) {
      console.error('Error loading tenant users:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const tenantInfo = TenantService.getCurrentUserTenantInfo();

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Users className="h-6 w-6" />
          <h1 className="text-3xl font-bold">Usuarios del Tenant</h1>
        </div>
        <Button>
          <UserPlus className="h-4 w-4 mr-2" />
          Invitar Usuario
        </Button>
      </div>

      {/* Información del Tenant */}
      <Card>
        <CardHeader>
          <CardTitle>Información del Tenant</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <Label className="text-sm font-medium">Tenant ID</Label>
              <p className="text-sm text-muted-foreground">{tenantInfo.tenantId}</p>
            </div>
            <div>
              <Label className="text-sm font-medium">Tu Rol</Label>
              <p className="text-sm text-muted-foreground">
                <Badge variant="default">Admin de Tenant</Badge>
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium">Total Usuarios</Label>
              <p className="text-sm text-muted-foreground">{users.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Búsqueda y Filtros */}
      <Card>
        <CardHeader>
          <CardTitle>Buscar Usuarios</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar por nombre de usuario o email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
        </CardContent>
      </Card>

      {/* Lista de Usuarios */}
      <Card>
        <CardHeader>
          <CardTitle>Usuarios ({filteredUsers.length})</CardTitle>
          <CardDescription>
            Gestiona los usuarios que pertenecen a tu tenant
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Cargando usuarios...</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No se encontraron usuarios</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredUsers.map((tenantUser) => (
                <div key={tenantUser.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium">{tenantUser.username}</h3>
                      <Badge variant={tenantUser.role === 'Admin' ? 'default' : 'secondary'}>
                        {tenantUser.role}
                      </Badge>
                      {!tenantUser.isActive && (
                        <Badge variant="destructive">Inactivo</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">{tenantUser.email}</p>
                    <p className="text-xs text-muted-foreground">
                      Se unió: {new Date(tenantUser.joinedAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      Editar
                    </Button>
                    <Button variant="outline" size="sm">
                      {tenantUser.isActive ? 'Desactivar' : 'Activar'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}