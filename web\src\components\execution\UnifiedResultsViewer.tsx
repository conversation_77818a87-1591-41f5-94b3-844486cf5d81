"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { StandardResult, TestExecutionHistoryData } from "@/lib/types";
import { CapturesTab } from "./CapturesTab";
import { ResultsTab } from "./ResultsTab";
import { RichResultsViewer } from "./RichResultsViewer";

interface UnifiedResultsViewerProps {
  data: TestExecutionHistoryData | StandardResult | any;
  onAnalysisComplete?: () => void;
}

// Type guard to check if data is StandardResult
function isStandardResult(data: any): data is StandardResult {
  return (
    data &&
    typeof data === 'object' &&
    'execution_id' in data &&
    'test_type' in data &&
    'status' in data &&
    'steps' in data &&
    Array.isArray(data.steps) &&
    'summary' in data &&
    'artifacts' in data
  );
}

// Type guard to check if data is a wrapped StandardResult (API response format)
function isWrappedStandardResult(data: any): boolean {
  return (
    data &&
    typeof data === 'object' &&
    'result' in data &&
    data.result &&
    typeof data.result === 'object' &&
    isStandardResult(data.result)
  );
}

// Type guard to check if data is legacy TestExecutionHistoryData
function isLegacyHistoryData(data: any): data is TestExecutionHistoryData {
  return (
    data &&
    typeof data === 'object' &&
    'results' in data &&
    'metadata' in data &&
    Array.isArray(data.results)
  );
}

export function UnifiedResultsViewer({ data, onAnalysisComplete }: UnifiedResultsViewerProps) {
  // Debug logging to see what data we're getting
  console.log("UnifiedResultsViewer received data:", data);
  console.log("Data type check - isStandardResult:", isStandardResult(data));
  console.log("Data type check - isWrappedStandardResult:", isWrappedStandardResult(data));
  console.log("Data type check - isLegacyHistoryData:", isLegacyHistoryData(data));

  // Handle wrapped StandardResult (API response format)
  if (isWrappedStandardResult(data)) {
    console.log("Using RichResultsViewer with wrapped StandardResult data");
    console.log("Steps count:", data.result.steps?.length);
    console.log("Summary:", data.result.summary);
    return <RichResultsViewer result={data.result} onAnalysisComplete={onAnalysisComplete} />;
  }

  // Use new rich results viewer for direct StandardResult format
  if (isStandardResult(data)) {
    console.log("Using RichResultsViewer with direct StandardResult data");
    console.log("Steps count:", data.steps?.length);
    console.log("Summary:", data.summary);
    return <RichResultsViewer result={data} onAnalysisComplete={onAnalysisComplete} />;
  }

  // Use legacy components for old format
  if (isLegacyHistoryData(data)) {
    console.log("Using legacy components for TestExecutionHistoryData");
    return (
      <div className="space-y-6">
        <Tabs defaultValue="results" className="w-full">
          <TabsList>
            <TabsTrigger value="results">Test Results</TabsTrigger>
            <TabsTrigger value="captures">Screenshots</TabsTrigger>
          </TabsList>
          <TabsContent value="results">
            <ResultsTab historyData={data} />
          </TabsContent>
          <TabsContent value="captures">
            <CapturesTab historyData={data} />
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  // Fallback for unrecognized format
  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
      <p className="text-yellow-800">
        ⚠️ Unable to display results: Unrecognized data format
      </p>
      <div className="mt-2 text-xs text-gray-600">
        <p>Debug info:</p>
        <p>• Has 'result' key: {'result' in (data || {}) ? 'Yes' : 'No'}</p>
        <p>• Has 'execution_id' key: {'execution_id' in (data || {}) ? 'Yes' : 'No'}</p>
        <p>• Has 'results' key: {'results' in (data || {}) ? 'Yes' : 'No'}</p>
        <p>• Data type: {typeof data}</p>
        <p>• Data keys: {data && typeof data === 'object' ? Object.keys(data).join(', ') : 'N/A'}</p>
      </div>
      <details className="mt-2 cursor-pointer">
        <summary className="text-xs font-medium text-yellow-700 hover:text-yellow-800">
          Show raw data (click to expand)
        </summary>
        <pre className="mt-2 text-xs bg-yellow-100 p-2 rounded overflow-auto max-h-32 whitespace-pre-wrap break-all">
          {JSON.stringify(data, null, 2)}
        </pre>
      </details>
    </div>
  );
}