"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BookOpen, FileSearch, TestTube, ArrowRight, Database, Lightbulb, FlaskConical } from 'lucide-react';
import type { WorkflowType } from '@/app/qaki/page';

interface WorkflowSelectionProps {
  onWorkflowSelect: (workflow: WorkflowType) => void;
}

interface WorkflowOption {
  id: WorkflowType;
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  badge?: string;
  color: string;
}

const workflows: WorkflowOption[] = [
  {
    id: 'knowledge-base',
    title: 'Alimentar Knowledge Base',
    description: 'Sube documentos y contenido para mejorar la base de conocimiento del proyecto',
    icon: <Database size={24} />,
    features: [
      'Subir PDFs, Word, Markdown, TXT',
      'Indexación automática con IA',
      'Búsqueda semántica de contenido',
      'Mejora contexto para generación de tests'
    ],
    badge: 'RAG',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    id: 'user-story',
    title: 'Mejorar Historias de Usuario',
    description: 'Utiliza IA para enriquecer y mejorar historias de usuario existentes',
    icon: <Lightbulb size={24} />,
    features: [
      'Análisis y sugerencias de mejora',
      'Criterios de aceptación automáticos',
      'Detección de casos edge',
      'Contexto desde Knowledge Base'
    ],
    badge: 'AI Enhanced',
    color: 'from-purple-500 to-pink-500'
  },
  {
    id: 'test-cases',
    title: 'Generar Test Cases',
    description: 'Crea casos de prueba automatizados a partir de historias de usuario',
    icon: <FlaskConical size={24} />,
    features: [
      'Generación desde historias de usuario',
      'Test manuales y automatizados',
      'Conversión a Gherkin',
      'Export directo al proyecto'
    ],
    badge: 'Auto Export',
    color: 'from-green-500 to-emerald-500'
  }
];

export function WorkflowSelection({ onWorkflowSelect }: WorkflowSelectionProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRight size={20} />
            Elegir Flujo de Trabajo
          </CardTitle>
          <CardDescription>
            Selecciona qué acción quieres realizar con QAKI Assistant
          </CardDescription>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {workflows.map((workflow) => (
          <Card 
            key={workflow.id}
            className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] group border-2 hover:border-primary/50"
            onClick={() => onWorkflowSelect(workflow.id)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between mb-3">
                <div className={`p-3 rounded-lg bg-gradient-to-br ${workflow.color} text-white`}>
                  {workflow.icon}
                </div>
                {workflow.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {workflow.badge}
                  </Badge>
                )}
              </div>
              <CardTitle className="text-xl group-hover:text-primary transition-colors">
                {workflow.title}
              </CardTitle>
              <CardDescription className="text-sm">
                {workflow.description}
              </CardDescription>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="space-y-2 mb-4">
                {workflow.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary/60" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
              
              <Button 
                className="w-full group-hover:shadow-md transition-all"
                variant="outline"
              >
                Seleccionar
                <ArrowRight size={16} className="ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>


    </div>
  );
}