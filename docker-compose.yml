version: '3.8'

services:
  # Steel Browser API
  steel-browser:
    image: ghcr.io/steel-dev/steel-browser-api:latest
    restart: unless-stopped
    ports:
      - "3001:3000"
      - "9223:9223"
    environment:
      - DOMAIN=${STEEL_DOMAIN:-localhost:3001}
      - CDP_DOMAIN=${STEEL_CDP_DOMAIN:-localhost:9223}
    volumes:
      - steel-browser-cache:/app/.cache
    networks:
      - qak-network

  # Browser Automation API (Python)
  browser-automation-api:
    build:
      context: ./browser
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - GEMINI_MODEL=${GEMINI_MODEL:-gemini-2.5-flash}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4-turbo-preview}
      - AGENT_MAX_STEPS=${AGENT_MAX_STEPS:-8}
      - DEFAULT_LLM_PROVIDER=${DEFAULT_LLM_PROVIDER:-openai}
    depends_on:
      - steel-browser
    networks:
      - qak-network

  # Next.js Web Application
  web-app:
    build:
      context: ./web
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "3002:9001"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-}
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL:-https://qekegvxuykuuokipfple.supabase.co}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.7sFgaSFtsiwdU4uKWjkXKVNlykK62A7itHGnx81kM-I}
      - SUPABASE_URL=${SUPABASE_URL:-https://qekegvxuykuuokipfple.supabase.co}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.7sFgaSFtsiwdU4uKWjkXKVNlykK62A7itHGnx81kM-I}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFla2Vndnh1eWt1dW9raXBmcGxlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Nzk3NzY0OSwiZXhwIjoyMDczNTUzNjQ5fQ.WgHMszFAfr79TYIacsxADhXTOMIqqiGIkmR-FbKNias}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4}
      - JWT_SECRET=${JWT_SECRET:-}
      - JWT_EXPIRY=${JWT_EXPIRY:-7d}
      - NEXTAUTH_URL=${NEXTAUTH_URL:-http://localhost:3002}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-}
    depends_on:
      - browser-automation-api
    networks:
      - qak-network

volumes:
  steel-browser-cache:

networks:
  qak-network:
    driver: bridge