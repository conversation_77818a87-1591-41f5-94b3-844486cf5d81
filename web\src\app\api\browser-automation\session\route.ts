import { NextRequest } from 'next/server'
import { BrowserAutomationApiHandler } from '@/lib/api-supabase/browser-automation'

export async function POST(request: NextRequest) {
  const handler = new BrowserAutomationApiHandler()
  return handler.handleRequest(() => handler.handleCreateSession())
}

export async function DELETE(request: NextRequest) {
  const handler = new BrowserAutomationApiHandler()
  return handler.handleRequest(() => handler.handleStopSession())
}