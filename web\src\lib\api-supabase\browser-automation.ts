import { BaseApiHandler } from './base'

export interface BrowserSession {
  status: string
  message: string
  browser_id?: string
  current_url?: string
}

export interface RecordingStep {
  id: number
  timestamp: number
  type: string
  action: string
  tag?: string
  input_type?: string
  value?: string
  xpath?: string
  selectors?: string[]
  primary_selector?: string
  element_id?: string
  element_name?: string
  element_class?: string
  placeholder?: string
  aria_label?: string
  description: string
  text?: string
  click_type?: string
  modifiers?: Record<string, any>
  key?: string
  scroll_top?: number
  scroll_left?: number
  url?: string
  position?: { x: number; y: number }
  cypress_selector?: string
  is_agent_action?: boolean
  task_text?: string
  result_summary?: string
  total_steps?: number
  errors?: string[]
}

export interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp: number
  is_step_update?: boolean
  is_final_result?: boolean
  step_type?: string
  step_number?: number
  task_text?: string
  total_steps?: number
}

export class BrowserAutomationApiHandler extends BaseApiHandler {
  private readonly PYTHON_API_BASE = process.env.NODE_ENV === 'production' 
    ? 'http://browser-automation-api:8080' 
    : 'http://localhost:8080'

  private async callPythonApi<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${this.PYTHON_API_BASE}${endpoint}`
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      })

      if (!response.ok) {
        throw new Error(`Python API error: ${response.status} ${response.statusText}`)
      }

      return await response.json()
    } catch (error: any) {
      if (error.code === 'ECONNREFUSED') {
        throw new Error('Browser automation service is not running. Please start it first.')
      }
      throw error
    }
  }

  async createBrowserSession(): Promise<BrowserSession> {
    return this.callPythonApi<BrowserSession>('/api/sessions/create', {
      method: 'POST',
    })
  }

  async stopBrowserSession(): Promise<{ status: string }> {
    return this.callPythonApi<{ status: string }>('/api/sessions/stop', {
      method: 'POST',
    })
  }

  async startRecording(): Promise<{ status: string; message: string }> {
    return this.callPythonApi<{ status: string; message: string }>('/api/recording/start', {
      method: 'POST',
    })
  }

  async stopRecording(): Promise<{ status: string; message: string }> {
    return this.callPythonApi<{ status: string; message: string }>('/api/recording/stop', {
      method: 'POST',
    })
  }

  async getRecordedSteps(): Promise<{ status: string; steps: RecordingStep[] }> {
    return this.callPythonApi<{ status: string; steps: RecordingStep[] }>('/api/steps')
  }

  async getChatMessages(): Promise<{ status: string; messages: ChatMessage[] }> {
    return this.callPythonApi<{ status: string; messages: ChatMessage[] }>('/api/chat/messages')
  }

  async sendChatMessage(message: string): Promise<{ status: string }> {
    return this.callPythonApi<{ status: string }>('/api/chat/send', {
      method: 'POST',
      body: JSON.stringify({ message }),
    })
  }

  // Methods for Next.js API routes
  async handleCreateSession() {
    const result = await this.createBrowserSession()
    return this.createSuccessResponse(result)
  }

  async handleStopSession() {
    const result = await this.stopBrowserSession()
    return this.createSuccessResponse(result)
  }

  async handleStartRecording() {
    const result = await this.startRecording()
    return this.createSuccessResponse(result)
  }

  async handleStopRecording() {
    const result = await this.stopRecording()
    return this.createSuccessResponse(result)
  }

  async handleGetSteps() {
    const result = await this.getRecordedSteps()
    return this.createSuccessResponse(result.steps)
  }

  async handleGetChatMessages() {
    const result = await this.getChatMessages()
    return this.createSuccessResponse(result.messages)
  }

  async handleSendChatMessage(message: string) {
    const result = await this.sendChatMessage(message)
    return this.createSuccessResponse(result)
  }

  // Execute a test case using the browser automation
  async executeTestCase(testCase: {
    instructions: string
    user_story: string
    url: string
  }) {
    try {
      // Create browser session
      await this.createBrowserSession()
      
      // Start recording
      await this.startRecording()
      
      // Send the test case as a chat message to the agent
      const taskMessage = `Execute this test case:
URL: ${testCase.url}
User Story: ${testCase.user_story}
Instructions: ${testCase.instructions}

Please navigate to the URL and follow the instructions to complete the test case.`
      
      await this.sendChatMessage(taskMessage)
      
      return this.createSuccessResponse({
        message: 'Test case execution started',
        task: taskMessage
      })
    } catch (error: any) {
      throw new Error(`Failed to execute test case: ${error.message}`)
    }
  }

  async handleExecuteTestCase(testCase: {
    instructions: string
    user_story: string
    url: string
  }) {
    const result = await this.executeTestCase(testCase)
    return result
  }
}