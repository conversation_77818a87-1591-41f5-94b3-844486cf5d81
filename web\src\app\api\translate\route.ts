import { NextRequest, NextResponse } from 'next/server';

interface TranslationRequest {
  text: string;
  from: string;
  to: string;
  context: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: TranslationRequest = await request.json();
    const { text, from, to, context } = body;

    if (!text || !from || !to) {
      return NextResponse.json(
        { error: 'Missing required fields: text, from, to' },
        { status: 400 }
      );
    }

    // For now, we'll use a simple translation service
    // In production, you could integrate with OpenAI, Google Translate, or other services
    const translatedText = await translateText(text, from, to, context);

    return NextResponse.json({
      translatedText,
      originalText: text,
      from,
      to,
      context
    });

  } catch (error) {
    console.error('Translation error:', error);
    return NextResponse.json(
      { error: 'Translation service unavailable' },
      { status: 500 }
    );
  }
}

async function translateText(text: string, from: string, to: string, context: string): Promise<string> {
  // Check if we have OpenAI API key available
  const openAiKey = process.env.OPENAI_API_KEY;
  
  if (!openAiKey) {
    console.warn('OpenAI API key not found, using fallback translation');
    return fallbackTranslation(text, from, to);
  }

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are a professional translator specializing in software testing and browser automation prompts.

Translate the following text from ${from === 'es' ? 'Spanish' : 'English'} to ${to === 'en' ? 'English' : 'Spanish'}.

CRITICAL REQUIREMENTS:
1. Preserve ALL technical terms (CSS selectors, XPaths, element IDs, etc.)
2. Keep URLs, error codes, and technical identifiers unchanged
3. Maintain the structure and formatting of the text
4. Preserve any code snippets or technical instructions exactly
5. Only translate user-facing messages and descriptions
6. Keep placeholder variables like {scenario_text} unchanged
7. Maintain the professional and technical tone

Context: ${context}

Output only the translated text, no additional comments or explanations.`
          },
          {
            role: 'user',
            content: text
          }
        ],
        temperature: 0.1,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || fallbackTranslation(text, from, to);

  } catch (error) {
    console.error('OpenAI translation error:', error);
    return fallbackTranslation(text, from, to);
  }
}

function fallbackTranslation(text: string, from: string, to: string): string {
  // Simple fallback when AI translation is not available
  if (from === 'es' && to === 'en') {
    return `[AUTO-TRANSLATED FROM SPANISH]

${text}

[Please review and edit this translation as needed. Technical terms and placeholders have been preserved.]`;
  } else if (from === 'en' && to === 'es') {
    return `[TRADUCCIÓN AUTOMÁTICA DEL INGLÉS]

${text}

[Por favor revisa y edita esta traducción según sea necesario. Los términos técnicos y marcadores de posición se han preservado.]`;
  }
  
  return text; // Return original if unsupported language pair
}
