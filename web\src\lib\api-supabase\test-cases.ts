import { BaseApiHandler } from './base'
import type { Database } from '@/lib/supabase/database.types'

type TestCase = Database['public']['Tables']['test_cases']['Row']
type TestCaseInsert = Database['public']['Tables']['test_cases']['Insert']
type TestCaseUpdate = Database['public']['Tables']['test_cases']['Update']

export class TestCasesApiHandler extends BaseApiHandler {
  private readonly testCaseSelect = `
    *,
    test_file_attachments (
      file_id,
      file_name,
      file_size,
      content_type,
      upload_date,
      file_path
    )
  `

  async getTestCases(projectId: string, suiteId: string) {
    if (!(await this.verifyProjectOwnership(projectId))) {
      throw new Error('Project not found')
    }

    const { data: testCases, error } = await this.supabase
      .from('test_cases')
      .select(this.testCaseSelect)
      .eq('project_id', projectId)
      .eq('suite_id', suiteId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error('Failed to fetch test cases')
    }

    return this.createListResponse(testCases || [])
  }

  async getTestCase(projectId: string, suiteId: string, testCaseId: string) {
    const { data: testCase, error } = await this.supabase
      .from('test_cases')
      .select(`
        ${this.testCaseSelect},
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('id', testCaseId)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', this.user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Test case not found')
      }
      throw new Error('Failed to fetch test case')
    }

    return this.createSuccessResponse(testCase)
  }

  async createTestCase(
    projectId: string,
    suiteId: string,
    data: {
      name: string
      description: string
      instructions: string
      user_story: string
      gherkin?: string
      url: string
      tags?: string[]
      priority?: string
      type?: string
      sensitive_data?: any
      allowed_domains?: string[]
      enable_file_handling?: boolean
    }
  ) {
    if (!(await this.verifyProjectOwnership(projectId))) {
      throw new Error('Project not found')
    }

    // Verify the suite exists in the project
    const { data: suite, error: suiteError } = await this.supabase
      .from('test_suites')
      .select('suite_id')
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .single()

    if (suiteError || !suite) {
      throw new Error('Test suite not found')
    }

    const {
      name,
      description,
      instructions,
      user_story,
      gherkin,
      url,
      tags = [],
      priority = 'Medium',
      type = 'Functional',
      sensitive_data,
      allowed_domains,
      enable_file_handling
    } = data

    if (!name || !description || !instructions || !user_story || !url) {
      throw new Error('Name, description, instructions, user_story, and url are required')
    }

    const testCaseData: TestCaseInsert = {
      suite_id: suiteId,
      project_id: projectId,
      name,
      description,
      instructions,
      user_story,
      gherkin,
      url,
      tags,
      priority: priority as any,
      type: type as any,
      sensitive_data,
      allowed_domains,
      enable_file_handling
    }

    const { data: testCase, error } = await this.supabase
      .from('test_cases')
      .insert(testCaseData)
      .select(this.testCaseSelect)
      .single()

    if (error) {
      throw new Error('Failed to create test case')
    }

    return this.createSuccessResponse(testCase, 201)
  }

  async updateTestCase(
    projectId: string,
    suiteId: string,
    testCaseId: string,
    data: {
      name?: string
      description?: string
      instructions?: string
      user_story?: string
      gherkin?: string
      url?: string
      tags?: string[]
      status?: string
      priority?: string
      type?: string
      last_result?: string
      error_message?: string
      sensitive_data?: any
      allowed_domains?: string[]
      code?: string
      framework?: string
      enable_file_handling?: boolean
    }
  ) {
    // Verify ownership through the test case-project relationship
    const { data: existingTestCase, error: checkError } = await this.supabase
      .from('test_cases')
      .select(`
        id,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('id', testCaseId)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', this.user.id)
      .single()

    if (checkError || !existingTestCase) {
      throw new Error('Test case not found')
    }

    const updateData: TestCaseUpdate = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.instructions !== undefined) updateData.instructions = data.instructions
    if (data.user_story !== undefined) updateData.user_story = data.user_story
    if (data.gherkin !== undefined) updateData.gherkin = data.gherkin
    if (data.url !== undefined) updateData.url = data.url
    if (data.tags !== undefined) updateData.tags = data.tags
    if (data.status !== undefined) updateData.status = data.status as any
    if (data.priority !== undefined) updateData.priority = data.priority as any
    if (data.type !== undefined) updateData.type = data.type as any
    if (data.last_result !== undefined) updateData.last_result = data.last_result
    if (data.error_message !== undefined) updateData.error_message = data.error_message
    if (data.sensitive_data !== undefined) updateData.sensitive_data = data.sensitive_data
    if (data.allowed_domains !== undefined) updateData.allowed_domains = data.allowed_domains
    if (data.code !== undefined) updateData.code = data.code
    if (data.framework !== undefined) updateData.framework = data.framework
    if (data.enable_file_handling !== undefined) updateData.enable_file_handling = data.enable_file_handling

    // Update last_execution if status is being updated to a completed state
    if (data.status && ['Passed', 'Failed', 'Blocked', 'Skipped'].includes(data.status)) {
      updateData.last_execution = new Date().toISOString()
    }

    const { data: testCase, error } = await this.supabase
      .from('test_cases')
      .update(updateData)
      .eq('id', testCaseId)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .select(this.testCaseSelect)
      .single()

    if (error) {
      throw new Error('Failed to update test case')
    }

    return this.createSuccessResponse(testCase)
  }

  async deleteTestCase(projectId: string, suiteId: string, testCaseId: string) {
    // Verify ownership through the test case-project relationship
    const { data: existingTestCase, error: checkError } = await this.supabase
      .from('test_cases')
      .select(`
        id,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('id', testCaseId)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', this.user.id)
      .single()

    if (checkError || !existingTestCase) {
      throw new Error('Test case not found')
    }

    const { error } = await this.supabase
      .from('test_cases')
      .delete()
      .eq('id', testCaseId)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)

    if (error) {
      throw new Error('Failed to delete test case')
    }

    return this.createSuccessResponse({ message: 'Test case deleted successfully' })
  }
}