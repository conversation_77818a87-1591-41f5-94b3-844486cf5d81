import { AuthService } from './auth';

/**
 * Utilidades para manejo de tenants
 */
export class TenantService {
  /**
   * Obtiene el tenantId del usuario actualmente autenticado
   */
  static getCurrentTenantId(): string | null {
    const user = AuthService.getUser();
    return user?.tenantId || null;
  }

  /**
   * Verifica si el usuario tiene acceso a un tenant específico
   */
  static validateTenantAccess(tenantId: string): boolean {
    const user = AuthService.getUser();
    if (!user) return false;

    // SuperAdmin puede acceder a cualquier tenant
    if (user.role === 'SuperAdmin') return true;

    // Admin puede acceder a cualquier tenant
    if (user.role === 'Admin') return true;

    // Otros usuarios solo pueden acceder a su propio tenant
    return user.tenantId === tenantId;
  }

  /**
   * Obtiene el tenantId a usar (del usuario o fallback)
   */
  static getTenantIdForRequest(fallbackTenantId?: string): string {
    const userTenantId = this.getCurrentTenantId();
    return userTenantId || fallbackTenantId || 'default-tenant';
  }

  /**
   * Verifica si el usuario pertenece a un tenant específico
   */
  static userBelongsToTenant(tenantId: string): boolean {
    const user = AuthService.getUser();
    return user?.tenantId === tenantId;
  }

  /**
   * Obtiene información del tenant del usuario actual
   */
  static getCurrentUserTenantInfo() {
    const user = AuthService.getUser();
    return {
      tenantId: user?.tenantId,
      hasTenant: !!user?.tenantId,
      isSuperAdmin: user?.role === 'SuperAdmin',
      isAdmin: user?.role === 'Admin',
      isTenantUser: user?.role === 'User' && !!user?.tenantId
    };
  }
}