"use client";
import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { useQueryClient } from "@tanstack/react-query";
import { importProject } from "@/lib/api";

export function ImportProjectBar() {
  const [file, setFile] = useState<File | null>(null);
  const queryClient = useQueryClient();
  const inputRef = useRef<HTMLInputElement>(null);

  const handleImport = async () => {
    if (!file) return;
    try {
      const data = await importProject(file);
      if (data.success) {
        alert("Project imported");
        queryClient.invalidateQueries({ queryKey: ["projects"] });
        setFile(null);
      } else {
        alert(data.error || "Import failed");
      }
    } catch (e) {
      console.error(e);
      alert("Unexpected error importing project");
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-4 mb-6">
      {/* Hidden real input */}
      <input
        ref={inputRef}
        type="file"
        accept="application/json"
        onChange={(e) => setFile(e.target.files?.[0] || null)}
        className="hidden"
      />

      <Button
        variant="secondary"
        size="sm"
        onClick={() => inputRef.current?.click()}
      >
        {file ? "Change File" : "Choose File"}
      </Button>

      <span className="text-sm text-muted-foreground truncate max-w-[12rem]">
        {file ? file.name : "No file chosen"}
      </span>

      <Button size="sm" onClick={handleImport} disabled={!file}>
        Import Project
      </Button>
    </div>
  );
} 