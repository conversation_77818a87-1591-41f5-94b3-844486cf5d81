"use client";

import { useParams } from "next/navigation";
import ProjectVariablesManager from "@/components/projects/ProjectVariablesManager";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function ProjectVariablesPage() {
  const params = useParams();
  const projectId = params.projectId as string;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/projects/${projectId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Link>
        </Button>
        
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Project Variables</h1>
          <p className="text-muted-foreground">
            Manage global variables that will be inherited by all environments
          </p>
        </div>
      </div>

      <ProjectVariablesManager projectId={projectId} />
    </div>
  );
}