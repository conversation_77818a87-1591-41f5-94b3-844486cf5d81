"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Copy } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { copyToClipboard } from "@/lib/clipboard";

interface GherkinHighlighterProps {
  gherkinCode: string;
}

export function GherkinHighlighter({ gherkinCode }: GherkinHighlighterProps) {
  const { toast } = useToast();
  const [highlightedGherkin, setHighlightedGherkin] = useState<string>(gherkinCode);
  
  useEffect(() => {
    // Apply syntax highlighting to the Gherkin code - only highlight keywords, not entire lines
    let highlighted = gherkinCode;
    
    // Highlight English keywords
    highlighted = highlighted.replace(/\b(Feature):/g, '<span class="text-primary font-bold">$1</span>:');
    highlighted = highlighted.replace(/\b(<PERSON><PERSON><PERSON>):/g, '<span class="text-violet-400 dark:text-violet-300 font-bold">$1</span>:');
    highlighted = highlighted.replace(/\b(Scenario Outline):/g, '<span class="text-fuchsia-400 font-bold">$1</span>:');
    highlighted = highlighted.replace(/\b(Given)\s/g, '<span class="text-emerald-500 dark:text-emerald-300 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(When)\s/g, '<span class="text-amber-500 dark:text-amber-300 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(Then)\s/g, '<span class="text-rose-500 dark:text-rose-300 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(And)\s/g, '<span class="text-sky-400 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(But)\s/g, '<span class="text-sky-400 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(Examples):/g, '<span class="text-indigo-400 font-bold">$1</span>:');
    
    // Highlight Spanish keywords
    highlighted = highlighted.replace(/\b(Característica):/g, '<span class="text-primary font-bold">$1</span>:');
    highlighted = highlighted.replace(/\b(Escenario):/g, '<span class="text-violet-400 dark:text-violet-300 font-bold">$1</span>:');
    highlighted = highlighted.replace(/\b(Esquema del escenario):/g, '<span class="text-fuchsia-400 font-bold">$1</span>:');
    highlighted = highlighted.replace(/\b(Dado)\s/g, '<span class="text-emerald-500 dark:text-emerald-300 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(Cuando)\s/g, '<span class="text-amber-500 dark:text-amber-300 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(Entonces)\s/g, '<span class="text-rose-500 dark:text-rose-300 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(Y)\s/g, '<span class="text-sky-400 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(Pero)\s/g, '<span class="text-sky-400 font-semibold">$1</span> ');
    highlighted = highlighted.replace(/\b(Ejemplos):/g, '<span class="text-indigo-400 font-bold">$1</span>:');
    
    setHighlightedGherkin(highlighted);
  }, [gherkinCode]);

  const handleCopyToClipboard = async () => {
    // Copy the raw Gherkin code (without HTML)
    const success = await copyToClipboard(gherkinCode);
    
    if (success) {
      toast({ title: "Copied to Clipboard", description: "Gherkin copied." });
    } else {
      toast({ 
        title: "Copy Failed", 
        description: "Could not copy Gherkin. You may need to copy manually.", 
        variant: "destructive" 
      });
    }
  };

  return (
    <div className="mt-1 flex items-start gap-2">
      <ScrollArea className="h-[300px] w-full rounded-md border bg-muted/30">
        <div 
          className="p-4 font-mono text-sm whitespace-pre-wrap"
          dangerouslySetInnerHTML={{ __html: highlightedGherkin }}
        />
      </ScrollArea>
      <Button variant="ghost" size="icon" onClick={handleCopyToClipboard}>
        <Copy className="h-4 w-4" />
      </Button>
    </div>
  );
}
