"use client";

import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import {
  Play,
  Square,
  RotateCcw,
  Globe,
  Eye,
  Monitor,
  Smartphone,
  Tablet,
  Settings,
  RefreshCw,
  ArrowLeft,
  ArrowRight,
  Home,
  Search,
  X
} from "lucide-react";

interface BrowserSession {
  id: string;
  url: string;
  title?: string;
  status: 'active' | 'inactive';
  viewport?: {
    width: number;
    height: number;
  };
}

interface PageInfo {
  url: string;
  title: string;
  loading: boolean;
}

export default function SteelBrowserPage() {
  const [sessions, setSessions] = useState<BrowserSession[]>([]);
  const [activeSession, setActiveSession] = useState<BrowserSession | null>(null);
  const [pageInfo, setPageInfo] = useState<PageInfo | null>(null);
  const [urlInput, setUrlInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [viewportSize, setViewportSize] = useState({ width: 1920, height: 1080 });
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { toast } = useToast();

  // API base URL for Steel Browser
  const STEEL_API_BASE = process.env.NEXT_PUBLIC_STEEL_API_BASE_URL ||
    (process.env.NODE_ENV === 'production'
      ? 'http://steel-browser-api:3000'
      : 'http://localhost:3001');

  // Load sessions on component mount
  useEffect(() => {
    loadSessions();
  }, []);

  // Update page info when active session changes
  useEffect(() => {
    if (activeSession) {
      updatePageInfo();
    }
  }, [activeSession]);

  const loadSessions = async () => {
    try {
      const response = await fetch(`${STEEL_API_BASE}/sessions`);
      if (response.ok) {
        const data = await response.json();
        setSessions(data.sessions || []);
      }
    } catch (error) {
      console.error('Error loading sessions:', error);
    }
  };

  const createSession = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${STEEL_API_BASE}/sessions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          viewport: viewportSize
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create session');
      }

      const session = await response.json();
      setActiveSession(session);
      setSessions(prev => [...prev, session]);

      toast({
        title: "Session Created",
        description: "New browser session started",
      });
    } catch (error) {
      console.error('Error creating session:', error);
      toast({
        title: "Error",
        description: "Failed to create browser session",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const closeSession = async (sessionId: string) => {
    try {
      const response = await fetch(`${STEEL_API_BASE}/sessions/${sessionId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setSessions(prev => prev.filter(s => s.id !== sessionId));
        if (activeSession?.id === sessionId) {
          setActiveSession(null);
          setPageInfo(null);
        }

        toast({
          title: "Session Closed",
          description: "Browser session ended",
        });
      }
    } catch (error) {
      console.error('Error closing session:', error);
      toast({
        title: "Error",
        description: "Failed to close session",
        variant: "destructive",
      });
    }
  };

  const navigateToUrl = async (url: string) => {
    if (!activeSession) return;

    try {
      setIsLoading(true);
      const fullUrl = url.startsWith('http') ? url : `https://${url}`;

      const response = await fetch(`${STEEL_API_BASE}/sessions/${activeSession.id}/navigate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: fullUrl }),
      });

      if (!response.ok) {
        throw new Error('Failed to navigate');
      }

      setUrlInput(fullUrl);
      await updatePageInfo();

      toast({
        title: "Navigation Complete",
        description: `Navigated to ${fullUrl}`,
      });
    } catch (error) {
      console.error('Error navigating:', error);
      toast({
        title: "Navigation Error",
        description: "Failed to navigate to URL",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updatePageInfo = async () => {
    if (!activeSession) return;

    try {
      const response = await fetch(`${STEEL_API_BASE}/sessions/${activeSession.id}/info`);
      if (response.ok) {
        const info = await response.json();
        setPageInfo(info);
        setUrlInput(info.url || "");
      }
    } catch (error) {
      console.error('Error getting page info:', error);
    }
  };

  const refreshPage = async () => {
    if (!activeSession) return;

    try {
      const response = await fetch(`${STEEL_API_BASE}/sessions/${activeSession.id}/refresh`, {
        method: 'POST',
      });

      if (response.ok) {
        await updatePageInfo();
        toast({
          title: "Page Refreshed",
          description: "Current page has been reloaded",
        });
      }
    } catch (error) {
      console.error('Error refreshing page:', error);
      toast({
        title: "Error",
        description: "Failed to refresh page",
        variant: "destructive",
      });
    }
  };

  const goBack = async () => {
    if (!activeSession) return;

    try {
      const response = await fetch(`${STEEL_API_BASE}/sessions/${activeSession.id}/back`, {
        method: 'POST',
      });

      if (response.ok) {
        await updatePageInfo();
      }
    } catch (error) {
      console.error('Error going back:', error);
    }
  };

  const goForward = async () => {
    if (!activeSession) return;

    try {
      const response = await fetch(`${STEEL_API_BASE}/sessions/${activeSession.id}/forward`, {
        method: 'POST',
      });

      if (response.ok) {
        await updatePageInfo();
      }
    } catch (error) {
      console.error('Error going forward:', error);
    }
  };

  const setViewport = async (width: number, height: number) => {
    if (!activeSession) return;

    try {
      const response = await fetch(`${STEEL_API_BASE}/sessions/${activeSession.id}/viewport`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ width, height }),
      });

      if (response.ok) {
        setViewportSize({ width, height });
        setActiveSession(prev => prev ? { ...prev, viewport: { width, height } } : null);
        toast({
          title: "Viewport Updated",
          description: `Set to ${width}x${height}`,
        });
      }
    } catch (error) {
      console.error('Error setting viewport:', error);
      toast({
        title: "Error",
        description: "Failed to update viewport",
        variant: "destructive",
      });
    }
  };

  const getViewportIcon = () => {
    if (viewportSize.width <= 768) return <Smartphone className="w-4 h-4" />;
    if (viewportSize.width <= 1024) return <Tablet className="w-4 h-4" />;
    return <Monitor className="w-4 h-4" />;
  };

  const getViewportLabel = () => {
    if (viewportSize.width <= 768) return "Mobile";
    if (viewportSize.width <= 1024) return "Tablet";
    return "Desktop";
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Steel Browser</h1>
          <p className="text-muted-foreground">
            Control and interact with web pages using Steel Browser
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={activeSession ? "default" : "secondary"}>
            {activeSession ? "Session Active" : "No Session"}
          </Badge>
          {activeSession && (
            <Badge variant="outline" className="flex items-center gap-1">
              {getViewportIcon()}
              {getViewportLabel()} ({viewportSize.width}x{viewportSize.height})
            </Badge>
          )}
        </div>
      </div>

      {/* Session Management */}
      <Card>
        <CardHeader>
          <CardTitle>Browser Sessions</CardTitle>
          <CardDescription>
            Create and manage browser sessions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 flex-wrap">
            {!activeSession ? (
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="viewport-width">Width:</Label>
                  <Input
                    id="viewport-width"
                    type="number"
                    value={viewportSize.width}
                    onChange={(e) => setViewportSize(prev => ({ ...prev, width: parseInt(e.target.value) || 1920 }))}
                    className="w-20"
                  />
                  <Label htmlFor="viewport-height">Height:</Label>
                  <Input
                    id="viewport-height"
                    type="number"
                    value={viewportSize.height}
                    onChange={(e) => setViewportSize(prev => ({ ...prev, height: parseInt(e.target.value) || 1080 }))}
                    className="w-20"
                  />
                </div>
                <Button
                  onClick={createSession}
                  disabled={isLoading}
                  className="flex items-center gap-2"
                >
                  <Play className="w-4 h-4" />
                  Create Session
                </Button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => closeSession(activeSession.id)}
                  variant="destructive"
                  className="flex items-center gap-2"
                >
                  <Square className="w-4 h-4" />
                  Close Session
                </Button>
              </div>
            )}

            {/* Quick Viewport Presets */}
            {activeSession && (
              <div className="flex items-center gap-2 ml-4">
                <Label className="text-sm">Presets:</Label>
                <Button
                  onClick={() => setViewport(1920, 1080)}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Monitor className="w-3 h-3" />
                  Desktop
                </Button>
                <Button
                  onClick={() => setViewport(768, 1024)}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Tablet className="w-3 h-3" />
                  Tablet
                </Button>
                <Button
                  onClick={() => setViewport(375, 667)}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Smartphone className="w-3 h-3" />
                  Mobile
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Browser Controls */}
      {activeSession && (
        <Card>
          <CardHeader>
            <CardTitle>Browser Controls</CardTitle>
            <CardDescription>
              Navigate and control the browser session
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 flex-wrap">
              {/* Navigation Controls */}
              <div className="flex items-center gap-1">
                <Button
                  onClick={goBack}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back
                </Button>
                <Button
                  onClick={goForward}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <ArrowRight className="w-4 h-4" />
                  Forward
                </Button>
                <Button
                  onClick={refreshPage}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </Button>
              </div>

              {/* URL Bar */}
              <div className="flex-1 min-w-0 flex items-center gap-2">
                <div className="flex items-center gap-1 flex-1">
                  <Globe className="w-4 h-4 text-muted-foreground" />
                  <Input
                    value={urlInput}
                    onChange={(e) => setUrlInput(e.target.value)}
                    placeholder="Enter URL..."
                    onKeyPress={(e) => e.key === 'Enter' && navigateToUrl(urlInput)}
                    className="flex-1"
                  />
                  <Button
                    onClick={() => navigateToUrl(urlInput)}
                    disabled={isLoading}
                    size="sm"
                  >
                    <Search className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Page Info */}
              {pageInfo && (
                <div className="text-sm text-muted-foreground">
                  {pageInfo.loading && <span>Loading...</span>}
                  {pageInfo.title && (
                    <span className="truncate max-w-xs block" title={pageInfo.title}>
                      {pageInfo.title}
                    </span>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Browser Viewport */}
      {activeSession && (
        <Card className="flex flex-col">
          <CardHeader>
            <CardTitle>Browser Viewport</CardTitle>
            <CardDescription>
              Live browser session - {viewportSize.width}x{viewportSize.height}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex-1">
            <div className="relative bg-gray-100 border rounded-lg overflow-hidden">
              <div className="w-full h-[600px] border-0 bg-white flex items-center justify-center">
                <div className="text-center">
                  <Monitor className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold mb-2">Browser Viewport</h3>
                  <p className="text-muted-foreground mb-4">
                    Interactive browser session active
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Use browser controls above to navigate
                  </p>
                  {pageInfo && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <p className="font-medium">{pageInfo.title || 'Loading...'}</p>
                      <p className="text-sm text-muted-foreground break-all">{pageInfo.url}</p>
                    </div>
                  )}
                </div>
              </div>
              {isLoading && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                  <div className="flex items-center gap-2">
                    <RefreshCw className="w-6 h-6 animate-spin" />
                    <span>Loading...</span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sessions List */}
      {sessions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Active Sessions</CardTitle>
            <CardDescription>
              {sessions.length} session{sessions.length !== 1 ? 's' : ''} available
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    activeSession?.id === session.id
                      ? 'bg-primary/10 border-primary'
                      : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Eye className="w-4 h-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium text-sm">
                        Session {session.id.slice(-8)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {session.url || 'No URL'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={session.status === 'active' ? 'default' : 'secondary'}>
                      {session.status}
                    </Badge>
                    <Button
                      onClick={() => setActiveSession(session)}
                      variant={activeSession?.id === session.id ? 'default' : 'outline'}
                      size="sm"
                    >
                      {activeSession?.id === session.id ? 'Active' : 'Switch'}
                    </Button>
                    <Button
                      onClick={() => closeSession(session.id)}
                      variant="destructive"
                      size="sm"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}