import { Page } from '@playwright/test';

/**
 * Helper function to login to the application
 * This is a mock implementation - you'll need to adjust based on your actual auth system
 */
export async function login(page: Page, email = '<EMAIL>', password = 'password123') {
  // Navigate to login page
  await page.goto('/login');
  
  // Wait for the login form to be visible
  await page.waitForSelector('[data-testid="email"], input[type="email"], input[name="email"]');
  
  // Fill login form (adjust selectors based on your actual login form)
  const emailInput = page.locator('[data-testid="email"], input[type="email"], input[name="email"]').first();
  const passwordInput = page.locator('[data-testid="password"], input[type="password"], input[name="password"]').first();
  const loginButton = page.locator('[data-testid="login-button"], button[type="submit"], button:has-text("Login"), button:has-text("Sign In")').first();
  
  await emailInput.fill(email);
  await passwordInput.fill(password);
  await loginButton.click();
  
  // Wait for redirect after successful login (could be /projects or /admin)
  await page.waitForURL(/\/(projects|admin)/, { timeout: 30000 });
}

/**
 * Helper function to logout from the application
 */
export async function logout(page: Page) {
  // Look for logout button or user menu
  const logoutButton = page.locator('[data-testid="logout"], button:has-text("Logout"), button:has-text("Sign Out")').first();
  
  if (await logoutButton.isVisible()) {
    await logoutButton.click();
  } else {
    // Try to find user menu first
    const userMenu = page.locator('[data-testid="user-menu"], [aria-label="User menu"]').first();
    if (await userMenu.isVisible()) {
      await userMenu.click();
      await page.locator('button:has-text("Logout"), button:has-text("Sign Out")').first().click();
    }
  }
  
  // Wait for redirect to login page
  await page.waitForURL('/login', { timeout: 5000 });
}

/**
 * Helper function to check if user is logged in
 */
export async function isLoggedIn(page: Page): Promise<boolean> {
  try {
    // Check if we're on an admin page or if auth token exists
    const currentUrl = page.url();
    if (currentUrl.includes('/admin')) {
      return true;
    }
    
    // Check for auth token in localStorage
    const token = await page.evaluate(() => localStorage.getItem('qak_auth_token'));
    return !!token;
  } catch {
    return false;
  }
}

/**
 * Helper function to ensure user is logged in before running tests
 */
export async function ensureLoggedIn(page: Page) {
  if (!(await isLoggedIn(page))) {
    await login(page);
  }
}