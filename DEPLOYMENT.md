# 🚀 Guía de Despliegue en Producción - QAK

## 📋 Índice
1. [Prerrequisitos](#prerrequisitos)
2. [Configuración de Supabase Cloud](#configuración-de-supabase-cloud)
3. [Configuración de Variables de Entorno](#configuración-de-variables-de-entorno)
4. [Configuración de SSL/TLS](#configuración-de-ssltls)
5. [Despliegue con Docker](#despliegue-con-docker)
6. [Configuración de Nginx](#configuración-de-nginx)
7. [Monitoreo y Logs](#monitoreo-y-logs)
8. [Troubleshooting](#troubleshooting)

## 🔧 Prerrequisitos

### Servidor
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM**: Mínimo 4GB, Recomendado 8GB+
- **CPU**: Mínimo 2 cores, Recomendado 4+ cores
- **Almacenamiento**: Mínimo 50GB SSD
- **Docker**: v20.10+
- **Docker Compose**: v2.0+

### Servicios Externos
- **Supabase Cloud**: Proyecto configurado (ID: `qekegvxuykuuokipfple`)
- **Dominio**: Configurado con DNS apuntando al servidor
- **SSL Certificate**: Let's Encrypt o certificado comercial
- **API Keys**: OpenAI y/o Gemini

## 🗄️ Configuración de Supabase Cloud

### 1. Verificar Configuración del Proyecto
```bash
# ID del proyecto: qekegvxuykuuokipfple
# URL: https://qekegvxuykuuokipfple.supabase.co
```

### 2. Configurar Authentication
1. Ir a **Authentication > Settings**
2. Configurar **Site URL**: `https://tu-dominio.com`
3. Agregar **Redirect URLs**:
   - `https://tu-dominio.com/auth/callback`
   - `https://tu-dominio.com/auth/callback/google`

### 3. Configurar Google OAuth
1. Ir a **Authentication > Providers > Google**
2. Habilitar Google provider
3. Configurar Client ID y Client Secret de Google Cloud Console

### 4. Configurar RLS (Row Level Security)
```sql
-- Ejecutar en SQL Editor de Supabase
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE test_results ENABLE ROW LEVEL SECURITY;

-- Políticas de ejemplo (ajustar según necesidades)
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);
```

## 🔐 Configuración de Variables de Entorno

### 1. Copiar archivo de producción
```bash
cp .env.prod .env
```

### 2. Configurar variables críticas
```bash
# Editar .env con valores reales
nano .env
```

**Variables OBLIGATORIAS a cambiar:**
- `NEXTAUTH_URL`: Tu dominio real
- `NEXTAUTH_SECRET`: Generar con `openssl rand -base64 32`
- `GOOGLE_CLIENT_ID`: De Google Cloud Console
- `GOOGLE_CLIENT_SECRET`: De Google Cloud Console
- `OPENAI_API_KEY`: Tu API key de OpenAI
- `STEEL_DOMAIN`: Tu dominio real
- `STEEL_CDP_DOMAIN`: Tu dominio real

## 🔒 Configuración de SSL/TLS

### Opción 1: Let's Encrypt (Recomendado)
```bash
# Instalar Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Obtener certificado
sudo certbot --nginx -d tu-dominio.com

# Copiar certificados para Docker
sudo cp /etc/letsencrypt/live/tu-dominio.com/fullchain.pem ./nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/tu-dominio.com/privkey.pem ./nginx/ssl/key.pem
sudo chown $USER:$USER ./nginx/ssl/*
```

### Opción 2: Certificado Comercial
```bash
# Copiar certificados al directorio nginx/ssl/
mkdir -p nginx/ssl
cp tu-certificado.pem nginx/ssl/cert.pem
cp tu-clave-privada.key nginx/ssl/key.pem
```

## 🐳 Despliegue con Docker

### 1. Preparar el entorno
```bash
# Clonar repositorio
git clone <tu-repositorio>
cd persoqak

# Configurar variables de entorno
cp .env.prod .env
# Editar .env con valores reales

# Crear directorios necesarios
mkdir -p nginx/ssl
mkdir -p logs
```

### 2. Construir y ejecutar
```bash
# Construir imágenes
docker-compose -f docker-compose.prod.yml build

# Ejecutar en producción
docker-compose -f docker-compose.prod.yml up -d

# Verificar estado
docker-compose -f docker-compose.prod.yml ps
```

### 3. Verificar logs
```bash
# Ver logs de todos los servicios
docker-compose -f docker-compose.prod.yml logs -f

# Ver logs específicos
docker-compose -f docker-compose.prod.yml logs -f web-app
docker-compose -f docker-compose.prod.yml logs -f nginx
```

## 🌐 Configuración de Nginx

### 1. Actualizar configuración
```bash
# Editar nginx/nginx.conf
nano nginx/nginx.conf

# Cambiar 'tu-dominio.com' por tu dominio real
sed -i 's/tu-dominio.com/tudominio.com/g' nginx/nginx.conf
```

### 2. Reiniciar Nginx
```bash
docker-compose -f docker-compose.prod.yml restart nginx
```

## 📊 Monitoreo y Logs

### 1. Health Checks
```bash
# Verificar salud de servicios
curl https://tu-dominio.com/health
curl https://tu-dominio.com/api/health
```

### 2. Monitoreo de recursos
```bash
# Ver uso de recursos
docker stats

# Ver logs en tiempo real
docker-compose -f docker-compose.prod.yml logs -f --tail=100
```

### 3. Configurar logrotate
```bash
# Crear configuración de logrotate
sudo tee /etc/logrotate.d/docker-qak << EOF
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
EOF
```

## 🔧 Scripts de Automatización

### 1. Script de despliegue
```bash
# Usar el script creado
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 2. Script de backup
```bash
chmod +x scripts/backup.sh
./scripts/backup.sh
```

### 3. Configurar cron jobs
```bash
# Editar crontab
crontab -e

# Agregar backup diario a las 2 AM
0 2 * * * /ruta/al/proyecto/scripts/backup.sh

# Renovar certificados SSL mensualmente
0 3 1 * * certbot renew --quiet && docker-compose -f /ruta/al/proyecto/docker-compose.prod.yml restart nginx
```

## 🚨 Troubleshooting

### Problemas Comunes

#### 1. Error de conexión a Supabase
```bash
# Verificar variables de entorno
docker-compose -f docker-compose.prod.yml exec web-app env | grep SUPABASE

# Verificar conectividad
docker-compose -f docker-compose.prod.yml exec web-app curl -I https://qekegvxuykuuokipfple.supabase.co
```

#### 2. Error de SSL
```bash
# Verificar certificados
openssl x509 -in nginx/ssl/cert.pem -text -noout

# Verificar configuración de Nginx
docker-compose -f docker-compose.prod.yml exec nginx nginx -t
```

#### 3. Error de autenticación Google
```bash
# Verificar configuración OAuth
# 1. Verificar Client ID/Secret en .env
# 2. Verificar URLs de redirect en Google Console
# 3. Verificar configuración en Supabase
```

#### 4. Alto uso de memoria
```bash
# Verificar uso de recursos
docker stats

# Ajustar límites en docker-compose.prod.yml
# Reiniciar servicios problemáticos
docker-compose -f docker-compose.prod.yml restart <servicio>
```

### Comandos Útiles

```bash
# Reiniciar todos los servicios
docker-compose -f docker-compose.prod.yml restart

# Actualizar una imagen específica
docker-compose -f docker-compose.prod.yml pull web-app
docker-compose -f docker-compose.prod.yml up -d web-app

# Limpiar recursos no utilizados
docker system prune -f

# Ver logs de errores
docker-compose -f docker-compose.prod.yml logs --tail=50 | grep -i error
```

## 📞 Soporte

Para problemas adicionales:
1. Revisar logs detallados
2. Verificar configuración de variables de entorno
3. Comprobar conectividad de red
4. Validar certificados SSL
5. Verificar configuración de Supabase

## 🔄 Actualizaciones

### Proceso de actualización
```bash
# 1. Backup
./scripts/backup.sh

# 2. Pull cambios
git pull origin main

# 3. Rebuild y deploy
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d

# 4. Verificar
curl https://tu-dominio.com/health
```

---

**⚠️ Importante**: Siempre realizar backups antes de actualizaciones en producción.