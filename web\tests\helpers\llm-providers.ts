import { Page, expect } from '@playwright/test';

/**
 * Mock data for LLM provider testing
 */
export const mockProviders = {
  openai: {
    name: 'Test OpenAI Provider',
    type: 'openai',
    description: 'Test OpenAI provider for E2E testing',
    baseUrl: 'https://api.openai.com/v1',
    rateLimits: {
      requestsPerMinute: 100,
      tokensPerMinute: 10000
    }
  },
  anthropic: {
    name: 'Test Anthropic Provider',
    type: 'anthropic',
    description: 'Test Anthropic provider for E2E testing',
    baseUrl: 'https://api.anthropic.com',
    rateLimits: {
      requestsPerMinute: 50,
      tokensPerMinute: 5000
    }
  },
  google: {
    name: 'Test Google Provider',
    type: 'google',
    description: 'Test Google provider for E2E testing',
    baseUrl: 'https://generativelanguage.googleapis.com/v1',
    rateLimits: {
      requestsPerMinute: 60,
      tokensPerMinute: 6000
    }
  }
};

/**
 * Helper function to navigate to LLM providers page
 */
export async function navigateToProvidersPage(page: Page) {
  await page.goto('/admin/llm/providers');
  await page.waitForLoadState('networkidle');
  
  // Verify we're on the correct page
  await expect(page.locator('h1').filter({ hasText: 'LLM Providers' })).toBeVisible();
}

/**
 * Helper function to open the create provider dialog
 */
export async function openCreateProviderDialog(page: Page) {
  await page.click('button:has-text("Add Provider")');
  await expect(page.locator('[role="dialog"]')).toBeVisible();
}

/**
 * Helper function to fill the provider form
 */
export async function fillProviderForm(page: Page, provider: any) {
  // Fill basic information
  await page.fill('#name', provider.name);
  await page.click('[data-testid="type-select"], [role="combobox"]');
  await page.click(`[role="option"]:has-text("${provider.type}")`);
  if (provider.description) {
    await page.fill('#description', provider.description);
  }
  if (provider.baseUrl) {
    await page.fill('#baseUrl', provider.baseUrl);
  }
  
  // Add default headers if provided
  if (provider.defaultHeaders && Object.keys(provider.defaultHeaders).length > 0) {
    const headers = Object.entries(provider.defaultHeaders);
    for (const [key, value] of headers) {
      // Fill header name and value
      await page.fill('input[placeholder="Header name"]', key);
      await page.fill('input[placeholder="Header value"]', value as string);
      
      // Click Add button (use force to bypass overlay issues)
      await page.click('button:has-text("Add")', { force: true });
    }
  }
  
  // Set rate limits if provided
  if (provider.rateLimits) {
    if (provider.rateLimits.requestsPerMinute) {
      await page.fill('#requestsPerMinute', provider.rateLimits.requestsPerMinute.toString());
    }
    if (provider.rateLimits.tokensPerMinute) {
      await page.fill('#tokensPerMinute', provider.rateLimits.tokensPerMinute.toString());
    }
  }
  
  // Set active status if provided
  if (provider.isActive !== undefined) {
    const checkbox = page.locator('[name="isActive"]');
    if (provider.isActive) {
      await checkbox.check();
    } else {
      await checkbox.uncheck();
    }
  }
}

/**
 * Helper function to create a provider
 */
export async function createProvider(page: Page, provider: any) {
  await openCreateProviderDialog(page);
  await fillProviderForm(page, provider);
  await page.click('button:has-text("Create Provider")');
  
  // Wait for success message
  await expect(page.locator('text=LLM provider created successfully')).toBeVisible();
  
  // Verify the provider appears in the table
  await expect(page.locator(`text=${provider.name}`)).toBeVisible();
}

/**
 * Helper function to edit a provider
 */
export async function editProvider(page: Page, providerName: string, updatedProvider: any) {
  // Find and click the edit button for the provider
  const providerRow = page.locator(`tr:has-text("${providerName}")`);
  await providerRow.locator('button[title="Edit"]').click();
  
  // Wait for edit dialog to open
  await expect(page.locator('[role="dialog"]')).toBeVisible();
  
  // Clear existing values and fill with new ones
  await page.fill('[name="name"]', '');
  await page.fill('[name="description"]', '');
  await page.fill('[name="baseUrl"]', '');
  
  await fillProviderForm(page, updatedProvider);
  await page.click('button:has-text("Update Provider")');
  
  // Wait for success message
  await expect(page.locator('text=LLM provider updated successfully')).toBeVisible();
}

/**
 * Helper function to delete a provider
 */
export async function deleteProvider(page: Page, providerName: string) {
  // Find and click the delete button for the provider
  const providerRow = page.locator(`tr:has-text("${providerName}")`);
  await providerRow.locator('button[title="Delete"]').click();
  
  // Wait for delete confirmation dialog
  await expect(page.locator('[role="dialog"]')).toBeVisible();
  await expect(page.locator('text=Are you sure you want to delete this provider?')).toBeVisible();
  
  // Confirm deletion
  await page.click('button:has-text("Delete Provider")');
  
  // Wait for success message
  await expect(page.locator('text=LLM provider deleted successfully')).toBeVisible();
  
  // Verify the provider is no longer in the table
  await expect(page.locator(`text=${providerName}`)).not.toBeVisible();
}

/**
 * Helper function to search for providers
 */
export async function searchProviders(page: Page, searchTerm: string) {
  const searchInput = page.locator('[placeholder*="Search"]');
  await searchInput.fill(searchTerm);
  
  // Wait for search results to update
  await page.waitForTimeout(500);
}

/**
 * Helper function to clear search
 */
export async function clearSearch(page: Page) {
  const searchInput = page.locator('[placeholder*="Search"]');
  await searchInput.clear();
  
  // Wait for results to update
  await page.waitForTimeout(500);
}

/**
 * Helper function to verify provider in table
 */
export async function verifyProviderInTable(page: Page, provider: any, shouldExist = true) {
  const providerRow = page.locator(`tr:has-text("${provider.name}")`);
  
  if (shouldExist) {
    await expect(providerRow).toBeVisible();
    await expect(providerRow.locator(`text=${provider.type}`)).toBeVisible();
    if (provider.description) {
      await expect(providerRow.locator(`text=${provider.description}`)).toBeVisible();
    }
  } else {
    await expect(providerRow).not.toBeVisible();
  }
}

/**
 * Helper function to wait for table to load
 */
export async function waitForTableLoad(page: Page) {
  await page.waitForSelector('table');
  await page.waitForLoadState('networkidle');
}

/**
 * Helper function to get provider count from table
 */
export async function getProviderCount(page: Page): Promise<number> {
  await waitForTableLoad(page);
  const rows = page.locator('tbody tr');
  return await rows.count();
}