import { fetchApi } from '../core/fetch';

/**
 * Input interface for generating Gherkin scenarios
 */
export interface GenerateGherkinInput {
  instructions?: string | string[];
  userStory?: string;
  url?: string;
  language: string;
}

/**
 * Output interface for Gherkin generation
 */
export interface GenerateGherkinOutput {
  gherkin: string;
}

/**
 * Input interface for code generation
 */
export interface GenerateCodeInput {
  framework: string;
  gherkin_scenario: string;
  test_history?: any;
}

/**
 * Output interface for code generation
 */
export interface GenerateCodeOutput {
  code: string;
}

/**
 * Input interface for test result summarization
 */
export interface SummarizeTestResultsInput {
  testResults: any;
}

/**
 * Output interface for test result summarization
 */
export interface SummarizeTestResultsOutput {
  summary: string;
}

/**
 * Generates Gherkin scenarios from manual test cases or user stories
 * 
 * @deprecated Use executeTest with Gherkin-based execution instead
 * @param input - The input containing instructions or user story and language preference
 * @returns Promise resolving to Gherkin scenario
 * 
 * @example
 * ```typescript
 * // From manual test cases
 * const gherkin = await generateGherkin({
 *   instructions: ["Test login with valid credentials", "Test login with invalid credentials"],
 *   language: "es"
 * });
 * 
 * // From user story
 * const gherkin = await generateGherkin({
 *   userStory: "As a user I want to login",
 *   url: "https://app.example.com/login",
 *   language: "es"
 * });
 * ```
 */
export async function generateGherkin(input: GenerateGherkinInput): Promise<GenerateGherkinOutput> {
  // For generating Gherkin from manual test cases
  if (input.instructions) {
    try {
      // Using the documented endpoint for generating Gherkin from manual tests
      const apiInput = {
        manual_tests: Array.isArray(input.instructions)
          ? input.instructions.join('\n')
          : input.instructions,
        language: input.language
      };

      const result = await fetchApi<any>('/stories/generate-gherkin', {
        method: 'POST',
        body: JSON.stringify(apiInput),
      });

      // Convert API response to our expected output format
      return {
        gherkin: result.gherkin || result.gherkin_scenario || '',
      };
    } catch (error) {
      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);
    }
  }
  // For generating Gherkin directly from instructions and URL
  else {
    try {
      // Using the documented endpoint for generating Gherkin directly
      const apiInput = {
        instructions: input.userStory || '',
        url: input.url || '',
        user_story: input.userStory || '',
        language: input.language
      };

      const result = await fetchApi<any>('/generate/gherkin', {
        method: 'POST',
        body: JSON.stringify(apiInput),
      });

      // Convert API response to our expected output format
      return {
        gherkin: result.gherkin || result.gherkin_scenario || '',
      };
    } catch (error) {
      throw new Error(`Failed to generate Gherkin: ${(error as Error).message}`);
    }
  }
}

/**
 * Generates automated test code from Gherkin scenarios
 * 
 * @param input - The framework, Gherkin scenario and optional test history
 * @returns Promise resolving to generated test code
 * 
 * @example
 * ```typescript
 * const codeResult = await generateCode({
 *   framework: "playwright",
 *   gherkin_scenario: "Given I am on the login page...",
 *   test_history: { previousRuns: [] }
 * });
 * console.log(codeResult.code);
 * ```
 */
export async function generateCode(input: GenerateCodeInput): Promise<GenerateCodeOutput> {
  try {
    const apiInput = {
      framework: input.framework,
      gherkin_scenario: input.gherkin_scenario,
      test_history: input.test_history || {}
    };

    const result = await fetchApi<any>('/generate/code', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    return {
      code: result.code || '',
    };
  } catch (error) {
    let errorMessage = `Failed to generate code: ${(error as Error).message}`;
    throw new Error(errorMessage);
  }
}

/**
 * Summarizes test execution results using AI analysis
 * 
 * @param input - The test results to summarize
 * @returns Promise resolving to test summary
 * 
 * @example
 * ```typescript
 * const summary = await summarizeTestResults({
 *   testResults: {
 *     passed: 5,
 *     failed: 2,
 *     errors: ["Login failed", "Timeout on page load"]
 *   }
 * });
 * console.log(summary.summary);
 * ```
 */
export async function summarizeTestResults(input: SummarizeTestResultsInput): Promise<SummarizeTestResultsOutput> {
  // Converting our input to match the API schema
  const apiInput = {
    test_results: input.testResults
  };

  try {
    const result = await fetchApi<any>('/tests/summarize', {
      method: 'POST',
      body: JSON.stringify(apiInput),
    });

    return {
      summary: result.summary || '',
    };
  } catch (error) {
    throw new Error(`Failed to summarize test results: ${(error as Error).message}`);
  }
}