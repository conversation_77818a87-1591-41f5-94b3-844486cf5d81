import { fetchApi } from '../core/fetch';
import type {
  TestSuite,
  TestSuiteCreateInput,
  TestSuiteUpdateInput,
} from '@/lib/types';

/**
 * Test Suites API Module
 * 
 * Provides CRUD operations for test suite management including:
 * - Suite creation, retrieval, update, and deletion within projects
 * - Backend response mapping from C# to TypeScript types
 * - Handling of nested project-suite relationships
 * 
 * @example
 * ```typescript
 * // Get all suites for a project
 * const suites = await getSuitesByProjectId("project-uuid");
 * 
 * // Create new suite
 * const newSuite = await createSuite("project-uuid", {
 *   name: "Authentication Tests",
 *   description: "Tests for user authentication flow"
 * });
 * 
 * // Get specific suite
 * const suite = await getSuiteById("project-uuid", "suite-uuid");
 * ```
 */

/**
 * Retrieves all test suites for a specific project
 * 
 * Handles multiple backend response formats and maps C# DTO to frontend types.
 * 
 * @param projectId - Unique project identifier
 * @returns Promise<TestSuite[]> Array of mapped test suites
 * @throws Error if the request fails or project not found
 */
export const getSuitesByProjectId = async (projectId: string): Promise<TestSuite[]> => {
  try {
    const response = await fetchApi<any>(`/projects/${projectId}/suites`);
    
    // Handle the response format from C# controller
    let suites: any[] = [];
    if (response && typeof response === 'object') {
      // Check if it has items property (expected format from controller)
      if (response.items && Array.isArray(response.items)) {
        suites = response.items;
      }
      // Fallback: check if it's a BaseResponse with Data
      else if (response.Data && Array.isArray(response.Data)) {
        suites = response.Data;
      }
      // Fallback: check if response is directly an array
      else if (Array.isArray(response)) {
        suites = response;
      }
      else {
        console.warn('Unexpected suites response format:', response);
        return [];
      }
    }

    // Map C# backend format to frontend format
    const mappedSuites = suites.map(suite => ({
      suite_id: suite.suite_id || suite.id || suite.Id || '',
      project_id: suite.project_id || suite.projectId || suite.ProjectId || projectId,
      name: suite.name || suite.Name || '',
      description: suite.description || suite.Description || '',
      type: (suite.type || suite.Type || 'manual') as 'manual' | 'github' | 'automated',
      tags: suite.tags || suite.Tags || [],
      created_at: suite.created_at || suite.createdAt || suite.CreatedAt || new Date().toISOString(),
      updated_at: suite.updated_at || suite.updatedAt || suite.UpdatedAt || new Date().toISOString(),
      execution_times: suite.execution_times || suite.executionTimes || suite.ExecutionTimes || 1,
      test_cases: suite.test_cases || suite.testCases || suite.TestCases || []
    }));

    return mappedSuites;
  } catch (error) {
    console.error('Error in getSuitesByProjectId:', error);
    throw error;
  }
};

/**
 * Creates a new test suite within a project
 * 
 * @param projectId - Unique project identifier
 * @param data - Test suite creation data
 * @returns Promise<TestSuite> Created test suite with backend-generated fields
 * @throws Error if creation fails or project not found
 */
export const createSuite = async (projectId: string, data: TestSuiteCreateInput): Promise<TestSuite> => {
  const suite = await fetchApi<any>(`/projects/${projectId}/suites`, { method: 'POST', body: JSON.stringify(data) });

  // Map C# backend format to frontend format
  return {
    suite_id: suite.id || suite.Id || '',
    project_id: suite.projectId || suite.ProjectId || projectId,
    name: suite.name || suite.Name || '',
    description: suite.description || suite.Description || '',
    type: 'manual' as const, // Default type
    tags: suite.tags || suite.Tags || [],
    created_at: suite.createdAt || suite.CreatedAt || new Date().toISOString(),
    updated_at: suite.updatedAt || suite.UpdatedAt || new Date().toISOString(),
    execution_times: suite.executionTimes || suite.ExecutionTimes || 1,
    test_cases: suite.testCases || suite.TestCases || []
  };
};

/**
 * Retrieves a specific test suite by ID
 * 
 * Note: C# backend doesn't have individual suite endpoint, so this fetches
 * all suites and filters for the specific one.
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @returns Promise<TestSuite> Mapped test suite data
 * @throws Error if suite not found or request fails
 */
export const getSuiteById = async (projectId: string, suiteId: string): Promise<TestSuite> => {
  // C# backend doesn't have individual suite endpoint, so get from suites list
  const response = await fetchApi<{ count: number; items: any[] }>(`/projects/${projectId}/suites`);

  if (!response.items || !Array.isArray(response.items)) {
    throw new Error(`Invalid response format from suites API for project ${projectId}`);
  }

  // Find the specific suite in the list - search using all possible ID field names
  const suite = response.items.find(s => 
    s.suite_id === suiteId || 
    s.id === suiteId || 
    s.Id === suiteId
  );

  if (!suite) {
    console.error('Suite not found. Available suites:', response.items.map(s => ({
      suite_id: s.suite_id,
      id: s.id,
      Id: s.Id,
      name: s.name || s.Name
    })));
    throw new Error(`Suite with ID ${suiteId} not found in project ${projectId}`);
  }

  // Map C# backend format to frontend format
  return {
    suite_id: suite.suite_id || suite.id || suite.Id || suiteId,
    project_id: suite.project_id || suite.projectId || suite.ProjectId || projectId,
    name: suite.name || suite.Name || '',
    description: suite.description || suite.Description || '',
    type: 'manual' as const, // Default type
    tags: suite.tags || suite.Tags || [],
    created_at: suite.created_at || suite.createdAt || suite.CreatedAt || new Date().toISOString(),
    updated_at: suite.updated_at || suite.updatedAt || suite.UpdatedAt || new Date().toISOString(),
    execution_times: suite.execution_times || suite.executionTimes || suite.ExecutionTimes || 1,
    test_cases: suite.test_cases || suite.testCases || suite.TestCases || []
  };
};

/**
 * Updates an existing test suite
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @param data - Updated test suite data
 * @returns Promise<TestSuite> Updated test suite
 * @throws Error Currently not implemented in C# backend
 */
export const updateSuite = async (projectId: string, suiteId: string, data: TestSuiteUpdateInput): Promise<TestSuite> => {
  // C# backend doesn't have PUT endpoint for individual suites yet
  // This is a temporary implementation that throws an informative error
  throw new Error(`Suite update functionality is not yet implemented in the C# backend. The PUT /projects/${projectId}/suites/${suiteId} endpoint does not exist. Attempted to update suite ${suiteId} with data: ${JSON.stringify(data)}. Please contact the development team to implement this endpoint.`);
};

/**
 * Deletes a test suite
 * 
 * @param projectId - Unique project identifier
 * @param suiteId - Unique suite identifier
 * @returns Promise<void>
 * @throws Error if deletion fails or suite not found
 */
export const deleteSuite = (projectId: string, suiteId: string): Promise<void> => 
  fetchApi<void>(`/projects/${projectId}/suites/${suiteId}`, { method: 'DELETE' });