'use client'

import * as React from 'react'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Building,
  Users,
  Calendar,
  DollarSign,
  Settings,
  CheckCircle
} from 'lucide-react'
import { API_BASE_URL } from '@/lib/config'
import { useToast } from '@/hooks/use-toast'
import { getGlobalTenants, createGlobalTenant, updateGlobalTenant, deleteGlobalTenant, toggleGlobalTenantStatus } from '@/lib/api'

interface Tenant {
  id: string
  name: string
  subdomain?: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt?: string
  subscription?: {
    plan: string
    status: 'active' | 'inactive' | 'trial' | 'expired'
    startDate: string
    endDate?: string
    maxUsers: number
    maxProjects: number
  }
  settings?: {
    allowRegistration: boolean
    requireEmailVerification: boolean
    customDomain?: string
    logoUrl?: string
    primaryColor?: string
  }
  stats?: {
    totalUsers: number
    totalProjects: number
    totalTests: number
    storageUsed: number
  }
}

interface TenantFilters {
  searchTerm: string
  subscriptionStatus?: 'active' | 'inactive' | 'trial' | 'expired'
  isActive?: boolean
}

interface CreateTenantRequest {
  name: string
  subdomain?: string
  description?: string
  subscriptionPlan: string
  maxUsers: number
  maxProjects: number
}

export default function TenantsPage() {
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<TenantFilters>({ searchTerm: '' })
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingTenant, setEditingTenant] = useState<Tenant | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize] = useState(20)
  const [totalTenants, setTotalTenants] = useState(0)
  const { toast } = useToast()

  useEffect(() => {
    fetchTenants()
  }, [page, filters])

  const fetchTenants = async () => {
    try {
      setLoading(true)
      
      // Convert filters to the format expected by getGlobalTenants
      const searchTerm = filters.searchTerm || undefined
      const status = filters.isActive !== undefined ? (filters.isActive ? 'active' : 'inactive') : undefined
      
      const data = await getGlobalTenants(page, pageSize, searchTerm, status) as any
      
      // Based on the API response structure: { data: [...], totalCount: 4, ... }
      setTenants(data.data || [])
      setTotalTenants(data.totalCount || 0)
    } catch (error) {
      console.error('Error fetching tenants:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch tenants',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTenant = async (tenantData: CreateTenantRequest) => {
    try {
      await createGlobalTenant(tenantData)
      
      toast({
        title: 'Success',
        description: 'Tenant created successfully',
      })
      
      setShowCreateDialog(false)
      fetchTenants()
    } catch (error) {
      console.error('Error creating tenant:', error)
      toast({
        title: 'Error',
        description: 'Failed to create tenant',
        variant: 'destructive',
      })
    }
  }

  const handleUpdateTenant = async (tenantId: string, updateData: Partial<Tenant>) => {
    try {
      await updateGlobalTenant(tenantId, updateData)
      
      toast({
        title: 'Success',
        description: 'Tenant updated successfully',
      })
      
      setEditingTenant(null)
      fetchTenants()
    } catch (error) {
      console.error('Error updating tenant:', error)
      toast({
        title: 'Error',
        description: 'Failed to update tenant',
        variant: 'destructive',
      })
    }
  }

  const handleToggleTenantStatus = async (tenantId: string, isActive: boolean) => {
    try {
      await toggleGlobalTenantStatus(tenantId, isActive)
      
      toast({
        title: 'Success',
        description: `Tenant ${isActive ? 'deactivated' : 'activated'} successfully`,
      })
      
      fetchTenants()
    } catch (error) {
      console.error('Error toggling tenant status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update tenant status',
        variant: 'destructive',
      })
    }
  }

  const getSubscriptionBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'default'
      case 'trial':
        return 'secondary'
      case 'expired':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  const formatStorage = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Building className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Tenants</h1>
              <p className="text-muted-foreground">
                Gestiona organizaciones de tenants y sus suscripciones
              </p>
            </div>
          </div>

          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground">
                <Plus className="mr-2 h-4 w-4" />
                Crear Tenant
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Crear Nuevo Tenant</DialogTitle>
                <DialogDescription>
                  Agregar una nueva organización tenant al sistema
                </DialogDescription>
              </DialogHeader>
              <CreateTenantForm onSubmit={handleCreateTenant} />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary">Total Tenants</p>
                <p className="text-3xl font-bold text-foreground">{totalTenants}</p>
              </div>
              <Building className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-accent/5 to-accent/10 border-accent/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-accent">Activos</p>
                <p className="text-3xl font-bold text-foreground">{tenants.filter(t => t.isActive).length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-accent" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-secondary/50 to-secondary/30 border-secondary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-secondary-foreground">Suscripciones</p>
                <p className="text-3xl font-bold text-foreground">{tenants.filter(t => t.subscription?.status === 'active').length}</p>
              </div>
              <DollarSign className="h-8 w-8 text-secondary-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-muted/50 to-muted/30 border-muted/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Planes Únicos</p>
                <p className="text-3xl font-bold text-foreground">{[...new Set(tenants.map(t => t.subscription?.plan || 'No Plan'))].length}</p>
              </div>
              <Calendar className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-card border-border">
        <CardHeader className="bg-gradient-to-r from-muted/20 to-muted/10">
          <CardTitle className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Gestión de Tenants</CardTitle>
          <CardDescription className="text-muted-foreground">
            Visualiza y administra todas las organizaciones de tenants
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar tenants..."
                value={filters.searchTerm}
                onChange={(e) => setFilters({ ...filters, searchTerm: e.target.value })}
                className="pl-8 bg-background border-border"
              />
            </div>
            
            <Select
              value={filters.subscriptionStatus || 'all'}
              onValueChange={(value) => setFilters({ ...filters, subscriptionStatus: value === 'all' ? undefined : value as any })}
            >
              <SelectTrigger className="w-[160px] border-primary/20">
                <SelectValue placeholder="Suscripción" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los planes</SelectItem>
                <SelectItem value="active">Activo</SelectItem>
                <SelectItem value="trial">Prueba</SelectItem>
                <SelectItem value="expired">Expirado</SelectItem>
                <SelectItem value="inactive">Inactivo</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.isActive?.toString() || 'all'}
              onValueChange={(value) => setFilters({
                ...filters,
                isActive: value === 'all' ? undefined : value === 'true'
              })}
            >
              <SelectTrigger className="w-[120px] border-primary/20">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="true">Activo</SelectItem>
                <SelectItem value="false">Inactivo</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex flex-col items-center gap-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary border-t-transparent"></div>
                <p className="text-sm text-muted-foreground">Cargando tenants...</p>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/30 hover:bg-muted/30">
                  <TableHead className="text-foreground">Tenant</TableHead>
                  <TableHead className="text-foreground">Suscripción</TableHead>
                  <TableHead className="text-foreground">Uso</TableHead>
                  <TableHead className="text-foreground">Creado</TableHead>
                  <TableHead className="text-foreground">Estado</TableHead>
                  <TableHead className="text-foreground text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tenants.map((tenant, index) => (
                  <TableRow key={tenant.id} className={`${index % 2 === 0 ? 'bg-card' : 'bg-muted/20'} hover:bg-primary/5 transition-colors`}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Building className="h-8 w-8 p-1 bg-primary/10 rounded-full text-primary" />
                        <div>
                          <div className="font-medium text-foreground">{tenant.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {tenant.subdomain && `${tenant.subdomain}.qak.app`}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <Badge variant={getSubscriptionBadgeVariant(tenant.subscription?.status || 'inactive')} className="bg-primary/10 text-primary border-primary/20">
                          {tenant.subscription?.plan || 'Sin Plan'}
                        </Badge>
                        <div className="text-xs text-muted-foreground">
                          {tenant.subscription?.maxUsers} usuarios • {tenant.subscription?.maxProjects} proyectos
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-foreground">
                          <Users className="mr-1 h-3 w-3 text-accent" />
                          {tenant.stats?.totalUsers || 0} usuarios
                        </div>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <DollarSign className="mr-1 h-3 w-3 text-secondary-foreground" />
                          {formatStorage(tenant.stats?.storageUsed || 0)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-foreground">
                        <Calendar className="mr-1 h-3 w-3 text-muted-foreground" />
                        {formatDate(tenant.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={tenant.isActive ? 'default' : 'secondary'} className={tenant.isActive ? 'bg-accent/10 text-accent border-accent/20' : 'bg-destructive/10 text-destructive border-destructive/20'}>
                        {tenant.isActive ? 'Activo' : 'Inactivo'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingTenant(tenant)}
                          className="border-primary/20 text-primary hover:bg-primary/5"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleTenantStatus(tenant.id, tenant.isActive)}
                          className="border-accent/20 text-accent hover:bg-accent/5"
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          <div className="flex items-center justify-between mt-4 bg-gradient-to-r from-muted/20 to-muted/10 rounded-lg p-4">
            <div className="text-sm text-muted-foreground">
              Mostrando {tenants.length} de {totalTenants} tenants
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page <= 1}
                className="border-primary/20 text-primary hover:bg-primary/5"
              >
                Anterior
              </Button>
              <span className="text-sm text-foreground px-3 py-1 bg-card border border-border rounded-md">{page}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={tenants.length < pageSize}
                className="border-primary/20 text-primary hover:bg-primary/5"
              >
                Siguiente
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {editingTenant && (
        <Dialog open={!!editingTenant} onOpenChange={() => setEditingTenant(null)}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Edit Tenant</DialogTitle>
              <DialogDescription>
                Update tenant information and settings
              </DialogDescription>
            </DialogHeader>
            <EditTenantForm 
              tenant={editingTenant} 
              onSubmit={(data) => handleUpdateTenant(editingTenant.id, data)}
              onCancel={() => setEditingTenant(null)}
            />
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

function CreateTenantForm({ onSubmit }: { onSubmit: (data: CreateTenantRequest) => void }) {
  const [formData, setFormData] = useState<CreateTenantRequest>({
    name: '',
    subdomain: '',
    description: '',
    subscriptionPlan: 'basic',
    maxUsers: 10,
    maxProjects: 5,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Tenant Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="subdomain">Subdomain</Label>
          <Input
            id="subdomain"
            value={formData.subdomain}
            onChange={(e) => setFormData({ ...formData, subdomain: e.target.value })}
            placeholder="company-name"
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Brief description of the tenant organization"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="subscriptionPlan">Subscription Plan</Label>
        <Select
          value={formData.subscriptionPlan}
          onValueChange={(value) => setFormData({ ...formData, subscriptionPlan: value })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="trial">Trial (7 days)</SelectItem>
            <SelectItem value="basic">Basic</SelectItem>
            <SelectItem value="pro">Pro</SelectItem>
            <SelectItem value="enterprise">Enterprise</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="maxUsers">Max Users</Label>
          <Input
            id="maxUsers"
            type="number"
            value={formData.maxUsers}
            onChange={(e) => setFormData({ ...formData, maxUsers: parseInt(e.target.value) || 0 })}
            min={1}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="maxProjects">Max Projects</Label>
          <Input
            id="maxProjects"
            type="number"
            value={formData.maxProjects}
            onChange={(e) => setFormData({ ...formData, maxProjects: parseInt(e.target.value) || 0 })}
            min={1}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="submit">Create Tenant</Button>
      </div>
    </form>
  )
}

function EditTenantForm({ 
  tenant, 
  onSubmit, 
  onCancel 
}: { 
  tenant: Tenant
  onSubmit: (data: Partial<Tenant>) => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    name: tenant.name,
    subdomain: tenant.subdomain || '',
    description: tenant.description || '',
    isActive: tenant.isActive,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-name">Tenant Name</Label>
          <Input
            id="edit-name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-subdomain">Subdomain</Label>
          <Input
            id="edit-subdomain"
            value={formData.subdomain}
            onChange={(e) => setFormData({ ...formData, subdomain: e.target.value })}
          />
        </div>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="edit-description">Description</Label>
        <Textarea
          id="edit-description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        />
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="edit-isActive"
          checked={formData.isActive}
          onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
          className="rounded"
        />
        <Label htmlFor="edit-isActive">Active</Label>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Update Tenant</Button>
      </div>
    </form>
  )
}