'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContextSupabase';
import { TenantService } from '@/lib/tenant';
import { Settings, Users, Building2, Shield } from 'lucide-react';

export default function TenantSettingsPage() {
  const permissions = usePermissions();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  // Verificar permisos de acceso
  if (!permissions.isTenantAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Shield className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-destructive mb-2">Acceso Denegado</h2>
          <p className="text-muted-foreground">No tienes permisos para acceder a la configuración del tenant.</p>
          <p className="text-sm text-muted-foreground mt-2">Rol actual: {user?.role || 'No definido'}</p>
          <p className="text-sm text-muted-foreground">Se requiere: Admin de tenant</p>
        </div>
      </div>
    );
  }

  const tenantInfo = TenantService.getCurrentUserTenantInfo();

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Settings className="h-6 w-6" />
        <h1 className="text-3xl font-bold">Configuración del Tenant</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Información del Tenant */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Información del Tenant
            </CardTitle>
            <CardDescription>
              Configuración básica de tu organización
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="tenantId">ID del Tenant</Label>
              <Input
                id="tenantId"
                value={tenantInfo.tenantId || 'No asignado'}
                disabled
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="userRole">Tu Rol</Label>
              <div className="mt-1 flex items-center gap-2">
                <Badge variant={user?.role === 'Admin' ? 'default' : 'secondary'}>
                  {user?.role === 'Admin' ? 'Admin de Tenant' : user?.role}
                </Badge>
              </div>
            </div>

            <div>
              <Label htmlFor="permissions">Permisos</Label>
              <div className="mt-1 space-y-2">
                <div className="flex items-center gap-2">
                  <Switch checked={permissions.canManageUsersInOwnTenant()} disabled />
                  <span className="text-sm">Gestionar usuarios del tenant</span>
                </div>
                <div className="flex items-center gap-2">
                  <Switch checked={permissions.canManageOwnTenant()} disabled />
                  <span className="text-sm">Gestionar configuración del tenant</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Gestión de Usuarios */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Gestión de Usuarios
            </CardTitle>
            <CardDescription>
              Administra los usuarios de tu tenant
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className="w-full"
              onClick={() => window.location.href = '/tenant/users'}
            >
              <Users className="h-4 w-4 mr-2" />
              Gestionar Usuarios del Tenant
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Información del Sistema de Permisos */}
      <Card>
        <CardHeader>
          <CardTitle>Sistema de Permisos</CardTitle>
          <CardDescription>
            Información sobre el sistema de roles implementado
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 border rounded-lg">
              <Shield className="h-8 w-8 mx-auto mb-2 text-primary" />
              <h3 className="font-semibold">Super Admin</h3>
              <p className="text-sm text-muted-foreground">Control total del sistema</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <Building2 className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <h3 className="font-semibold">Admin de Tenant</h3>
              <p className="text-sm text-muted-foreground">Gestiona su propio tenant</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <Users className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <h3 className="font-semibold">Usuario de Tenant</h3>
              <p className="text-sm text-muted-foreground">Usa funcionalidades básicas</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}