"""
Simplified Browser Automation API Server
Pure API without UI components - designed for Docker deployment
"""
from __future__ import annotations

import asyncio
import json
import logging
import os
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from browser_use import Browser, Agent
from llm_providers import get_llm_provider
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models for API
class SessionCreateRequest(BaseModel):
    headless: bool = True
    keep_alive: bool = True

class SessionResponse(BaseModel):
    status: str
    message: str
    browser_id: Optional[str] = None
    current_url: Optional[str] = None

class RecordingResponse(BaseModel):
    status: str
    message: str

class StepResponse(BaseModel):
    status: str
    steps: List[Dict]

class ChatMessageRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    status: str
    response: Optional[str] = None

class TestExecutionRequest(BaseModel):
    instructions: str
    user_story: str
    url: str
    max_steps: int = 8

class TestExecutionResponse(BaseModel):
    status: str
    execution_id: str
    message: str

class BrowserAutomationAPI:
    def __init__(self):
        self.app = FastAPI(
            title="Browser Automation API",
            description="Simplified API for browser automation with AI",
            version="1.0.0"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Browser and agent instances
        self.browser: Browser | None = None
        self.agent: Agent | None = None
        self.recording_active: bool = False
        self.recorded_steps: List[Dict] = []
        self.step_counter: int = 0
        self.active_executions: Dict[str, Dict] = {}
        
        # Setup routes
        self._setup_routes()

    def _setup_routes(self):
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "service": "browser-automation-api"}

        @self.app.post("/api/sessions/create", response_model=SessionResponse)
        async def create_session(request: SessionCreateRequest):
            try:
                if self.browser:
                    # Test if existing browser is still working
                    try:
                        cdp_session = await self.browser.get_or_create_cdp_session()
                        if cdp_session:
                            return SessionResponse(
                                status="success",
                                message="Using existing browser session",
                                browser_id=getattr(self.browser, 'id', 'shared'),
                                current_url="shared_session"
                            )
                    except Exception as e:
                        logger.warning(f"Existing browser session unhealthy: {e}")
                        await self._cleanup_browser()

                # Create new browser session
                temp_user_data_dir = tempfile.mkdtemp()
                self.browser = Browser(
                    cdp_url="ws://steel-browser:3000",  # Docker service name
                    user_data_dir=temp_user_data_dir,
                    headless=request.headless,
                    keep_alive=request.keep_alive
                )
                await self.browser.start()
                
                return SessionResponse(
                    status="success",
                    message="Browser session created successfully",
                    browser_id=self.browser.id,
                    current_url="about:blank"
                )
            except Exception as e:
                logger.error(f"Failed to create browser session: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/sessions/stop", response_model=SessionResponse)
        async def stop_session():
            try:
                await self._cleanup_browser()
                self.recorded_steps = []
                self.step_counter = 0
                self.recording_active = False
                
                return SessionResponse(
                    status="success",
                    message="Browser session stopped successfully"
                )
            except Exception as e:
                logger.error(f"Failed to stop session: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/recording/start", response_model=RecordingResponse)
        async def start_recording():
            if not self.browser:
                raise HTTPException(status_code=400, detail="Browser session not active")
            
            self.recorded_steps = []
            self.step_counter = 0
            self.recording_active = True
            
            return RecordingResponse(
                status="success",
                message="Recording started"
            )

        @self.app.post("/api/recording/stop", response_model=RecordingResponse)
        async def stop_recording():
            if not self.browser:
                raise HTTPException(status_code=400, detail="Browser session not active")
            
            self.recording_active = False
            
            return RecordingResponse(
                status="success",
                message="Recording stopped"
            )

        @self.app.get("/api/steps", response_model=StepResponse)
        async def get_steps():
            return StepResponse(
                status="success",
                steps=self.recorded_steps
            )

        @self.app.post("/api/execute-test", response_model=TestExecutionResponse)
        async def execute_test(request: TestExecutionRequest):
            try:
                # Ensure browser session exists
                if not self.browser:
                    await self._ensure_browser_session()
                
                # Create execution ID
                import uuid
                execution_id = str(uuid.uuid4())
                
                # Start recording
                self.recorded_steps = []
                self.step_counter = 0
                
                # Initialize LLM provider
                llm_provider = get_llm_provider()
                
                # Create agent with selected LLM provider
                self.agent = Agent(
                    browser=self.browser,
                    llm_provider=llm_provider,
                    max_steps=request.max_steps
                )
                self.recording_active = True
                
                # Create task message for the agent
                task_message = f"""Execute this test case:
URL: {request.url}
User Story: {request.user_story}
Instructions: {request.instructions}

Please navigate to the URL and follow the instructions to complete the test case."""
                
                # Store execution info
                self.active_executions[execution_id] = {
                    "status": "running",
                    "request": request.dict(),
                    "task_message": task_message,
                    "steps": []
                }
                
                # Execute in background
                asyncio.create_task(self._execute_test_background(execution_id, task_message, request.max_steps))
                
                return TestExecutionResponse(
                    status="success",
                    execution_id=execution_id,
                    message="Test execution started"
                )
                
            except Exception as e:
                logger.error(f"Failed to execute test: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/executions/{execution_id}")
        async def get_execution_status(execution_id: str):
            if execution_id not in self.active_executions:
                raise HTTPException(status_code=404, detail="Execution not found")
            
            execution = self.active_executions[execution_id]
            return {
                "execution_id": execution_id,
                "status": execution["status"],
                "steps": self.recorded_steps if execution["status"] == "running" else execution.get("final_steps", []),
                "result": execution.get("result"),
                "error": execution.get("error")
            }

    async def _ensure_browser_session(self):
        """Ensure browser session is healthy, recreate if necessary."""
        try:
            if self.browser:
                try:
                    cdp_session = await self.browser.get_or_create_cdp_session()
                    if cdp_session and cdp_session.cdp_client:
                        await cdp_session.cdp_client.send.Page.enable(session_id=cdp_session.session_id)
                        return
                except Exception as e:
                    logger.warning(f"Browser session unhealthy: {e}")
                    await self._cleanup_browser()

            # Create new browser session
            temp_user_data_dir = tempfile.mkdtemp()
            self.browser = Browser(
                cdp_url="ws://steel-browser:3000",
                user_data_dir=temp_user_data_dir,
                headless=True,
                keep_alive=True
            )
            await self.browser.start()
            
        except Exception as e:
            logger.error(f"Failed to ensure browser session: {e}")
            raise RuntimeError(f"Could not establish browser session: {e}")

    async def _cleanup_browser(self):
        """Clean up browser resources."""
        if self.browser:
            try:
                await self.browser.stop()
            except Exception:
                pass
            self.browser = None
        self.agent = None

    async def _execute_test_background(self, execution_id: str, task_message: str, max_steps: int):
        """Execute test in background and update execution status."""
        try:
            # Ensure we have a fresh agent
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise RuntimeError("GEMINI_API_KEY not set in environment")
            
            llm = ChatGoogle(model=os.getenv("GEMINI_MODEL", "gemini-2.5-flash"), api_key=api_key)
            self.agent = Agent(task=task_message, browser_session=self.browser, llm=llm)
            
            # Execute the agent
            result = await self.agent.run(max_steps=max_steps)
            
            # Update execution status
            self.active_executions[execution_id].update({
                "status": "completed",
                "result": str(result) if result else "Task completed",
                "final_steps": self.recorded_steps.copy()
            })
            
            # Stop recording
            self.recording_active = False
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            self.active_executions[execution_id].update({
                "status": "failed",
                "error": str(e),
                "final_steps": self.recorded_steps.copy()
            })
            self.recording_active = False

    def run(self, host: str = "0.0.0.0", port: int = 8080):
        """Run the API server."""
        uvicorn.run(self.app, host=host, port=port)

def main():
    """Main entry point."""
    api = BrowserAutomationAPI()
    api.run()

if __name__ == "__main__":
    main()