"use client";

import type { TestExecutionHistoryData, TestExecutionStepResult } from "@/lib/types";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle2, XCircle, Info } from "lucide-react";
import { formatSafeDate } from '@/lib/date-utils';
import { ExpandableText } from "@/components/ExpandableContent";

interface ResultsTabProps {
  historyData: TestExecutionHistoryData;
}

function StepResultItem({ result, index }: { result: TestExecutionStepResult; index: number }) {
  // First check the explicit success flag, then fallback to checking status
  const success = result.success !== undefined ? result.success : 
                 (result.content && result.content.toLowerCase().includes("status") && result.content.toLowerCase().includes("completed") && 
                  !result.content.toLowerCase().includes("error") && !result.content.toLowerCase().includes("fail"));

  const icon = success === undefined ? <Info className="h-5 w-5 text-blue-500" /> : 
               success ? <CheckCircle2 className="h-5 w-5 text-green-500" /> : 
               <XCircle className="h-5 w-5 text-red-500" />;
  
  return (
    <div className={`mb-3 p-3 border rounded-md bg-card ${success === true ? 'border-green-200' : (success === false ? 'border-red-200' : '')}`}>
      <div className="flex items-center font-medium mb-1">
        {icon}
        <span className="ml-2">Step {result.step}</span>
        {success !== undefined && (
          <span className={`ml-2 text-sm ${success ? 'text-green-500' : 'text-red-500'}`}>
            {success ? 'Passed' : 'Failed'}
          </span>
        )}
      </div>
      <ExpandableText text={result.content || "No content for this step."} maxLength={150} className="text-sm" contentClassName="text-muted-foreground font-mono text-xs whitespace-pre-wrap"/>
    </div>
  );
}

export function ResultsTab({ historyData }: ResultsTabProps) {
  const { metadata, results } = historyData;

  // Verificar el estado real de la ejecución basado en los resultados
  const hasFailedSteps = results?.some(result => result.success === false);
  const allStepsCompleted = results?.every(result => result.success === true);
  
  // Si no hay pasos fallidos y todos los pasos están completados, entonces es exitoso
  const isSuccess = !hasFailedSteps && allStepsCompleted && metadata.success !== false;

  const statusClass = isSuccess ? "status-success" : "status-error";
  const statusText = isSuccess ? "✅ Test completed successfully" : 
                    hasFailedSteps ? "❌ Test failed" : "⚠️ Test incomplete or status unknown";
  const StatusIcon = isSuccess ? CheckCircle2 : (hasFailedSteps ? XCircle : Info);
  const iconColor = isSuccess ? "text-green-500" : (hasFailedSteps ? "text-red-500" : "text-amber-500");


  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="glow-text">Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`p-4 rounded-md flex items-center justify-center text-lg font-semibold ${statusClass}`}>
            <StatusIcon className={`mr-2 h-6 w-6 ${iconColor}`} />
            {statusText}
          </div>
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-muted/50">
              <CardHeader className="pb-2">
                <CardDescription>Start Time</CardDescription>
                <CardTitle className="text-xl">
                  {formatSafeDate(metadata.start_time, 'PPpp')}
                </CardTitle>
              </CardHeader>
            </Card>
            <Card className="bg-muted/50">
              <CardHeader className="pb-2">
                <CardDescription>End Time</CardDescription>
                <CardTitle className="text-xl">
                  {formatSafeDate(metadata.end_time, 'PPpp')}
                </CardTitle>
              </CardHeader>
            </Card>
             <Card className="bg-muted/50 md:col-span-2">
              <CardHeader className="pb-2">
                <CardDescription>Total Steps Executed</CardDescription>
                <CardTitle className="text-xl">
                  {metadata.total_steps}
                </CardTitle>
              </CardHeader>
            </Card>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="glow-text">Executed Steps</CardTitle>
        </CardHeader>
        <CardContent>
          {results && results.length > 0 ? (
            results.map((result, index) => (
              <StepResultItem key={index} result={result} index={index} />
            ))
          ) : (
            <p className="text-muted-foreground">No detailed step results available.</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
