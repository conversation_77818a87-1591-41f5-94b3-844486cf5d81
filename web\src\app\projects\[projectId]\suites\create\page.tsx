"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createSuite } from "@/lib/api";
import type { TestSuiteCreateInput, TestSuiteUpdateInput } from "@/lib/types";
import { SuiteForm } from "@/components/forms/SuiteForm";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function CreateSuitePage() {
  const router = useRouter();
  const params = useParams();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;

  const mutation = useMutation({
    mutationFn: (newSuite: TestSuiteCreateInput) => createSuite(projectId, newSuite),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['suites', projectId] });
      queryClient.invalidateQueries({ queryKey: ['project', projectId] }); // Also invalidate project details if it shows suite counts etc.
      toast({
        title: "Suite Created",
        description: `Suite "${data.name}" has been successfully created.`,
      });
      router.push(`/projects/${projectId}/suites/${data.suite_id}`);
    },
    onError: (error) => {
      toast({
        title: "Error Creating Suite",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: TestSuiteCreateInput) => {
    await mutation.mutateAsync(data);
  };

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Project
        </Link>
      </Button>
      <h1 className="page-header">Create New Suite</h1>
      <SuiteForm 
        onSubmit={handleSubmit as (data: TestSuiteCreateInput | TestSuiteUpdateInput) => Promise<void>} 
        isSubmitting={mutation.isPending}
        submitButtonText="Create Suite"
      />
    </div>
  );
}
