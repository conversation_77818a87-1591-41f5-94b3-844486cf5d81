"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Variable, 
  Plus, 
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Database,
  Layers,
  Save,
  X
} from "lucide-react";
import { ProjectVariable, SuiteVariable, ComputedVariable } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";

interface VariableManagerProps {
  projectId: string;
  suiteId?: string;
  projectVariables: ProjectVariable[];
  suiteVariables?: SuiteVariable[];
  onProjectVariableCreate: (variable: Omit<ProjectVariable, 'var_id' | 'project_id' | 'created_at' | 'updated_at'>) => Promise<void>;
  onProjectVariableUpdate: (varId: string, variable: Partial<ProjectVariable>) => Promise<void>;
  onProjectVariableDelete: (varId: string) => Promise<void>;
  onSuiteVariableCreate?: (variable: Omit<SuiteVariable, 'var_id' | 'suite_id' | 'project_id' | 'created_at' | 'updated_at'>) => Promise<void>;
  onSuiteVariableUpdate?: (varId: string, variable: Partial<SuiteVariable>) => Promise<void>;
  onSuiteVariableDelete?: (varId: string) => Promise<void>;
}

interface VariableFormData {
  name: string;
  value: string;
  description: string;
  is_secret: boolean;
  overrides_project?: boolean;
  tags: string[];
}

export function VariableManager({
  projectId,
  suiteId,
  projectVariables,
  suiteVariables = [],
  onProjectVariableCreate,
  onProjectVariableUpdate,
  onProjectVariableDelete,
  onSuiteVariableCreate,
  onSuiteVariableUpdate,
  onSuiteVariableDelete
}: VariableManagerProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingVariable, setEditingVariable] = useState<(ProjectVariable | SuiteVariable) | null>(null);
  const [variableScope, setVariableScope] = useState<'project' | 'suite'>('project');
  const [showSecrets, setShowSecrets] = useState(false);
  const [formData, setFormData] = useState<VariableFormData>({
    name: '',
    value: '',
    description: '',
    is_secret: false,
    overrides_project: false,
    tags: []
  });

  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      name: '',
      value: '',
      description: '',
      is_secret: false,
      overrides_project: false,
      tags: []
    });
    setEditingVariable(null);
  };

  const handleCreate = async () => {
    if (!formData.name.trim() || !formData.value.trim()) {
      toast({
        title: "Error",
        description: "Name and value are required",
        variant: "destructive"
      });
      return;
    }

    try {
      if (variableScope === 'project') {
        await onProjectVariableCreate({
          name: formData.name.trim(),
          value: formData.value.trim(),
          description: formData.description.trim(),
          is_secret: formData.is_secret,
          tags: formData.tags
        });
      } else if (suiteId && onSuiteVariableCreate) {
        await onSuiteVariableCreate({
          name: formData.name.trim(),
          value: formData.value.trim(),
          description: formData.description.trim(),
          is_secret: formData.is_secret,
          overrides_project: formData.overrides_project,
          tags: formData.tags
        });
      }

      toast({
        title: "Variable created",
        description: `${formData.name} has been created successfully`
      });

      resetForm();
      setIsCreateDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create variable",
        variant: "destructive"
      });
    }
  };

  const handleEdit = (variable: ProjectVariable | SuiteVariable) => {
    setEditingVariable(variable);
    setFormData({
      name: variable.name,
      value: variable.value,
      description: variable.description || '',
      is_secret: variable.is_secret,
      overrides_project: 'overrides_project' in variable ? variable.overrides_project || false : false,
      tags: variable.tags
    });
    setVariableScope('suite_id' in variable ? 'suite' : 'project');
    setIsCreateDialogOpen(true);
  };

  const handleUpdate = async () => {
    if (!editingVariable || !formData.name.trim() || !formData.value.trim()) {
      toast({
        title: "Error",
        description: "Name and value are required",
        variant: "destructive"
      });
      return;
    }

    try {
      if ('suite_id' in editingVariable && onSuiteVariableUpdate) {
        await onSuiteVariableUpdate(editingVariable.var_id, {
          name: formData.name.trim(),
          value: formData.value.trim(),
          description: formData.description.trim(),
          is_secret: formData.is_secret,
          overrides_project: formData.overrides_project,
          tags: formData.tags
        });
      } else {
        await onProjectVariableUpdate(editingVariable.var_id, {
          name: formData.name.trim(),
          value: formData.value.trim(),
          description: formData.description.trim(),
          is_secret: formData.is_secret,
          tags: formData.tags
        });
      }

      toast({
        title: "Variable updated",
        description: `${formData.name} has been updated successfully`
      });

      resetForm();
      setIsCreateDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update variable",
        variant: "destructive"
      });
    }
  };

  const handleDelete = async (variable: ProjectVariable | SuiteVariable) => {
    try {
      if ('suite_id' in variable && onSuiteVariableDelete) {
        await onSuiteVariableDelete(variable.var_id);
      } else {
        await onProjectVariableDelete(variable.var_id);
      }

      toast({
        title: "Variable deleted",
        description: `${variable.name} has been deleted successfully`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete variable",
        variant: "destructive"
      });
    }
  };

  const getProjectOverriddenNames = () => {
    return suiteVariables
      .filter(v => v.overrides_project)
      .map(v => v.name);
  };

  const overriddenNames = getProjectOverriddenNames();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Variable Management</h2>
          <p className="text-muted-foreground">
            Manage environment variables for your {suiteId ? 'suite and project' : 'project'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSecrets(!showSecrets)}
          >
            {showSecrets ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {showSecrets ? 'Hide' : 'Show'} Secrets
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Variable
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingVariable ? 'Edit Variable' : 'Create New Variable'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                {!editingVariable && suiteId && (
                  <div>
                    <label className="text-sm font-medium">Scope</label>
                    <Tabs value={variableScope} onValueChange={(value) => setVariableScope(value as 'project' | 'suite')}>
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="project" className="flex items-center gap-2">
                          <Database className="h-4 w-4" />
                          Project
                        </TabsTrigger>
                        <TabsTrigger value="suite" className="flex items-center gap-2">
                          <Layers className="h-4 w-4" />
                          Suite
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium">Name</label>
                  <Input
                    placeholder="e.g., BASE_URL"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Value</label>
                  <Input
                    type={formData.is_secret ? "password" : "text"}
                    placeholder="Variable value"
                    value={formData.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Description (optional)</label>
                  <Textarea
                    placeholder="Describe what this variable is used for"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={2}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Secret Variable</label>
                  <Switch
                    checked={formData.is_secret}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_secret: checked }))}
                  />
                </div>

                {variableScope === 'suite' && (
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Override Project Variable</label>
                    <Switch
                      checked={formData.overrides_project}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, overrides_project: checked }))}
                    />
                  </div>
                )}

                <div className="flex gap-2 pt-4">
                  <Button onClick={editingVariable ? handleUpdate : handleCreate} className="flex-1">
                    <Save className="h-4 w-4 mr-2" />
                    {editingVariable ? 'Update' : 'Create'}
                  </Button>
                  <Button variant="outline" onClick={() => {
                    resetForm();
                    setIsCreateDialogOpen(false);
                  }}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="project" className="w-full">
        <TabsList>
          <TabsTrigger value="project" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Project Variables ({projectVariables.length})
          </TabsTrigger>
          {suiteId && (
            <TabsTrigger value="suite" className="flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Suite Variables ({suiteVariables.length})
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="project" className="space-y-4">
          {projectVariables.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <Variable className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-muted-foreground">No project variables defined</p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {projectVariables.map((variable) => (
                <Card key={variable.var_id}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
                            {`{${variable.name}}`}
                          </code>
                          {variable.is_secret && (
                            <Badge variant="secondary">Secret</Badge>
                          )}
                          {overriddenNames.includes(variable.name) && (
                            <Badge variant="outline" className="text-orange-600">
                              Overridden in Suite
                            </Badge>
                          )}
                        </div>
                        {variable.description && (
                          <p className="text-sm text-muted-foreground mb-2">
                            {variable.description}
                          </p>
                        )}
                        {!variable.is_secret && showSecrets && (
                          <p className="text-sm text-green-600 font-mono">
                            Value: {variable.value}
                          </p>
                        )}
                        {variable.is_secret && (
                          <p className="text-sm text-muted-foreground">
                            {showSecrets ? `Value: ${variable.value}` : 'Value: ***'}
                          </p>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(variable)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(variable)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {suiteId && (
          <TabsContent value="suite" className="space-y-4">
            {suiteVariables.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <Variable className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-muted-foreground">No suite variables defined</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Suite variables can override project variables or add new ones
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4">
                {suiteVariables.map((variable) => (
                  <Card key={variable.var_id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
                              {`{${variable.name}}`}
                            </code>
                            {variable.is_secret && (
                              <Badge variant="secondary">Secret</Badge>
                            )}
                            {variable.overrides_project && (
                              <Badge variant="outline" className="text-purple-600">
                                Overrides Project
                              </Badge>
                            )}
                          </div>
                          {variable.description && (
                            <p className="text-sm text-muted-foreground mb-2">
                              {variable.description}
                            </p>
                          )}
                          {!variable.is_secret && showSecrets && (
                            <p className="text-sm text-green-600 font-mono">
                              Value: {variable.value}
                            </p>
                          )}
                          {variable.is_secret && (
                            <p className="text-sm text-muted-foreground">
                              {showSecrets ? `Value: ${variable.value}` : 'Value: ***'}
                            </p>
                          )}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(variable)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(variable)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}