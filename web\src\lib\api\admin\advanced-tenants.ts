/**
 * Advanced Tenant Management Functions
 * 
 * Functions for comprehensive tenant administration including
 * advanced configurations, bulk operations, and tenant analytics.
 * Extends basic tenant operations with enterprise features.
 * 
 * @module AdvancedTenantAPI
 */

import { fetchApi } from '../core/fetch';

// ============================================================================
// ADVANCED TENANT TYPES
// ============================================================================

export type TenantConfiguration = {
  tenantId: string;
  settings: {
    maxProjects?: number;
    maxUsers?: number;
    maxExecutionsPerMonth?: number;
    allowedFrameworks?: string[];
    storageQuotaGB?: number;
    retentionDays?: number;
    customDomain?: string;
    ssoEnabled?: boolean;
    apiRateLimit?: number;
  };
  features: {
    aiGeneration?: boolean;
    analytics?: boolean;
    integrations?: boolean;
    customReports?: boolean;
    priority?: 'basic' | 'premium' | 'enterprise';
  };
  billing?: {
    plan: string;
    monthlyLimit?: number;
    overageRate?: number;
    billingContact?: string;
  };
};

export type TenantUsage = {
  tenantId: string;
  period: {
    start: string;
    end: string;
  };
  usage: {
    projects: number;
    users: number;
    executions: number;
    storageUsedGB: number;
    apiCalls: number;
  };
  limits: {
    maxProjects: number;
    maxUsers: number;
    maxExecutions: number;
    storageQuotaGB: number;
    apiRateLimit: number;
  };
  utilization: {
    projectsPercent: number;
    usersPercent: number;
    executionsPercent: number;
    storagePercent: number;
  };
};

export type TenantMigration = {
  migrationId: string;
  sourceTenantId: string;
  targetTenantId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  itemsToMigrate: {
    projects: boolean;
    users: boolean;
    executions: boolean;
    configurations: boolean;
  };
  progress?: {
    total: number;
    completed: number;
    failed: number;
    currentItem?: string;
  };
  startedAt?: string;
  completedAt?: string;
  errors?: string[];
};

// ============================================================================
// TENANT CONFIGURATION FUNCTIONS
// ============================================================================

/**
 * Get detailed tenant configuration including limits and features.
 * @param tenantId - Tenant identifier
 * @returns Promise<TenantConfiguration> Tenant configuration
 * @example
 * ```typescript
 * const config = await getTenantConfiguration('tenant-123');
 * console.log(`Max projects: ${config.settings.maxProjects}`);
 * console.log(`AI enabled: ${config.features.aiGeneration}`);
 * ```
 */
export const getTenantConfiguration = (tenantId: string): Promise<TenantConfiguration> => {
  return fetchApi<TenantConfiguration>(`/tenants/${tenantId}/configuration`);
};

/**
 * Update tenant configuration with new settings and limits.
 * @param tenantId - Tenant identifier
 * @param configuration - New configuration settings
 * @returns Promise<TenantConfiguration> Updated configuration
 */
export const updateTenantConfiguration = (
  tenantId: string,
  configuration: Partial<TenantConfiguration>
): Promise<TenantConfiguration> => {
  return fetchApi<TenantConfiguration>(`/tenants/${tenantId}/configuration`, {
    method: 'PUT',
    body: JSON.stringify(configuration),
  });
};

/**
 * Reset tenant configuration to default values.
 * @param tenantId - Tenant identifier
 * @param keepBilling - Whether to preserve billing settings
 * @returns Promise<TenantConfiguration> Reset configuration
 */
export const resetTenantConfiguration = (
  tenantId: string,
  keepBilling: boolean = true
): Promise<TenantConfiguration> => {
  return fetchApi<TenantConfiguration>(`/tenants/${tenantId}/configuration/reset`, {
    method: 'POST',
    body: JSON.stringify({ keepBilling }),
  });
};

// ============================================================================
// TENANT USAGE AND ANALYTICS
// ============================================================================

/**
 * Get detailed tenant usage statistics and quota utilization.
 * @param tenantId - Tenant identifier
 * @param period - Optional time period (defaults to current month)
 * @returns Promise<TenantUsage> Usage statistics
 */
export const getTenantUsage = (
  tenantId: string,
  period?: { start: string; end: string }
): Promise<TenantUsage> => {
  const params = new URLSearchParams();
  if (period) {
    params.append('start', period.start);
    params.append('end', period.end);
  }
  const query = params.toString();
  return fetchApi<TenantUsage>(`/tenants/${tenantId}/usage${query ? `?${query}` : ''}`);
};

/**
 * Get usage trends for multiple tenants.
 * Useful for system-wide analytics and capacity planning.
 * @param params - Filter and period parameters
 * @returns Promise<TenantUsage[]> Usage data for multiple tenants
 */
export const getMultiTenantUsage = (params?: {
  tenantIds?: string[];
  period?: { start: string; end: string };
  includeBilling?: boolean;
}): Promise<TenantUsage[]> => {
  const query = new URLSearchParams();
  if (params?.tenantIds) {
    params.tenantIds.forEach(id => query.append('tenant_id', id));
  }
  if (params?.period) {
    query.append('start', params.period.start);
    query.append('end', params.period.end);
  }
  if (params?.includeBilling) {
    query.append('include_billing', 'true');
  }
  const qs = query.toString();
  return fetchApi<TenantUsage[]>(`/tenants/usage${qs ? `?${qs}` : ''}`);
};

/**
 * Generate usage report for a tenant.
 * Creates detailed PDF or CSV report with usage analytics.
 * @param tenantId - Tenant identifier
 * @param format - Report format
 * @param period - Time period for the report
 * @returns Promise<Blob> Generated report file
 */
export const generateTenantUsageReport = (
  tenantId: string,
  format: 'pdf' | 'csv',
  period?: { start: string; end: string }
): Promise<Blob> => {
  const params = new URLSearchParams({ format });
  if (period) {
    params.append('start', period.start);
    params.append('end', period.end);
  }
  // Note: This endpoint returns a blob, handle response appropriately
  return fetchApi<Blob>(`/tenants/${tenantId}/usage/report?${params.toString()}`);
};

// ============================================================================
// TENANT MIGRATION FUNCTIONS
// ============================================================================

/**
 * Start migration of data between tenants.
 * Useful for tenant consolidation or data reorganization.
 * @param migration - Migration configuration
 * @returns Promise<TenantMigration> Migration status
 */
export const startTenantMigration = (migration: {
  sourceTenantId: string;
  targetTenantId: string;
  itemsToMigrate: {
    projects: boolean;
    users: boolean;
    executions: boolean;
    configurations: boolean;
  };
  options?: {
    preserveIds?: boolean;
    skipExisting?: boolean;
    validateBeforeMigration?: boolean;
  };
}): Promise<TenantMigration> => {
  return fetchApi<TenantMigration>('/tenants/migration/start', {
    method: 'POST',
    body: JSON.stringify(migration),
  });
};

/**
 * Get status of an ongoing tenant migration.
 * @param migrationId - Migration identifier
 * @returns Promise<TenantMigration> Migration status
 */
export const getTenantMigrationStatus = (migrationId: string): Promise<TenantMigration> => {
  return fetchApi<TenantMigration>(`/tenants/migration/${migrationId}/status`);
};

/**
 * Cancel an ongoing tenant migration.
 * @param migrationId - Migration identifier
 * @returns Promise<TenantMigration> Migration status after cancellation
 */
export const cancelTenantMigration = (migrationId: string): Promise<TenantMigration> => {
  return fetchApi<TenantMigration>(`/tenants/migration/${migrationId}/cancel`, {
    method: 'POST',
  });
};

/**
 * Get list of all tenant migrations with optional filtering.
 * @param filters - Optional filters for status, tenant, etc.
 * @returns Promise<TenantMigration[]> List of migrations
 */
export const getTenantMigrations = (filters?: {
  status?: 'pending' | 'in_progress' | 'completed' | 'failed';
  tenantId?: string;
  limit?: number;
}): Promise<TenantMigration[]> => {
  const params = new URLSearchParams();
  if (filters?.status) params.append('status', filters.status);
  if (filters?.tenantId) params.append('tenant_id', filters.tenantId);
  if (filters?.limit) params.append('limit', filters.limit.toString());
  const query = params.toString();
  return fetchApi<TenantMigration[]>(`/tenants/migrations${query ? `?${query}` : ''}`);
};

// ============================================================================
// BULK OPERATIONS
// ============================================================================

/**
 * Perform bulk operations on multiple tenants.
 * @param operation - Bulk operation to perform
 * @param tenantIds - List of tenant IDs to operate on
 * @param params - Operation-specific parameters
 * @returns Promise<any> Operation results
 */
export const bulkTenantOperation = (
  operation: 'activate' | 'deactivate' | 'update_config' | 'reset_usage',
  tenantIds: string[],
  params?: any
): Promise<any> => {
  return fetchApi<any>('/tenants/bulk-operation', {
    method: 'POST',
    body: JSON.stringify({
      operation,
      tenantIds,
      params,
    }),
  });
};

/**
 * Get bulk operation status.
 * @param operationId - Bulk operation identifier
 * @returns Promise<any> Operation status
 */
export const getBulkOperationStatus = (operationId: string): Promise<any> => {
  return fetchApi<any>(`/tenants/bulk-operation/${operationId}/status`);
};