import { createClient } from '@/lib/supabase/server'
import { SupabaseAuthServerService } from '@/lib/auth-supabase-server'
import { NextResponse } from 'next/server'
import type { Database } from '@/lib/supabase/database.types'

export abstract class BaseApiHandler {
  protected supabase: any
  protected user: any

  constructor() {}

  protected async initialize() {
    this.user = await SupabaseAuthServerService.getCurrentUserServer()
    if (!this.user) {
      throw new Error('Unauthorized')
    }
    this.supabase = await createClient()
  }

  protected createErrorResponse(message: string, status: number = 500) {
    return NextResponse.json({ error: message }, { status })
  }

  protected createSuccessResponse(data: any, status: number = 200) {
    return NextResponse.json({ success: true, data }, { status })
  }

  protected createListResponse(items: any[], count?: number) {
    return NextResponse.json({
      success: true,
      count: count ?? items.length,
      items
    })
  }

  protected async verifyProjectOwnership(projectId: string): Promise<boolean> {
    const { data, error } = await this.supabase
      .from('projects')
      .select('project_id')
      .eq('project_id', projectId)
      .eq('user_id', this.user.id)
      .single()

    return !error && !!data
  }

  public async handleRequest<T>(
    handler: () => Promise<T>
  ): Promise<NextResponse> {
    try {
      await this.initialize()
      const result = await handler()
      return result as NextResponse
    } catch (error: any) {
      console.error('API Error:', error)
      
      if (error.message === 'Unauthorized') {
        return this.createErrorResponse('Unauthorized', 401)
      }
      
      return this.createErrorResponse(
        error.message || 'Internal server error',
        500
      )
    }
  }
}