"use client";

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { getProjectEnvironments } from '@/lib/api';
import type { Environment } from '@/lib/types';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Globe, CheckCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CompactEnvironmentSelectorProps {
  projectId: string;
  selectedEnvId?: string;
  onSelect: (envId: string) => void;
  className?: string;
  disabled?: boolean;
  variant?: 'default' | 'minimal';
}

export function CompactEnvironmentSelector({
  projectId,
  selectedEnvId,
  onSelect,
  className,
  disabled = false,
  variant = 'default'
}: CompactEnvironmentSelectorProps) {
  const { data: environments, isLoading } = useQuery({
    queryKey: ['environments', projectId],
    queryFn: () => getProjectEnvironments(projectId),
    enabled: !!projectId,
  });

   // Auto-select default environment if none selected
   React.useEffect(() => {
     if (environments && environments.length > 0 && !selectedEnvId) {
       const defaultEnv = environments.find(env => env.is_default);
       if (defaultEnv) {
         onSelect(defaultEnv.env_id);
       } else if (environments.length === 1) {
         onSelect(environments[0].env_id);
       }
     }
   }, [environments, selectedEnvId, onSelect]);

  if (isLoading) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
        <span className="text-sm text-muted-foreground">Loading...</span>
      </div>
    );
  }

  if (!environments || environments.length === 0) {
    return (
      <div className={cn("flex items-center gap-2 text-muted-foreground", className)}>
        <Globe className="h-4 w-4" />
        <span className="text-sm">No environments</span>
      </div>
    );
  }

  const selectedEnvironment = environments.find(env => env.env_id === selectedEnvId);
  
  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Select
          value={selectedEnvId || ''}
          onValueChange={onSelect}
          disabled={disabled || environments.length <= 1}
        >
          <SelectTrigger className="w-auto min-w-32 h-8 border-0 bg-transparent text-sm hover:bg-accent/50">
            <SelectValue>
              {selectedEnvironment ? (
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    selectedEnvironment.is_default ? "bg-blue-500" : "bg-gray-500"
                  )} />
                  <span>{selectedEnvironment.name}</span>
                  {selectedEnvironment.is_default && (
                    <CheckCircle className="h-3 w-3 text-blue-500" />
                  )}
                </div>
              ) : (
                <span className="text-muted-foreground">Select env</span>
              )}
            </SelectValue>
          </SelectTrigger>
          
          <SelectContent className="min-w-48">
            {environments.map((env) => (
              <SelectItem key={env.env_id} value={env.env_id}>
                <div className="flex items-center gap-2 w-full">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    env.is_default ? "bg-blue-500" : "bg-gray-500"
                  )} />
                  <span className="flex-1">{env.name}</span>
                  {env.is_default && (
                    <CheckCircle className="h-3 w-3 text-blue-500" />
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      <Label className="text-sm font-medium flex items-center gap-2">
        <Globe className="h-4 w-4" />
        Environment
      </Label>
      
      <Select
        value={selectedEnvId || ''}
        onValueChange={onSelect}
        disabled={disabled || environments.length <= 1}
      >
        <SelectTrigger className={cn(
          "w-full h-10",
          selectedEnvironment?.is_default && "border-blue-500/50 bg-blue-500/5"
        )}>
          <SelectValue>
            {selectedEnvironment ? (
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  selectedEnvironment.is_default ? "bg-blue-500" : "bg-gray-500"
                )} />
                <span>{selectedEnvironment.name}</span>
                {selectedEnvironment.is_default && (
                  <Badge variant="secondary" className="text-xs ml-auto">
                    Default
                  </Badge>
                )}
              </div>
            ) : (
              <span className="text-muted-foreground">Select environment</span>
            )}
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent className="max-w-sm">
          {environments.map((env) => (
            <SelectItem key={env.env_id} value={env.env_id}>
              <div className="flex items-center gap-2 w-full">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  env.is_default ? "bg-blue-500" : "bg-gray-500"
                )} />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{env.name}</span>
                    {env.is_default && (
                      <Badge variant="secondary" className="text-xs">
                        Default
                      </Badge>
                    )}
                  </div>
                  {env.base_url && (
                    <div className="text-xs text-muted-foreground truncate">
                      {env.base_url}
                    </div>
                  )}
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {environments.length === 1 && (
        <p className="text-xs text-muted-foreground">
          Only one environment available
        </p>
      )}
    </div>
  );
}

export default CompactEnvironmentSelector;