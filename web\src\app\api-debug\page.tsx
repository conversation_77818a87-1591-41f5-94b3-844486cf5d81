'use client';

import { useState, useEffect } from 'react';
import { AuthService } from '@/lib/auth';
import { API_BASE_URL } from '@/lib/config';

export default function ApiDebugPage() {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [testResults, setTestResults] = useState<any>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = () => {
    const token = AuthService.getToken();
    const user = AuthService.getUser();
    const isAuthenticated = AuthService.isAuthenticated();
    
    setDebugInfo({
      token: token ? `${token.substring(0, 20)}...` : 'No token',
      user: user,
      isAuthenticated,
      apiBaseUrl: API_BASE_URL,
      localStorage: typeof window !== 'undefined' ? {
        tokenExists: !!localStorage.getItem('qak_auth_token'),
        userExists: !!localStorage.getItem('qak_auth_user')
      } : 'Server side'
    });
  };

  const testAuthLogin = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/Auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        }),
      });

      const result = {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: response.ok ? await response.json() : await response.text()
      };

      setTestResults((prev: any) => ({ ...prev, authLogin: result }));
    } catch (error: any) {
      setTestResults((prev: any) => ({ ...prev, authLogin: { error: error.message } }));
    }
    setLoading(false);
  };

  const testProjectsEndpoint = async () => {
    setLoading(true);
    try {
      const headers: any = {
        'Content-Type': 'application/json',
      };

      const token = AuthService.getToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${API_BASE_URL}/api/projects`, {
        method: 'GET',
        headers,
      });

      const result = {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: response.ok ? await response.json() : await response.text()
      };

      setTestResults((prev: any) => ({ ...prev, projects: result }));
    } catch (error: any) {
      setTestResults((prev: any) => ({ ...prev, projects: { error: error.message } }));
    }
    setLoading(false);
  };

  const testHealthEndpoint = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = {
        status: response.status,
        statusText: response.statusText,
        body: response.ok ? await response.json() : await response.text()
      };

      setTestResults((prev: any) => ({ ...prev, health: result }));
    } catch (error: any) {
      setTestResults((prev: any) => ({ ...prev, health: { error: error.message } }));
    }
    setLoading(false);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🔍 API Debug Dashboard</h1>
      
      {/* Auth State */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-3">🔐 Authentication State</h2>
        <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto">
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
        <button 
          onClick={checkAuthState}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh Auth State
        </button>
      </div>

      {/* Test Buttons */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-3">🧪 API Tests</h2>
        <div className="space-x-2">
          <button 
            onClick={testHealthEndpoint}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            Test Health
          </button>
          <button 
            onClick={testAuthLogin}
            disabled={loading}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:opacity-50"
          >
            Test Auth Login
          </button>
          <button 
            onClick={testProjectsEndpoint}
            disabled={loading}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Test Projects
          </button>
        </div>
      </div>

      {/* Test Results */}
      <div className="space-y-4">
        {Object.entries(testResults).map(([test, result]) => (
          <div key={test} className="p-4 border rounded-lg">
            <h3 className="text-lg font-semibold mb-2">📊 {test} Result</h3>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-96">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 p-4 border rounded-lg bg-red-50">
        <h2 className="text-lg font-semibold mb-3">⚠️ Quick Actions</h2>
        <div className="space-x-2">
          <button 
            onClick={() => {
              AuthService.logout();
              checkAuthState();
              setTestResults({});
            }}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Clear Auth & Logout
          </button>
          <button 
            onClick={() => {
              if (typeof window !== 'undefined') {
                localStorage.clear();
                checkAuthState();
                setTestResults({});
              }
            }}
            className="px-4 py-2 bg-red-700 text-white rounded hover:bg-red-800"
          >
            Clear All Storage
          </button>
        </div>
      </div>
    </div>
  );
}