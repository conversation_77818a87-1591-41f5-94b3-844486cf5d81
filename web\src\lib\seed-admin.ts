import { createClient } from '@supabase/supabase-js'

// This script creates the default superadmin user
// It should be run once during initial setup

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || 'http://127.0.0.1:54321'
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('Missing required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY')
}

// Create Supabase client with service role key for admin operations
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

export interface DefaultAdminUser {
  email: string
  password: string
  username: string
  fullName: string
  role: 'SuperAdmin'
}

export const DEFAULT_ADMIN: DefaultAdminUser = {
  email: '<EMAIL>',
  password: 'caca12345',
  username: 'superadmin',
  fullName: 'Super Administrator',
  role: 'SuperAdmin'
}

export async function createDefaultSuperAdmin(): Promise<{ success: boolean; message: string; error?: string }> {
  try {
    console.log('🔧 Creating default superadmin user...')
    
    // Check if user already exists
    const { data: existingUser, error: checkError } = await supabaseAdmin
      .from('profiles')
      .select('id, email, role')
      .eq('email', DEFAULT_ADMIN.email)
      .single()

    if (existingUser && !checkError) {
      console.log('✅ Superadmin user already exists:', existingUser.email)
      
      // Update role to SuperAdmin if it's not already
      if (existingUser.role !== 'SuperAdmin') {
        const { error: updateError } = await supabaseAdmin
          .from('profiles')
          .update({ role: 'SuperAdmin', updated_at: new Date().toISOString() })
          .eq('id', existingUser.id)

        if (updateError) {
          console.error('❌ Error updating user role:', updateError)
          return { success: false, message: 'Failed to update user role', error: updateError.message }
        }
        
        console.log('✅ Updated existing user role to SuperAdmin')
      }
      
      return { success: true, message: 'Superadmin user already exists and is properly configured' }
    }

    // Create new user using Supabase Auth Admin API
    const { data: authUser, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: DEFAULT_ADMIN.email,
      password: DEFAULT_ADMIN.password,
      email_confirm: true,
      user_metadata: {
        username: DEFAULT_ADMIN.username,
        full_name: DEFAULT_ADMIN.fullName
      }
    })

    if (authError) {
      console.error('❌ Error creating auth user:', authError)
      return { success: false, message: 'Failed to create auth user', error: authError.message }
    }

    if (!authUser.user) {
      return { success: false, message: 'No user data returned from auth creation' }
    }

    console.log('✅ Auth user created:', authUser.user.email)

    // The profile should be created automatically by the trigger, but let's verify and update if needed
    await new Promise(resolve => setTimeout(resolve, 1000)) // Wait for trigger to execute

    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', authUser.user.id)
      .single()

    if (profileError || !profile) {
      console.log('⚠️  Profile not found, creating manually...')
      
      // Create profile manually if trigger didn't work
      const { error: insertError } = await supabaseAdmin
        .from('profiles')
        .insert({
          id: authUser.user.id,
          email: DEFAULT_ADMIN.email,
          username: DEFAULT_ADMIN.username,
          full_name: DEFAULT_ADMIN.fullName,
          role: DEFAULT_ADMIN.role,
          email_verified: true
        })

      if (insertError) {
        console.error('❌ Error creating profile:', insertError)
        return { success: false, message: 'Failed to create user profile', error: insertError.message }
      }
    } else {
      // Update existing profile to ensure SuperAdmin role
      const { error: updateError } = await supabaseAdmin
        .from('profiles')
        .update({ 
          role: DEFAULT_ADMIN.role,
          email_verified: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', authUser.user.id)

      if (updateError) {
        console.error('❌ Error updating profile:', updateError)
        return { success: false, message: 'Failed to update user profile', error: updateError.message }
      }
    }

    console.log('✅ Superadmin user created successfully!')
    console.log(`📧 Email: ${DEFAULT_ADMIN.email}`)
    console.log(`🔑 Password: ${DEFAULT_ADMIN.password}`)
    console.log(`👤 Username: ${DEFAULT_ADMIN.username}`)
    console.log(`🛡️  Role: ${DEFAULT_ADMIN.role}`)

    return { 
      success: true, 
      message: `Superadmin user created successfully! Email: ${DEFAULT_ADMIN.email}, Password: ${DEFAULT_ADMIN.password}` 
    }

  } catch (error: any) {
    console.error('❌ Unexpected error:', error)
    return { success: false, message: 'Unexpected error occurred', error: error.message }
  }
}

// Function to run the seed if this file is executed directly
export async function runSeed() {
  const result = await createDefaultSuperAdmin()
  console.log('\n' + '='.repeat(50))
  console.log(result.success ? '✅ SUCCESS' : '❌ FAILED')
  console.log(result.message)
  if (result.error) {
    console.log('Error:', result.error)
  }
  console.log('='.repeat(50))
  return result
}

// Allow running this script directly
if (require.main === module) {
  runSeed().then(() => process.exit(0)).catch(() => process.exit(1))
}