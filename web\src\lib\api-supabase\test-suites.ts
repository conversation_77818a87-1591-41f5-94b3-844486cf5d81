import { BaseApiHandler } from './base'
import type { Database } from '@/lib/supabase/database.types'

type TestSuite = Database['public']['Tables']['test_suites']['Row']
type TestSuiteInsert = Database['public']['Tables']['test_suites']['Insert']
type TestSuiteUpdate = Database['public']['Tables']['test_suites']['Update']

export class TestSuitesApiHandler extends BaseApiHandler {
  async getTestSuites(projectId: string) {
    if (!(await this.verifyProjectOwnership(projectId))) {
      throw new Error('Project not found')
    }

    const { data: suites, error } = await this.supabase
      .from('test_suites')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error('Failed to fetch test suites')
    }

    return this.createListResponse(suites || [])
  }

  async getTestSuite(projectId: string, suiteId: string) {
    const { data: suite, error } = await this.supabase
      .from('test_suites')
      .select(`
        *,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', this.user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Test suite not found')
      }
      throw new Error('Failed to fetch test suite')
    }

    return this.createSuccessResponse(suite)
  }

  async createTestSuite(
    projectId: string, 
    data: { name: string; description: string; type?: string; tags?: string[] }
  ) {
    if (!(await this.verifyProjectOwnership(projectId))) {
      throw new Error('Project not found')
    }

    const { name, description, type = 'manual', tags = [] } = data

    if (!name || !description) {
      throw new Error('Name and description are required')
    }

    const suiteData: TestSuiteInsert = {
      project_id: projectId,
      name,
      description,
      type: type as any,
      tags
    }

    const { data: suite, error } = await this.supabase
      .from('test_suites')
      .insert(suiteData)
      .select('*')
      .single()

    if (error) {
      throw new Error('Failed to create test suite')
    }

    return this.createSuccessResponse(suite, 201)
  }

  async updateTestSuite(
    projectId: string,
    suiteId: string,
    data: { name?: string; description?: string; type?: string; tags?: string[]; execution_times?: number }
  ) {
    // Verify ownership through the suite-project relationship
    const { data: existingSuite, error: checkError } = await this.supabase
      .from('test_suites')
      .select(`
        suite_id,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', this.user.id)
      .single()

    if (checkError || !existingSuite) {
      throw new Error('Test suite not found')
    }

    const updateData: TestSuiteUpdate = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.type !== undefined) updateData.type = data.type as any
    if (data.tags !== undefined) updateData.tags = data.tags
    if (data.execution_times !== undefined) updateData.execution_times = data.execution_times

    const { data: suite, error } = await this.supabase
      .from('test_suites')
      .update(updateData)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .select('*')
      .single()

    if (error) {
      throw new Error('Failed to update test suite')
    }

    return this.createSuccessResponse(suite)
  }

  async deleteTestSuite(projectId: string, suiteId: string) {
    // Verify ownership through the suite-project relationship
    const { data: existingSuite, error: checkError } = await this.supabase
      .from('test_suites')
      .select(`
        suite_id,
        projects!inner (
          project_id,
          user_id
        )
      `)
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)
      .eq('projects.user_id', this.user.id)
      .single()

    if (checkError || !existingSuite) {
      throw new Error('Test suite not found')
    }

    const { error } = await this.supabase
      .from('test_suites')
      .delete()
      .eq('suite_id', suiteId)
      .eq('project_id', projectId)

    if (error) {
      throw new Error('Failed to delete test suite')
    }

    return this.createSuccessResponse({ message: 'Test suite deleted successfully' })
  }
}