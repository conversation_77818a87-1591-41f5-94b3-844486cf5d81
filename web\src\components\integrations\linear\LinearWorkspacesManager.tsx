"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ExternalLinkIcon, 
  RefreshCwIcon, 
  Settings2Icon, 
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  AlertCircleIcon
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  getLinearWorkspaces,
  getLinearAuthUrl,
  deleteLinearWorkspace,
  syncLinearWorkspace,
  updateLinearSyncConfig
} from '@/lib/api/integration';

interface WorkspaceConnection {
  id: string;
  tenantId: string;
  linearWorkspaceId: string;
  workspaceName: string;
  workspaceUrlKey: string;
  isActive: boolean;
  syncConfiguration: {
    autoSync: boolean;
    syncIntervalMinutes: number;
    includeClosedIssues: boolean;
    syncComments: boolean;
    syncAttachments: boolean;
    teamIds: string[];
    projectIds: string[];
    labelFilters: string[];
  };
  lastSyncAt?: string;
  tokenExpiresAt: string;
  createdAt: string;
  updatedAt: string;
}

interface LinearWorkspacesManagerProps {
  tenantId: string;
}

export function LinearWorkspacesManager({ tenantId }: LinearWorkspacesManagerProps) {
  const [workspaces, setWorkspaces] = useState<WorkspaceConnection[]>([]);
  const [loading, setLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [selectedWorkspace, setSelectedWorkspace] = useState<WorkspaceConnection | null>(null);

  useEffect(() => {
    loadWorkspaces();
  }, [tenantId]);

  const loadWorkspaces = async () => {
    try {
      setLoading(true);
      const data = await getLinearWorkspaces(tenantId);
      setWorkspaces(data);
    } catch (error) {
      console.error('Error loading workspaces:', error);
      toast.error('Error loading Linear workspaces');
    } finally {
      setLoading(false);
    }
  };

  const handleConnectWorkspace = async () => {
    try {
      setIsConnecting(true);
      const redirectUri = `${window.location.origin}/integrations/linear/callback`;
      
      const { authorizationUrl } = await getLinearAuthUrl(redirectUri, tenantId);
      window.location.href = authorizationUrl;
    } catch (error) {
      console.error('Error connecting workspace:', error);
      toast.error('Error connecting workspace');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnectWorkspace = async (workspaceId: string) => {
    if (!confirm('Are you sure you want to disconnect this workspace? This will remove all synced documents.')) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/linear/workspaces/${workspaceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-Tenant-ID': tenantId
        }
      });

      if (response.ok) {
        setWorkspaces(prev => prev.filter(w => w.id !== workspaceId));
        toast.success('Workspace disconnected successfully');
      } else {
        toast.error('Error disconnecting workspace');
      }
    } catch (error) {
      console.error('Error disconnecting workspace:', error);
      toast.error('Error disconnecting workspace');
    }
  };

  const handleSyncWorkspace = async (workspaceId: string) => {
    try {
      const response = await fetch(`/api/v1/linear/workspaces/${workspaceId}/sync`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-Tenant-ID': tenantId,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fullSync: false,
          includeClosedIssues: false,
          syncComments: true,
          syncAttachments: false
        })
      });

      if (response.ok) {
        const syncJob = await response.json();
        toast.success(`Sync started. Job ID: ${syncJob.id}`);
        // Optionally, poll for sync status
      } else {
        toast.error('Error starting sync');
      }
    } catch (error) {
      console.error('Error syncing workspace:', error);
      toast.error('Error starting sync');
    }
  };

  const handleUpdateSyncConfig = async (workspaceId: string, config: any) => {
    try {
      const response = await fetch(`/api/v1/linear/workspaces/${workspaceId}/sync-config`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'X-Tenant-ID': tenantId,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        const updatedWorkspace = await response.json();
        setWorkspaces(prev => prev.map(w => w.id === workspaceId ? updatedWorkspace : w));
        toast.success('Sync configuration updated');
      } else {
        toast.error('Error updating sync configuration');
      }
    } catch (error) {
      console.error('Error updating sync config:', error);
      toast.error('Error updating sync configuration');
    }
  };

  const getStatusBadge = (workspace: WorkspaceConnection) => {
    const now = new Date();
    const tokenExpiry = new Date(workspace.tokenExpiresAt);
    
    if (!workspace.isActive) {
      return <Badge variant="secondary" className="flex items-center gap-1">
        <XCircleIcon className="w-3 h-3" />
        Inactive
      </Badge>;
    }

    if (tokenExpiry < now) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <AlertCircleIcon className="w-3 h-3" />
        Token Expired
      </Badge>;
    }

    const hoursUntilExpiry = (tokenExpiry.getTime() - now.getTime()) / (1000 * 60 * 60);
    if (hoursUntilExpiry < 24) {
      return <Badge variant="outline" className="flex items-center gap-1">
        <ClockIcon className="w-3 h-3" />
        Expires Soon
      </Badge>;
    }

    return <Badge variant="default" className="flex items-center gap-1">
      <CheckCircleIcon className="w-3 h-3" />
      Active
    </Badge>;
  };

  const formatLastSync = (lastSyncAt?: string) => {
    if (!lastSyncAt) return 'Never';
    
    const syncDate = new Date(lastSyncAt);
    const now = new Date();
    const diffHours = (now.getTime() - syncDate.getTime()) / (1000 * 60 * 60);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${Math.floor(diffHours)} hours ago`;
    } else {
      return `${Math.floor(diffHours / 24)} days ago`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCwIcon className="w-6 h-6 animate-spin" />
        <span className="ml-2">Loading workspaces...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Linear Workspaces</h2>
          <p className="text-muted-foreground">
            Manage your connected Linear workspaces and synchronization settings
          </p>
        </div>
        <Button 
          onClick={handleConnectWorkspace} 
          disabled={isConnecting}
          className="flex items-center gap-2"
        >
          {isConnecting ? (
            <RefreshCwIcon className="w-4 h-4 animate-spin" />
          ) : (
            <ExternalLinkIcon className="w-4 h-4" />
          )}
          Connect Workspace
        </Button>
      </div>

      <div className="grid gap-4">
        {workspaces.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">No workspaces connected</h3>
                <p className="text-muted-foreground mb-4">
                  Connect your first Linear workspace to start syncing issues and user stories
                </p>
                <Button onClick={handleConnectWorkspace} disabled={isConnecting}>
                  <ExternalLinkIcon className="w-4 h-4 mr-2" />
                  Connect Linear Workspace
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          workspaces.map((workspace) => (
            <Card key={workspace.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {workspace.workspaceName}
                        {getStatusBadge(workspace)}
                      </CardTitle>
                      <CardDescription>
                        {workspace.workspaceUrlKey}.linear.app
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedWorkspace(workspace)}
                    >
                      <Settings2Icon className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSyncWorkspace(workspace.id)}
                    >
                      <RefreshCwIcon className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDisconnectWorkspace(workspace.id)}
                    >
                      <TrashIcon className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <Label className="text-muted-foreground">Last Sync</Label>
                    <div>{formatLastSync(workspace.lastSyncAt)}</div>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Auto Sync</Label>
                    <div>{workspace.syncConfiguration.autoSync ? 'Enabled' : 'Disabled'}</div>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Interval</Label>
                    <div>{workspace.syncConfiguration.syncIntervalMinutes} minutes</div>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Token Expires</Label>
                    <div>{new Date(workspace.tokenExpiresAt).toLocaleDateString()}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Workspace Configuration Modal */}
      {selectedWorkspace && (
        <WorkspaceConfigModal
          workspace={selectedWorkspace}
          onClose={() => setSelectedWorkspace(null)}
          onUpdate={handleUpdateSyncConfig}
        />
      )}
    </div>
  );
}

interface WorkspaceConfigModalProps {
  workspace: WorkspaceConnection;
  onClose: () => void;
  onUpdate: (workspaceId: string, config: any) => void;
}

function WorkspaceConfigModal({ workspace, onClose, onUpdate }: WorkspaceConfigModalProps) {
  const [config, setConfig] = useState(workspace.syncConfiguration);

  const handleSave = () => {
    onUpdate(workspace.id, config);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>Sync Configuration - {workspace.workspaceName}</CardTitle>
          <CardDescription>
            Configure how issues and user stories are synchronized from Linear
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Tabs defaultValue="general">
            <TabsList>
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="filters">Filters</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={config.autoSync}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, autoSync: checked }))}
                />
                <Label>Enable automatic synchronization</Label>
              </div>

              <div className="space-y-2">
                <Label>Sync interval (minutes)</Label>
                <Input
                  type="number"
                  value={config.syncIntervalMinutes}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    syncIntervalMinutes: parseInt(e.target.value) || 60 
                  }))}
                  min="5"
                  max="1440"
                />
              </div>
            </TabsContent>

            <TabsContent value="content" className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch 
                  checked={config.includeClosedIssues}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, includeClosedIssues: checked }))}
                />
                <Label>Include closed issues</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch 
                  checked={config.syncComments}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, syncComments: checked }))}
                />
                <Label>Sync comments</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch 
                  checked={config.syncAttachments}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, syncAttachments: checked }))}
                />
                <Label>Sync attachments</Label>
              </div>
            </TabsContent>

            <TabsContent value="filters" className="space-y-4">
              <div className="space-y-2">
                <Label>Team IDs (comma-separated)</Label>
                <Input
                  value={config.teamIds.join(', ')}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    teamIds: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                  }))}
                  placeholder="team-1, team-2"
                />
              </div>

              <div className="space-y-2">
                <Label>Project IDs (comma-separated)</Label>
                <Input
                  value={config.projectIds.join(', ')}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    projectIds: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                  }))}
                  placeholder="project-1, project-2"
                />
              </div>

              <div className="space-y-2">
                <Label>Label filters (comma-separated)</Label>
                <Input
                  value={config.labelFilters.join(', ')}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    labelFilters: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                  }))}
                  placeholder="bug, feature, urgent"
                />
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              Save Configuration
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}