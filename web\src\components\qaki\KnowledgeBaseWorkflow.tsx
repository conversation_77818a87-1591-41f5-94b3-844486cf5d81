"use client";

import { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ArrowLeft, 
  Upload, 
  File, 
  FileText, 
  FileType, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Database,
  Trash2,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useDropzone } from 'react-dropzone';
import {
  uploadKnowledgeBaseDocument,
  getKnowledgeBaseDocuments,
  generateManualTestsFromKB
} from '@/lib/api/integration';
import { TenantService } from '@/lib/tenant';

interface SelectedProject {
  id: string;
  name: string;
  description?: string;
}

interface KnowledgeBaseWorkflowProps {
  project: SelectedProject;
  onBack: () => void;
}

interface UploadedDocument {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  uploadProgress: number;
  errorMessage?: string;
}

interface ProjectDocument {
  documentId: string;
  documentName: string;
  uploadDate: string;
  size: number;
  contentType: string;
  status: string;
}

const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'text/markdown': ['.md'],
  'text/plain': ['.txt'],
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export function KnowledgeBaseWorkflow({ project, onBack }: KnowledgeBaseWorkflowProps) {
  const [documents, setDocuments] = useState<UploadedDocument[]>([]);
  const [projectDocuments, setProjectDocuments] = useState<ProjectDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [tenantId, setTenantId] = useState<string>('');
  const { toast } = useToast();

  // Inicializar tenantId cuando el componente se monte
  useEffect(() => {
    const userTenantId = TenantService.getCurrentTenantId();
    if (userTenantId) {
      setTenantId(userTenantId);
    } else {
      // Fallback para desarrollo
      setTenantId('default-tenant');
    }
  }, []);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newDocuments: UploadedDocument[] = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(7),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      uploadProgress: 0,
    }));

    setDocuments(prev => [...prev, ...newDocuments]);

    // Upload each file
    newDocuments.forEach(doc => {
      const file = acceptedFiles.find(f => f.name === doc.name);
      if (file) {
        uploadDocument(file, doc.id);
      }
    });
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
    onDropRejected: (fileRejections) => {
      fileRejections.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === 'file-too-large') {
            toast({
              title: "Archivo muy grande",
              description: `El archivo ${file.name} excede el límite de 10MB`,
              variant: "destructive",
            });
          } else if (error.code === 'file-invalid-type') {
            toast({
              title: "Tipo de archivo no soportado",
              description: `El archivo ${file.name} no es un tipo de archivo soportado`,
              variant: "destructive",
            });
          }
        });
      });
    }
  });

  const uploadDocument = async (file: File, documentId: string) => {
    try {
      // Convert file to base64
      const base64Content = await fileToBase64(file);
      
      const requestData = {
        tenantId,
        projectId: project.id,
        documentName: file.name,
        content: base64Content,
        contentType: file.type,
        metadata: {
          originalSize: file.size,
          uploadedAt: new Date().toISOString(),
        }
      };

      // Simulate upload progress
      updateDocumentProgress(documentId, 25);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      updateDocumentProgress(documentId, 50);
      setDocuments(prev => prev.map(doc => 
        doc.id === documentId ? { ...doc, status: 'processing' } : doc
      ));

      // Use centralized API function instead of direct fetch
      const result = await uploadKnowledgeBaseDocument(file, tenantId);

      updateDocumentProgress(documentId, 100);
      setDocuments(prev => prev.map(doc => 
        doc.id === documentId ? { ...doc, status: 'completed' } : doc
      ));

      toast({
        title: "Documento subido",
        description: `${file.name} se ha subido correctamente al Knowledge Base`,
      });

      // Refresh project documents list
      await loadProjectDocuments();

    } catch (error) {
      console.error('Upload error:', error);
      setDocuments(prev => prev.map(doc => 
        doc.id === documentId 
          ? { 
              ...doc, 
              status: 'error', 
              errorMessage: error instanceof Error ? error.message : 'Error desconocido'
            } 
          : doc
      ));

      toast({
        title: "Error en la subida",
        description: `No se pudo subir ${file.name}: ${error instanceof Error ? error.message : 'Error desconocido'}`,
        variant: "destructive",
      });
    }
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data:mime;base64, prefix
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  const updateDocumentProgress = (documentId: string, progress: number) => {
    setDocuments(prev => prev.map(doc => 
      doc.id === documentId ? { ...doc, uploadProgress: progress } : doc
    ));
  };

  const loadProjectDocuments = async () => {
    try {
      setLoading(true);
      const result = await getKnowledgeBaseDocuments(tenantId, project.id);
      setProjectDocuments(result.data?.documents || []);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const removeDocument = (documentId: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== documentId));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return <File className="text-red-500" size={20} />;
    if (type.includes('word') || type.includes('document')) return <FileText className="text-blue-500" size={20} />;
    if (type.includes('text') || type.includes('markdown')) return <FileType className="text-green-500" size={20} />;
    return <File className="text-gray-500" size={20} />;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="text-green-500" size={20} />;
      case 'error':
        return <XCircle className="text-red-500" size={20} />;
      case 'processing':
        return <AlertCircle className="text-yellow-500" size={20} />;
      default:
        return <Upload className="text-blue-500" size={20} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft size={16} />
            </Button>
            <div className="flex items-center gap-2">
              <Database size={20} className="text-blue-500" />
              <CardTitle>Knowledge Base - {project.name}</CardTitle>
            </div>
          </div>
          <CardDescription>
            Sube documentos para alimentar la base de conocimiento y mejorar la calidad de las respuestas de IA
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle>Subir Documentos</CardTitle>
          <CardDescription>
            Arrastra archivos aquí o haz clic para seleccionar. Soporta PDF, Word, Markdown y TXT (máx. 10MB cada uno)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-primary/50'
            }`}
          >
            <input {...getInputProps()} />
            <Upload size={48} className="mx-auto mb-4 text-muted-foreground" />
            {isDragActive ? (
              <p className="text-primary font-medium">Suelta los archivos aquí...</p>
            ) : (
              <div>
                <p className="font-medium mb-2">Arrastra archivos aquí o haz clic para seleccionar</p>
                <p className="text-sm text-muted-foreground">
                  PDF, DOC, DOCX, MD, TXT • Máximo 10MB por archivo
                </p>
              </div>
            )}
          </div>

          {/* Upload Progress */}
          {documents.length > 0 && (
            <div className="mt-6 space-y-3">
              <h4 className="font-medium">Archivos en proceso:</h4>
              {documents.map((doc) => (
                <div key={doc.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  {getFileIcon(doc.type)}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{doc.name}</p>
                    <p className="text-sm text-muted-foreground">{formatFileSize(doc.size)}</p>
                    {doc.status === 'uploading' && (
                      <Progress value={doc.uploadProgress} className="mt-2" />
                    )}
                    {doc.errorMessage && (
                      <Alert className="mt-2 py-2">
                        <AlertCircle size={16} />
                        <AlertDescription className="text-sm">{doc.errorMessage}</AlertDescription>
                      </Alert>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(doc.status)}
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => removeDocument(doc.id)}
                    >
                      <Trash2 size={16} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Existing Documents */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Documentos del Proyecto</CardTitle>
              <CardDescription>
                Documentos ya subidos al Knowledge Base de este proyecto
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={loadProjectDocuments}>
              <Eye size={16} className="mr-2" />
              Actualizar
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-muted animate-pulse rounded-lg" />
              ))}
            </div>
          ) : projectDocuments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No hay documentos subidos aún en este proyecto
            </div>
          ) : (
            <div className="space-y-3">
              {projectDocuments.map((doc) => (
                <div key={doc.documentId} className="flex items-center gap-3 p-3 border rounded-lg">
                  {getFileIcon(doc.contentType)}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{doc.documentName}</p>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span>{formatFileSize(doc.size)}</span>
                      <span>{new Date(doc.uploadDate).toLocaleDateString('es-ES')}</span>
                      <Badge variant={doc.status === 'processed' ? 'default' : 'secondary'} className="text-xs">
                        {doc.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Help Section */}
      <Alert>
        <AlertCircle size={16} />
        <AlertDescription>
          <strong>Tip:</strong> Los documentos subidos se procesan automáticamente y estarán disponibles 
          para consultas de IA en los siguientes flujos de trabajo. La calidad del contexto mejora 
          significativamente la generación de historias de usuario y test cases.
        </AlertDescription>
      </Alert>
    </div>
  );
}