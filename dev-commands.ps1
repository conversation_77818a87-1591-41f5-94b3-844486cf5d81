# Comandos simplificados de desarrollo

function Start-Dev {
    Write-Host "Iniciando desarrollo local con Supabase Cloud..." -ForegroundColor Green
    Set-Location "web"
    npm run dev
    Set-Location ".."
}

function Build-Prod {
    Write-Host "Construyendo para produccion..." -ForegroundColor Yellow
    Set-Location "web"
    npm run build
    Set-Location ".."
}

function Deploy-Docker {
    Write-Host "Desplegando con Docker..." -ForegroundColor Blue
    docker-compose -f docker-compose.prod.yml up -d --build
}

function Test-Supabase {
    Write-Host "Verificando conexion con Supabase Cloud..." -ForegroundColor Cyan
    $env:SUPABASE_URL = "https://qekegvxuykuuokipfple.supabase.co"
    $env:SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFla2Vndnh1eWt1dW9raXBmcGxlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTc5Nzc2NDksImV4cCI6MjA3MzU1MzY0OX0.7sFgaSFtsiwdU4uKWjkXKVNlykK62A7itHGnx81kM-I"
    
    try {
        $response = Invoke-RestMethod -Uri "$env:SUPABASE_URL/rest/v1/" -Headers @{
            "apikey" = $env:SUPABASE_ANON_KEY
            "Authorization" = "Bearer $env:SUPABASE_ANON_KEY"
        }
        Write-Host "Conexion exitosa con Supabase Cloud" -ForegroundColor Green
    }
    catch {
        Write-Host "Error de conexion: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Reset-Dev {
    Write-Host "Limpiando entorno de desarrollo..." -ForegroundColor Magenta
    docker-compose down
    docker system prune -f
    Set-Location "web"
    Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
    Remove-Item -Recurse -Force ".next" -ErrorAction SilentlyContinue
    Remove-Item -Force "package-lock.json" -ErrorAction SilentlyContinue
    npm install
    Set-Location ".."
}

Write-Host "Comandos disponibles:" -ForegroundColor White
Write-Host "  Start-Dev     - Iniciar desarrollo local" -ForegroundColor Gray
Write-Host "  Build-Prod    - Construir para produccion" -ForegroundColor Gray
Write-Host "  Deploy-Docker - Desplegar con Docker" -ForegroundColor Gray
Write-Host "  Test-Supabase - Verificar conexion Supabase" -ForegroundColor Gray
Write-Host "  Reset-Dev     - Limpiar y reiniciar" -ForegroundColor Gray