"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { 
  Variable, 
  Search, 
  Copy,
  Eye,
  EyeOff,
  Database,
  Layers,
  Globe
} from "lucide-react";
import { ComputedVariable } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";

interface VariableSelectorProps {
  variables: ComputedVariable[];
  onVariableInsert: (variable: ComputedVariable) => void;
  className?: string;
}

export function VariableSelector({ variables, onVariableInsert, className }: VariableSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showSecrets, setShowSecrets] = useState(false);
  const { toast } = useToast();

  const filteredVariables = variables.filter(variable => {
    if (!showSecrets && variable.is_secret) return false;
    return variable.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
           (variable.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
  });

  const projectVariables = filteredVariables.filter(v => v.source === 'project');
  const suiteVariables = filteredVariables.filter(v => v.source === 'suite');
  const environmentVariables = filteredVariables.filter(v => v.source === 'environment');

  const handleVariableSelect = (variable: ComputedVariable) => {
    onVariableInsert(variable);
    setOpen(false);
    toast({
      title: "Variable inserted",
      description: `{${variable.name}} added to your step`,
    });
  };

  const copyVariableName = (variableName: string, e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(`{${variableName}}`);
    toast({
      title: "Copied",
      description: `{${variableName}} copied to clipboard`,
    });
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className={`flex items-center gap-2 ${className}`}
        >
          <Variable className="h-4 w-4" />
          Variables ({variables.length})
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-96 p-0" align="start">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Variable className="h-4 w-4" />
                Available Variables
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSecrets(!showSecrets)}
                className="h-8 w-8 p-0"
              >
                {showSecrets ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </CardTitle>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search variables..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0 max-h-80 overflow-y-auto">
            {projectVariables.length > 0 && (
              <div className="px-3 py-2">
                <div className="flex items-center gap-2 mb-2">
                  <Database className="h-4 w-4 text-blue-500" />
                  <span className="text-xs font-medium text-muted-foreground">Project Variables</span>
                </div>
                <div className="space-y-1">
                  {projectVariables.map((variable) => (
                    <div
                      key={`project-${variable.name}`}
                      className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer group"
                      onClick={() => handleVariableSelect(variable)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <code className="text-sm font-mono">{`{${variable.name}}`}</code>
                          {variable.is_secret && (
                            <Badge variant="secondary" className="text-xs">
                              Secret
                            </Badge>
                          )}
                          {variable.is_overridden && (
                            <Badge variant="outline" className="text-xs text-orange-600">
                              Overridden
                            </Badge>
                          )}
                        </div>
                        {variable.description && (
                          <p className="text-xs text-muted-foreground truncate mt-1">
                            {variable.description}
                          </p>
                        )}
                        {!variable.is_secret && (
                          <p className="text-xs text-green-600 font-mono mt-1">
                            Value: {variable.value}
                          </p>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 h-8 w-8 p-0"
                        onClick={(e) => copyVariableName(variable.name, e)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {environmentVariables.length > 0 && (
              <div className="px-3 py-2">
                <div className="flex items-center gap-2 mb-2">
                  <Globe className="h-4 w-4 text-green-500" />
                  <span className="text-xs font-medium text-muted-foreground">Environment Variables</span>
                </div>
                <div className="space-y-1">
                  {environmentVariables.map((variable) => (
                    <div
                      key={`environment-${variable.environment_id}-${variable.name}`}
                      className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer group"
                      onClick={() => handleVariableSelect(variable)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <code className="text-sm font-mono">{`{${variable.name}}`}</code>
                          <Badge variant="outline" className="text-xs text-green-600">
                            {variable.environment_name}
                          </Badge>
                          {variable.is_secret && (
                            <Badge variant="secondary" className="text-xs">
                              Secret
                            </Badge>
                          )}
                        </div>
                        {variable.description && (
                          <p className="text-xs text-muted-foreground truncate mt-1">
                            {variable.description}
                          </p>
                        )}
                        {!variable.is_secret && (
                          <p className="text-xs text-green-600 font-mono mt-1">
                            Value: {variable.value}
                          </p>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 h-8 w-8 p-0"
                        onClick={(e) => copyVariableName(variable.name, e)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {suiteVariables.length > 0 && (
              <div className="px-3 py-2">
                <div className="flex items-center gap-2 mb-2">
                  <Layers className="h-4 w-4 text-purple-500" />
                  <span className="text-xs font-medium text-muted-foreground">Suite Variables</span>
                </div>
                <div className="space-y-1">
                  {suiteVariables.map((variable) => (
                    <div
                      key={`suite-${variable.name}`}
                      className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer group"
                      onClick={() => handleVariableSelect(variable)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <code className="text-sm font-mono">{`{${variable.name}}`}</code>
                          {variable.is_secret && (
                            <Badge variant="secondary" className="text-xs">
                              Secret
                            </Badge>
                          )}
                        </div>
                        {variable.description && (
                          <p className="text-xs text-muted-foreground truncate mt-1">
                            {variable.description}
                          </p>
                        )}
                        {!variable.is_secret && (
                          <p className="text-xs text-green-600 font-mono mt-1">
                            Value: {variable.value}
                          </p>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 h-8 w-8 p-0"
                        onClick={(e) => copyVariableName(variable.name, e)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {filteredVariables.length === 0 && (
              <div className="p-6 text-center text-muted-foreground">
                <Variable className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">No variables found</p>
                {!showSecrets && (
                  <p className="text-xs mt-1">
                    Secret variables are hidden. Click the eye icon to show them.
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
}